
<!-- Thank you for being interested in nvm! Please help us by filling out the following form if you‘re having trouble. If you have a feature request, or some other question, please feel free to clear out the form. Thanks! -->

#### Operating system and version:

#### `nvm debug` output:
<details>
<!-- do not delete the following blank line -->

```sh

```
</details>

#### `nvm ls` output:
<details>
<!-- do not delete the following blank line -->

```sh

```
</details>

#### How did you install `nvm`?
<!-- (e.g. install script in readme, Homebrew) -->

#### What steps did you perform?

#### What happened?

#### What did you expect to happen?

#### Is there anything in any of your profile files that modifies the `PATH`?
<!--  (e.g. `.bashrc`, `.bash_profile`, `.zshrc`, etc) -->

<!-- Please remove the following section if it does not apply to you -->
#### If you are having installation issues, or getting "N/A", what does `curl -I --compressed -v https://nodejs.org/dist/` print out?
<details>
<!-- do not delete the following blank line -->

```sh

```
</details>
