Thanks for contributing to `nvm`!

We love pull requests and issues, they're our favorite.

However, before submitting, please review the following:

For bug reports:

  - Please make sure the bug is reproducible, and give us the steps to reproduce it, so that we can dig into the problem.
  - Please give us as much detail as possible about your environment, so we can more easily confirm the problem.

For pull requests:

  - Please include tests. Changes with tests will be merged very quickly.
  - Please manually confirm that your changes work in `bash`, `sh`/`dash`, `ksh`, and `zsh`. Fast tests do run in these shells, but it's nice to manually verify also.
  - Please maintain consistent whitespace - 2-space indentation, trailing newlines in all files, etc.
  - Any time you make a change to your PR, please rebase freshly on top of master. Nobody likes merge commits.

Even if you don't have all of these items covered, please still feel free to submit a PR/issue! Someone else may be inspired and volunteer to complete it for you.

Thanks again!
