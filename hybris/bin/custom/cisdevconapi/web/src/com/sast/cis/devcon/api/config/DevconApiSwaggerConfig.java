package com.sast.cis.devcon.api.config;

import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Arrays;
import java.util.List;

import static com.sast.cis.devcon.api.constants.CisdevconapiConstants.*;

@EnableSwagger2
@Component
@AllArgsConstructor
public class DevconApiSwaggerConfig {
    private final ConfigurationService configurationService;

    @Bean
    public Docket apiDocumentation() {
        return new Docket(DocumentationType.SWAGGER_2)
            .apiInfo(apiInfo())
            .select()
            .paths(PathSelectors.any())
            .build()
            .securitySchemes(Arrays.asList(clientCredentialFlow(), passwordFlow()))
            .securityContexts(Arrays.asList(oauthSecurityContext()))
            .tags(new Tag("Cisdevconapi", "endpoints to retrieve navigation items"));
    }

    protected ApiInfo apiInfo() {
        return new ApiInfoBuilder()
            .title(getPropertyValue(DOCUMENTATION_TITLE_PROPERTY))
            .description(getPropertyValue(DOCUMENTATION_DESC_PROPERTY))
            .termsOfServiceUrl(getPropertyValue(TERMS_OF_SERVICE_URL_PROPERTY))
            .license(getPropertyValue(LICENSE_PROPERTY))
            .licenseUrl(getPropertyValue(LICENSE_URL_PROPERTY))
            .version(API_VERSION)
            .build();
    }

    protected OAuth clientCredentialFlow() {
        AuthorizationScope authorizationScope = new AuthorizationScope(getPropertyValue(AUTHORIZATION_SCOPE_PROPERTY),
            StringUtils.EMPTY);
        ClientCredentialsGrant clientCredentialsGrant = new ClientCredentialsGrant(AUTHORIZATION_URL);
        return new OAuth(CLIENT_CREDENTIAL_AUTHORIZATION_NAME, Arrays.asList(authorizationScope),
            Arrays.asList(clientCredentialsGrant));
    }

    protected OAuth passwordFlow() {
        AuthorizationScope authorizationScope = new AuthorizationScope(getPropertyValue(AUTHORIZATION_SCOPE_PROPERTY),
            StringUtils.EMPTY);
        ResourceOwnerPasswordCredentialsGrant resourceOwnerPasswordCredentialsGrant = new ResourceOwnerPasswordCredentialsGrant(
            AUTHORIZATION_URL);
        return new OAuth(PASSWORD_AUTHORIZATION_NAME, Arrays.asList(authorizationScope),
            Arrays.asList(resourceOwnerPasswordCredentialsGrant));
    }

    protected SecurityContext oauthSecurityContext() {
        return SecurityContext.builder().securityReferences(oauthSecurityReferences()).forPaths(PathSelectors.any()).build();
    }

    protected String getPropertyValue(String propertyName) {
        return configurationService.getConfiguration().getString(propertyName);
    }

    protected List<SecurityReference> oauthSecurityReferences() {
        AuthorizationScope[] authorizationScopes = {};
        return Arrays.asList(new SecurityReference(PASSWORD_AUTHORIZATION_NAME, authorizationScopes), new SecurityReference(
            CLIENT_CREDENTIAL_AUTHORIZATION_NAME, authorizationScopes));
    }

}