<?xml version="1.0" encoding="UTF-8"?>
<!--
 [y] hybris Platform

 Copyright (c) 2017 SAP SE or an SAP affiliate company.  All rights reserved.

 This software is the confidential and proprietary information of SAP
 ("Confidential Information"). You shall not disclose such Confidential
 Information and shall use it only in accordance with the terms of the
 license agreement you entered into with SAP.
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
			  http://www.springframework.org/schema/context/spring-context.xsd">

	<!-- Field mapping configuration -->
	<bean id="dataMapper"
		class="de.hybris.platform.webservicescommons.mapping.impl.DefaultDataMapper">
		<property name="fieldSetBuilder" ref="fieldSetBuilder" />
	</bean>

	<bean id="fieldSetBuilder"
		class="de.hybris.platform.webservicescommons.mapping.impl.DefaultFieldSetBuilder">
		<property name="defaultRecurrencyLevel" value="4" />
		<property name="defaultMaxFieldSetSize" value="50000" />
		<property name="fieldSetLevelHelper" ref="fieldSetLevelHelper" />
	</bean>

	<alias alias="fieldSetLevelHelper" name="defaultFieldSetLevelHelper" />
	<bean id="defaultFieldSetLevelHelper"
		class="de.hybris.platform.webservicescommons.mapping.impl.DefaultFieldSetLevelHelper">
	</bean>
	
	<bean class="de.hybris.platform.webservicescommons.mapping.filters.GeneralFieldFilter">
		<property name="fieldSelectionStrategy" ref="fieldSelectionStrategy"/>
	</bean>
</beans>