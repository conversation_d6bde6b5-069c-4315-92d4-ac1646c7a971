package com.sast.cis.webservices.exceptionresolvers;

import de.hybris.platform.webservicescommons.resolver.RestHandlerExceptionResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.WebApplicationException;

public class CisRestHandlerExceptionResolver extends RestHandlerExceptionResolver {
    private static final Logger LOG = LoggerFactory.getLogger(CisRestHandlerExceptionResolver.class);

    @Override
    protected ModelAndView doResolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        LOG.info("Handling {} with message={}", ex.getClass(), ex.getMessage());
        if (isWebApplicationException(ex)) {
            return resolveWebApplicationException(request, response, (WebApplicationException) ex);
        }
        if (isHttpMessageNotReadableException(ex)) {
            ex = new HttpMessageNotReadableException("The RequestBody does not conform to interface specifications", ex,
                new ServletServerHttpRequest(request));
        }
        return super.doResolveException(request, response, handler, ex);
    }

    private ModelAndView resolveWebApplicationException(HttpServletRequest request, HttpServletResponse response,
            WebApplicationException ex) {
        LOG.debug("Resolving WebApplicationException: {}", ex.getMessage(),  ex);
        response.setStatus(ex.getResponse().getStatus());

        try (ServletServerHttpResponse outputMessage = new ServletServerHttpResponse(response)) {
            Object entity = ex.getMessage();
            if (ex.getResponse().hasEntity()) {
                entity = ex.getResponse().getEntity();
            }
            return writeWithMessageConverters(entity, new ServletServerHttpRequest(request), outputMessage);
        } catch (Exception exception) {
            LOG.error("Handling of {} resulted in Exception", ex.getClass().getName(), exception);
        }
        return null;
    }

    private boolean isWebApplicationException(Exception exception) {
        return exception instanceof WebApplicationException;
    }

    private boolean isHttpMessageNotReadableException(Exception exception) {
        return exception instanceof HttpMessageNotReadableException;
    }
}
