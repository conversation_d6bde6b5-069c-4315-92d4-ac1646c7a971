package com.sast.cis.payment.pgw.service.selleraccount

import com.sast.cis.core.enums.BillingSystemStatus
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.enums.PspSellerAccountStatus
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.PspSellerAccountModel
import com.sast.cis.core.service.order.OrderSellerProvider
import com.sast.cis.payment.pgw.model.PgwSellerAccountModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.PspSellerAccountBuilder
import generated.com.sast.cis.payment.pgw.model.PgwSellerAccountBuilder
import org.junit.Test

@UnitTest
class PgwSellerAccountProviderUnitSpec extends JUnitPlatformSpecification {
    private OrderSellerProvider orderSellerProvider = Mock()

    private PgwSellerAccountProvider pgwSellerAccountProvider

    private IoTCompanyModel mockSeller = Mock()
    private AbstractOrderModel mockOrder = Mock()

    private PspSellerAccountModel boschTransferSellerAccount
    private PspSellerAccountModel pgwSellerAccountWithWrongType
    private PgwSellerAccountModel inactivePgwSellerAccount
    private PgwSellerAccountModel unsyncedPgwSellerAccount
    private PgwSellerAccountModel activePgwSellerAccount

    def setup() {
        pgwSellerAccountProvider = new PgwSellerAccountProvider(orderSellerProvider)

        boschTransferSellerAccount = PspSellerAccountBuilder.generate().withPaymentProvider(PaymentProvider.BOSCH_TRANSFER)
                .withBillingSystemStatus(BillingSystemStatus.IN_SYNC).withStatus(PspSellerAccountStatus.ACTIVE).buildInstance()
        pgwSellerAccountWithWrongType = PspSellerAccountBuilder.generate().withPaymentProvider(PaymentProvider.PGW)
                .withBillingSystemStatus(BillingSystemStatus.IN_SYNC).withStatus(PspSellerAccountStatus.ACTIVE).buildInstance()
        inactivePgwSellerAccount = PgwSellerAccountBuilder.generate().withPaymentProvider(PaymentProvider.PGW)
                .withBillingSystemStatus(BillingSystemStatus.IN_SYNC).withStatus(PspSellerAccountStatus.DISABLED).buildInstance()
        unsyncedPgwSellerAccount = PgwSellerAccountBuilder.generate().withPaymentProvider(PaymentProvider.PGW)
                .withBillingSystemStatus(BillingSystemStatus.NEW).withStatus(PspSellerAccountStatus.DISABLED).buildInstance()
        activePgwSellerAccount = PgwSellerAccountBuilder.generate().withPaymentProvider(PaymentProvider.PGW)
                .withBillingSystemStatus(BillingSystemStatus.IN_SYNC).withStatus(PspSellerAccountStatus.ACTIVE).buildInstance()

        mockSeller.getPspSellerAccounts() >> [boschTransferSellerAccount, pgwSellerAccountWithWrongType,
                                              inactivePgwSellerAccount, activePgwSellerAccount]
        orderSellerProvider.getSellerForOrderOrThrow(mockOrder) >> mockSeller
    }

    @Test
    void 'getActiveSellerAccountForCompany returns only the active seller account'() {
        when:
        def actualResult = pgwSellerAccountProvider.getActiveSellerAccountForCompany(mockSeller)

        then:
        actualResult == Optional.of(activePgwSellerAccount)
    }

    @Test
    void 'getSellerAccountForOrder returns only the active seller account'() {
        when:
        def actualResult = pgwSellerAccountProvider.getSellerAccountForOrder(mockOrder)

        then:
        actualResult == Optional.of(activePgwSellerAccount)
    }

    @Test
    void 'getActiveSellerAccountForCompany returns empty if there is no active PGW account'() {
        when:
        def actualResult = pgwSellerAccountProvider.getActiveSellerAccountForCompany(mockSeller)

        then:
        mockSeller.getPspSellerAccounts() >> [boschTransferSellerAccount, pgwSellerAccountWithWrongType,
                                              inactivePgwSellerAccount]
        actualResult == Optional.empty()
    }

    @Test
    void 'getSellerAccountForOrder returns empty if there is no active PGW account'() {
        when:
        def actualResult = pgwSellerAccountProvider.getSellerAccountForOrder(mockOrder)

        then:
        mockSeller.getPspSellerAccounts() >> [boschTransferSellerAccount, pgwSellerAccountWithWrongType,
                                              inactivePgwSellerAccount]
        actualResult == Optional.empty()
    }

    @Test
    void 'getActiveSellerAccountForCompany throws IllegalStateException if there are multiple active PGW accounts'() {
        given:
        def anotherActiveAccount = PgwSellerAccountBuilder.generate().withPaymentProvider(PaymentProvider.PGW)
                .withBillingSystemStatus(BillingSystemStatus.IN_SYNC).withStatus(PspSellerAccountStatus.ACTIVE).buildInstance()

        when:
        pgwSellerAccountProvider.getActiveSellerAccountForCompany(mockSeller)

        then:
        mockSeller.getPspSellerAccounts() >> [boschTransferSellerAccount, pgwSellerAccountWithWrongType,
                                              inactivePgwSellerAccount, activePgwSellerAccount, anotherActiveAccount]
        thrown(IllegalStateException)
    }

    @Test
    void 'getActiveSellerAccountForOrder throws IllegalStateException if there are multiple active PGW accounts'() {
        given:
        def anotherActiveAccount = PgwSellerAccountBuilder.generate().withPaymentProvider(PaymentProvider.PGW)
                .withBillingSystemStatus(BillingSystemStatus.IN_SYNC).withStatus(PspSellerAccountStatus.ACTIVE).buildInstance()

        when:
        pgwSellerAccountProvider.getSellerAccountForOrder(mockOrder)

        then:
        mockSeller.getPspSellerAccounts() >> [boschTransferSellerAccount, pgwSellerAccountWithWrongType,
                                              inactivePgwSellerAccount, activePgwSellerAccount, anotherActiveAccount]
        thrown(IllegalStateException)
    }

    @Test
    void 'getSellerAccountForOrder throws IllegalArgumentException if given order is null'() {
        when:
        pgwSellerAccountProvider.getSellerAccountForOrder(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'getActiveSellerAccountForCompany throws IllegalArgumentException if given company is null'() {
        when:
        pgwSellerAccountProvider.getActiveSellerAccountForCompany(null)

        then:
        thrown(IllegalArgumentException)
    }
}