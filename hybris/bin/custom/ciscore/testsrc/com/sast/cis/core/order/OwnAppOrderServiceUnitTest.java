package com.sast.cis.core.order;

import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppLicenseBuilder;
import generated.de.hybris.platform.core.model.order.OrderEntryBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class OwnAppOrderServiceUnitTest {

    @InjectMocks
    private OwnAppOrderService ownAppOrderService;
    @Mock
    private OrderModel order;
    @Mock
    private IoTCompanyModel company;
    @Mock
    private IoTCompanyModel anotherCompany;
    private AppModel app;
    private AppLicenseModel license;
    private AppLicenseModel anotherLicense;
    private OrderEntryModel orderEntryModel;
    private OrderEntryModel anotherOrderEntryModel;

    @Before
    public void setup() {
        app = AppBuilder.generate().withCompany(company).buildMockInstance();
        license = AppLicenseBuilder.generate().withBaseProduct(app).withLicenseType(LicenseType.FULL).buildMockInstance();
        anotherLicense = AppLicenseBuilder.generate().withBaseProduct(app).withLicenseType(LicenseType.EVALUATION).buildMockInstance();
        orderEntryModel = OrderEntryBuilder.generate().withProduct(license).buildMockInstance();
        anotherOrderEntryModel = OrderEntryBuilder.generate().withProduct(anotherLicense).buildMockInstance();
        when(order.getEntries()).thenReturn(List.of(orderEntryModel,anotherOrderEntryModel));
        when(order.getCompany()).thenReturn(anotherCompany);

    }

    @Test
    public void isOwnFullLicenseOrder_noFullLicense_returnFalse(){
        when(order.getEntries()).thenReturn(List.of(anotherOrderEntryModel));

        assertThat(ownAppOrderService.isOwnFullLicenseOrder(order)).isFalse();
    }

    @Test
    public void isOwnFullLicenseOrder_hasFullAndTrialLicense_returnFalse(){
        assertThat(ownAppOrderService.isOwnFullLicenseOrder(order)).isFalse();
    }

    @Test
    public void isOwnFullLicenseOrder_onlyFullButFromDifferentCompany_returnFalse(){
        when(order.getEntries()).thenReturn(List.of(orderEntryModel));

        assertThat(ownAppOrderService.isOwnFullLicenseOrder(order)).isFalse();
    }

    @Test
    public void isOwnFullLicenseOrder_onlyFullFromSameCompany_returnTrue(){
        when(order.getEntries()).thenReturn(List.of(orderEntryModel));
        when(order.getCompany()).thenReturn(company);

        assertThat(ownAppOrderService.isOwnFullLicenseOrder(order)).isTrue();
    }
    @Test
    public void isOwnFullLicenseOrder_emptyEntries_returnFalse(){
        when(order.getEntries()).thenReturn(List.of());

        assertThat(ownAppOrderService.isOwnFullLicenseOrder(order)).isFalse();
    }

    @Test
    public void hasOwnFullLicenseEntries_noFullLicense_returnFalse(){
        when(order.getEntries()).thenReturn(List.of(anotherOrderEntryModel));

        assertThat(ownAppOrderService.hasOwnFullLicenseEntries(order,company)).isFalse();
    }

    @Test
    public void hasOwnFullLicenseEntries_hasFullAndTrialLicense_returnFalse(){
        assertThat(ownAppOrderService.hasOwnFullLicenseEntries(order,company)).isTrue();
    }

    @Test
    public void hasOwnFullLicenseEntries_onlyFullButFromDifferentCompany_returnFalse(){
        when(order.getEntries()).thenReturn(List.of(orderEntryModel));

        assertThat(ownAppOrderService.hasOwnFullLicenseEntries(order,anotherCompany)).isFalse();
    }

    @Test
    public void hasOwnFullLicenseEntries_onlyFullFromSameCompany_returnTrue(){
        when(order.getEntries()).thenReturn(List.of(orderEntryModel));
        when(order.getCompany()).thenReturn(company);

        assertThat(ownAppOrderService.hasOwnFullLicenseEntries(order,company)).isTrue();
    }
    @Test
    public void hasOwnFullLicenseEntries_emptyEntries_returnFalse(){
        when(order.getEntries()).thenReturn(List.of());

        assertThat(ownAppOrderService.hasOwnFullLicenseEntries(order,company)).isFalse();
    }
}
