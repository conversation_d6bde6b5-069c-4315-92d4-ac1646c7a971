package com.sast.cis.core.job;

import com.sast.cis.core.job.payment.UnusedPaymentRemovalJobPerformable;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.StripeCreditCardPaymentInfoModel;
import com.sast.cis.core.model.UnusedPaymentRemovalCronJobModel;
import com.sast.cis.core.model.ZeroPaymentInfoModel;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.order.CartModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.cronjob.enums.CronJobResult;
import de.hybris.platform.cronjob.enums.CronJobStatus;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.cronjob.CronJobService;
import de.hybris.platform.servicelayer.cronjob.PerformResult;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.store.services.BaseStoreService;
import generated.com.sast.cis.core.model.StripeCreditCardPaymentInfoBuilder;
import generated.com.sast.cis.core.model.UnusedPaymentRemovalCronJobBuilder;
import generated.com.sast.cis.core.model.ZeroPaymentInfoBuilder;
import generated.de.hybris.platform.core.model.order.CartBuilder;
import generated.de.hybris.platform.core.model.order.OrderBuilder;
import org.assertj.core.util.DateUtil;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;

import static com.sast.cis.core.constants.CiscoreConstants.IOT_STORE_BASE_STORE_UID;
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_INTEGRATOR_SSOID;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class UnusedPaymentRemovalJobPerformableITest extends ServicelayerTransactionalTest {
    private static final String UNUSED_STRIPE_PAYMENT_INFO = "unusedStripePaymentInfo";
    private static final String UNUSED_STRIPE_PAYMENT_INFO_ID = UNUSED_STRIPE_PAYMENT_INFO + "Id";

    @Resource(name = "unusedPaymentRemovalJobPerformable")
    private UnusedPaymentRemovalJobPerformable performable;

    @Resource
    private CronJobService cronJobService;

    @Resource
    private ModelService modelService;

    @Resource
    private BaseStoreService baseStoreService;

    @Resource
    private IntegratorService integratorService;

    private ZeroPaymentInfoModel unusedZeroPaymentInfo;
    private ZeroPaymentInfoModel zeroPaymentInfoOnCart;
    private ZeroPaymentInfoModel zeroPaymentInfoUsedForOrder;
    private ZeroPaymentInfoModel zeroPaymentInfoOnOrder;
    private StripeCreditCardPaymentInfoModel unusedStripePaymentInfo;
    private UnusedPaymentRemovalCronJobModel cronJob;

    private OrderModel orderWithZPI;

    @Before
    public void setUp() {
        IntegratorModel validUser = integratorService.getIntegratorByUserId(SAMPLE_DATA_INTEGRATOR_SSOID).get();

        unusedZeroPaymentInfo = ZeroPaymentInfoBuilder.generate()
            .withCode("unusedZPI")
            .withCreationtime(new Date(0L))
            .withUser(validUser)
            .buildIntegrationInstance();
        modelService.save(unusedZeroPaymentInfo);

        zeroPaymentInfoOnCart = ZeroPaymentInfoBuilder.generate()
            .withCode("activeZPI")
            .withCreationtime(new Date(0L))
            .withUser(validUser)
            .buildIntegrationInstance();
        modelService.save(zeroPaymentInfoOnCart);
        CartModel cartWithZeroPaymentInfo = CartBuilder.generate()
            .withCurrency(baseStoreService.getBaseStoreForUid(IOT_STORE_BASE_STORE_UID).getDefaultCurrency())
            .withCode("cartWithZPI")
            .withPaymentInfo(zeroPaymentInfoOnCart)
            .withUser(validUser)
            .withCompany(validUser.getCompany())
            .withDate(DateUtil.now())
            .buildIntegrationInstance();
        modelService.save(cartWithZeroPaymentInfo);

        zeroPaymentInfoUsedForOrder = ZeroPaymentInfoBuilder.generate()
            .withCode("anotherActiveZPI")
            .withCreationtime(new Date(0L))
            .withUser(validUser)
            .buildIntegrationInstance();
        modelService.save(zeroPaymentInfoUsedForOrder);
        orderWithZPI = OrderBuilder.generate()
            .withCurrency(baseStoreService.getBaseStoreForUid(IOT_STORE_BASE_STORE_UID).getDefaultCurrency())
            .withDate(DateUtil.now())
            .withUser(validUser)
            .withCompany(validUser.getCompany())
            .withCode("orderWithZPI")
            .withPaymentInfo(zeroPaymentInfoUsedForOrder)
            .buildIntegrationInstance();
        modelService.save(orderWithZPI);

        unusedStripePaymentInfo = StripeCreditCardPaymentInfoBuilder.generate()
            .withCode(UNUSED_STRIPE_PAYMENT_INFO)
            .withPaymentMethodId(UNUSED_STRIPE_PAYMENT_INFO_ID)
            .withCreationtime(new Date(0L))
            .withUser(validUser)
            .buildIntegrationInstance();
        modelService.save(unusedStripePaymentInfo);

        cronJob = UnusedPaymentRemovalCronJobBuilder.generate()
            .withCode("TestUnusedPaymentRemovalCronJob").withJob(cronJobService.getJob("unusedPaymentRemovalJob"))
            .buildIntegrationInstance();
        modelService.save(cronJob);
    }

    @Test
    public void variousPaymentInfos_areRemovedIfUnused() {
        zeroPaymentInfoOnOrder = (ZeroPaymentInfoModel) orderWithZPI.getPaymentInfo();
        assertThat(zeroPaymentInfoOnOrder.getDuplicate()).isTrue();

        PerformResult result = performable.perform(cronJob);

        assertThat(result.getStatus()).isEqualTo(CronJobStatus.FINISHED);
        assertThat(result.getResult()).isEqualTo(CronJobResult.SUCCESS);

        assertThat(modelService.isRemoved(unusedZeroPaymentInfo)).as("unusedZeroPaymentInfo is removed").isTrue();
        assertThat(modelService.isRemoved(zeroPaymentInfoOnCart)).as("zeroPaymentInfoOnCart is not removed").isFalse();
        assertThat(modelService.isRemoved(zeroPaymentInfoUsedForOrder)).as("zeroPaymentInfoUsedForOrder is removed").isTrue();
        assertThat(modelService.isRemoved(zeroPaymentInfoOnOrder)).as("zeroPaymentInfoOnOrder is not removed").isFalse();
        assertThat(modelService.isRemoved(unusedStripePaymentInfo)).as("unusedStripePaymentInfo is removed").isTrue();
    }
}
