package com.sast.cis.core.service.review;

import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.SessionCatalogRule;
import com.sast.cis.test.utils.TestDataConstants;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.commercefacades.product.data.ReviewData;
import de.hybris.platform.core.PK;
import de.hybris.platform.customerreview.model.CustomerReviewModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.UserService;
import org.junit.Rule;
import org.junit.Test;

import javax.annotation.Resource;

import static com.sast.cis.core.dao.CatalogVersion.ONLINE;
import static com.sast.cis.core.enums.LicenseType.FULL;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class ReviewServiceITest extends ServicelayerTransactionalTest {

    private static final String APP_CODE = "appCode";
    private static final String APP_VERSION_CODE = "appVersionCode";
    private static final String COMMENT = "comment";
    private static final String ALIAS = "alias";
    private static final String HEADLINE = "headline";
    private static final boolean SHOW_COMPANY = true;
    private static final boolean SHOW_NAME = true;
    private static final String PACKAGE_NAME = "a.b.c";
    private static final double RATING = 2.5;

    @Rule
    public final SessionCatalogRule sessionCatalogRule = SessionCatalogRule.onlineCatalog();
    @Resource
    private IntegratorService integratorService;

    @Resource
    private UserService userService;

    @Resource
    private ReviewService reviewService;

    @Resource
    private ModelService modelService;

    @Test
    public void reviewIsCreated_allDataIsPopulated() {
        IntegratorModel integrator = integratorService.getIntegratorByInternalUserId(TestDataConstants.SAMPLE_DATA_INTEGRATOR_UID);
        userService.setCurrentUser(integrator);

        SampleDataCreator sampleDataCreator = new SampleDataCreator();
        AppModel app = sampleDataCreator.createApp(APP_CODE, PACKAGE_NAME, ONLINE);
        sampleDataCreator.createAppLicense(APP_VERSION_CODE, app, APPROVED, FULL);

        ReviewData review = setupReview();
        ReviewData updatedReview = reviewService.createReview(APP_VERSION_CODE, review);

        CustomerReviewModel customerReview = modelService.get(PK.parse(updatedReview.getId()));

        assertThat(customerReview.isShowCompany()).isTrue();
        assertThat(customerReview.isShowName()).isTrue();
        assertThat(customerReview.getComment()).isEqualTo(COMMENT);
        assertThat(customerReview.getAlias()).isEqualTo(ALIAS);
        assertThat(customerReview.getHeadline()).isEqualTo(HEADLINE);
        assertThat(customerReview.getRating()).isEqualTo(RATING);
    }

    private ReviewData setupReview() {
        ReviewData review = new ReviewData();
        review.setComment(COMMENT);
        review.setAlias(ALIAS);
        review.setHeadline(HEADLINE);
        review.setShowCompany(SHOW_COMPANY);
        review.setShowName(SHOW_NAME);
        review.setRating(RATING);
        return review;
    }
}
