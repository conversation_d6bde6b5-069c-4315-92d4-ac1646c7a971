package com.sast.cis.core.service.price.determination

import com.sast.cis.core.factory.CisPdtCriteriaFactory
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderEntryModel
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.europe1.model.PriceRowModel
import de.hybris.platform.jalo.order.price.PriceInformation
import de.hybris.platform.order.exceptions.CalculationException
import de.hybris.platform.order.strategies.calculation.pdt.FindPDTValueInfoStrategy
import de.hybris.platform.order.strategies.calculation.pdt.criteria.PriceValueInfoCriteria
import de.hybris.platform.product.BaseCriteria
import de.hybris.platform.testframework.JUnitPlatformSpecification
import de.hybris.platform.util.PriceValue
import org.junit.Test

@UnitTest
class CisSLFindPriceStrategyUnitSpec extends JUnitPlatformSpecification {
    private static final DEFAULT_PRICE_VALUE = getPriceValue()
    private static final ORDER_ENTRY_NUMBER = 1i
    private static final ORDER_CODE = '9817234'

    private CisPdtCriteriaFactory cisPdtCriteriaFactory = Mock()
    private FindPDTValueInfoStrategy<PriceValue, PriceInformation, PriceValueInfoCriteria> findPriceValueInfoStrategy = Mock()

    private CisSLFindPriceStrategy cisSLFindPriceStrategy

    private PriceValueInfoCriteria priceValueInfoCriteria = Mock()
    private AbstractOrderEntryModel abstractOrderEntry = Mock()
    private AbstractOrderModel abstractOrder = Mock()
    private PriceRowModel priceRow = Mock()

    private PriceInformation defaultPriceInformation;

    def setup() {
        cisSLFindPriceStrategy = new CisSLFindPriceStrategy(cisPdtCriteriaFactory, findPriceValueInfoStrategy)

        defaultPriceInformation = getPriceInformation([pricerow: priceRow], DEFAULT_PRICE_VALUE)
        cisPdtCriteriaFactory.priceValueCriteriaFromOrderEntry(abstractOrderEntry) >> priceValueInfoCriteria
        findPriceValueInfoStrategy.getPDTValues(priceValueInfoCriteria) >> List.of(DEFAULT_PRICE_VALUE)
        findPriceValueInfoStrategy.getPDTInformation(priceValueInfoCriteria) >> List.of(defaultPriceInformation)


        abstractOrderEntry.getOrder() >> abstractOrder
        abstractOrderEntry.getEntryNumber() >> ORDER_ENTRY_NUMBER
        abstractOrder.getCode() >> ORDER_CODE
    }

    @Test
    void 'isSLOnly() returns true'() {
        when:
        def actualSlOnly = cisSLFindPriceStrategy.isSLOnly()

        then:
        actualSlOnly
    }

    @Test
    void 'getPriceInformation() returns price information'() {
        given:
        def givenCriteria = Mock(BaseCriteria)
        def expectedInfo = Mock(PriceInformation)

        when:
        def actualInfo = cisSLFindPriceStrategy.getPriceInformation(givenCriteria)

        then:
        1 * cisPdtCriteriaFactory.priceInfoCriteriaFromBaseCriteria(givenCriteria) >> priceValueInfoCriteria
        1 * findPriceValueInfoStrategy.getPDTInformation(priceValueInfoCriteria) >> List.of(expectedInfo)
        actualInfo == List.of(expectedInfo)
    }

    @Test
    void 'findBasePriceInfoForCalculation() returns price information accordingly'() {
        given:
        def expectedInformation = Optional.of(CisPriceInformation.builder()
                .priceRow(priceRow)
                .priceInformation(defaultPriceInformation)
                .build())

        when:
        def actualInformation = cisSLFindPriceStrategy.findBasePriceInfoForCalculation(abstractOrderEntry)

        then:
        actualInformation == expectedInformation
    }

    @Test
    void 'findBasePriceInfoForCalculation() returns empty if price value is not found'() {
        given:
        def expectedInformation = Optional.empty()

        when:
        def actualInformation = cisSLFindPriceStrategy.findBasePriceInfoForCalculation(abstractOrderEntry)

        then:
        findPriceValueInfoStrategy.getPDTValues(priceValueInfoCriteria) >> List.of()
        actualInformation == expectedInformation
    }

    @Test
    void 'findBasePriceInfoForCalculation() throws if given orderEntry is null'() {
        when:
        cisSLFindPriceStrategy.findBasePriceInfoForCalculation(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'findBasePriceInfoForCalculation() throws CalculationException if price info is not found'() {
        when:
        cisSLFindPriceStrategy.findBasePriceInfoForCalculation(abstractOrderEntry)

        then:
        findPriceValueInfoStrategy.getPDTInformation(priceValueInfoCriteria) >> List.of()
        thrown(CalculationException)
    }

    @Test
    void 'findBasePriceInfoForCalculation() throws CalculationException if does not match the legacy price value'() {
        given:
        def nonMatchingPriceInformation = getPriceInformation([pricerow: priceRow], getPriceValue('USD'))

        when:
        cisSLFindPriceStrategy.findBasePriceInfoForCalculation(abstractOrderEntry)

        then:
        findPriceValueInfoStrategy.getPDTInformation(priceValueInfoCriteria) >> List.of(nonMatchingPriceInformation)
        thrown(CalculationException)
    }

    @Test
    void 'findBasePriceInfoForCalculation() throws CalculationException if price info does not contain a price row'() {
        given:
        def priceInformationWithoutPriceRow = getPriceInformation([:], DEFAULT_PRICE_VALUE)

        when:
        cisSLFindPriceStrategy.findBasePriceInfoForCalculation(abstractOrderEntry)

        then:
        findPriceValueInfoStrategy.getPDTInformation(priceValueInfoCriteria) >> List.of(priceInformationWithoutPriceRow)
        thrown(CalculationException)
    }

    @Test
    void 'findBasePrice() returns price information accordingly'() {
        when:
        def actualInformation = cisSLFindPriceStrategy.findBasePrice(abstractOrderEntry)

        then:
        actualInformation == DEFAULT_PRICE_VALUE
    }

    @Test
    void 'findBasePrice() returns empty if price value is not found'() {
        when:
        def actualInformation = cisSLFindPriceStrategy.findBasePrice(abstractOrderEntry)

        then:
        findPriceValueInfoStrategy.getPDTValues(priceValueInfoCriteria) >> List.of()
        actualInformation == null
    }

    @Test
    void 'findBasePrice() throws if given orderEntry is null'() {
        when:
        cisSLFindPriceStrategy.findBasePrice(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'findBasePrice() throws CalculationException if price info is not found'() {
        when:
        cisSLFindPriceStrategy.findBasePrice(abstractOrderEntry)

        then:
        findPriceValueInfoStrategy.getPDTInformation(priceValueInfoCriteria) >> List.of()
        thrown(CalculationException)
    }

    @Test
    void 'findBasePrice() throws CalculationException if does not match the legacy price value'() {
        given:
        def nonMatchingPriceInformation = getPriceInformation([pricerow: priceRow], getPriceValue('USD'))

        when:
        cisSLFindPriceStrategy.findBasePrice(abstractOrderEntry)

        then:
        findPriceValueInfoStrategy.getPDTInformation(priceValueInfoCriteria) >> List.of(nonMatchingPriceInformation)
        thrown(CalculationException)
    }

    @Test
    void 'findBasePrice() throws CalculationException if price info does not contain a price row'() {
        given:
        def priceInformationWithoutPriceRow = getPriceInformation([:], DEFAULT_PRICE_VALUE)

        when:
        cisSLFindPriceStrategy.findBasePrice(abstractOrderEntry)

        then:
        findPriceValueInfoStrategy.getPDTInformation(priceValueInfoCriteria) >> List.of(priceInformationWithoutPriceRow)
        thrown(CalculationException)
    }

    private static PriceValue getPriceValue(String currencyIso = 'EUR', double price = 19.99d, boolean netto = true) {
        return new PriceValue(currencyIso, price, netto)
    }

    private static PriceInformation getPriceInformation(Map<Object, Object> qualifiers = Map.of(), PriceValue priceValue = DEFAULT_PRICE_VALUE) {
        return new PriceInformation(qualifiers, priceValue)
    }
}
