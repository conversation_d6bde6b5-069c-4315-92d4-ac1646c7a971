package com.sast.cis.core.filter

import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.LanguageModel
import de.hybris.platform.jalo.c2l.LocalizableItem
import de.hybris.platform.servicelayer.exceptions.SystemException
import de.hybris.platform.servicelayer.i18n.CommonI18NService
import de.hybris.platform.servicelayer.model.AbstractItemModel
import de.hybris.platform.servicelayer.session.SessionService
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

import javax.servlet.FilterChain
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

@UnitTest
class SessionLanguageFilterUnitSpec extends JUnitPlatformSpecification {
    private static final String EN = 'en'
    private SessionService sessionService = Mock()
    private CommonI18NService commonI18NService = Mock()

    private SessionLanguageFilter sessionLanguageFilter

    private HttpServletRequest mockRequest = Mock()
    private HttpServletResponse mockResponse = Mock()
    private FilterChain filterChain = Mock()
    private LanguageModel languageEn = Mock()

    def setup() {
        sessionLanguageFilter = new SessionLanguageFilter(sessionService, commonI18NService, EN)

        commonI18NService.getLanguage(EN) >> languageEn
    }

    @Test
    def 'filter sets language fallback and session language and continues filter chain'() {
        when:
        sessionLanguageFilter.doFilter(mockRequest, mockResponse, filterChain)

        then:
        1 * sessionService.setAttribute(LocalizableItem.LANGUAGE_FALLBACK_ENABLED, Boolean.TRUE)
        1 * sessionService.setAttribute(AbstractItemModel.LANGUAGE_FALLBACK_ENABLED_SERVICE_LAYER, Boolean.TRUE)
        1 * commonI18NService.setCurrentLanguage(languageEn)
        1 * filterChain.doFilter(mockRequest, mockResponse)
    }

    @Test
    def 'commonI18NService throws SystemException, fallback is still configured and filter chain continued'() {
        when:
        sessionLanguageFilter.doFilter(mockRequest, mockResponse, filterChain)

        then:
        1 * commonI18NService.getLanguage(EN) >> { throw new SystemException('lala') }

        1 * sessionService.setAttribute(LocalizableItem.LANGUAGE_FALLBACK_ENABLED, Boolean.TRUE)
        1 * sessionService.setAttribute(AbstractItemModel.LANGUAGE_FALLBACK_ENABLED_SERVICE_LAYER, Boolean.TRUE)
        0 * commonI18NService.setCurrentLanguage(_)
        1 * filterChain.doFilter(mockRequest, mockResponse)
    }

    @Test
    def 'commonI18NService throws RuntimeException, processing is aborted'() {
        when:
        sessionLanguageFilter.doFilter(mockRequest, mockResponse, filterChain)

        then:
        1 * commonI18NService.getLanguage(EN) >> { throw new RuntimeException('lala') }

        thrown(RuntimeException)
        0 * filterChain.doFilter(mockRequest, mockResponse)
    }

}
