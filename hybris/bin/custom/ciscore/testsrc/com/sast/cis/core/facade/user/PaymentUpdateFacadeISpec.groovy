package com.sast.cis.core.facade.user

import com.sast.cis.core.SpringContextServiceMockHelper
import com.sast.cis.core.config.BaseStoreConfigService
import com.sast.cis.core.config.keycloak.SiteUmpAdapterConfigResolutionService
import com.sast.cis.core.constants.BaseStoreEnum
import com.sast.cis.core.data.CreditCardPaymentInfoData
import com.sast.cis.core.data.OrderPaymentUpdate
import com.sast.cis.core.enums.CompanyApprovalStatus
import com.sast.cis.core.enums.Feature
import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.facade.CisCartFacade
import com.sast.cis.core.factory.CisCartFactory
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.SpringContextService
import com.sast.cis.core.service.TranslationService
import com.sast.cis.core.service.company.IotCompanyService
import com.sast.cis.core.util.Base58UUIDCodeGenerator
import com.sast.cis.payment.dpg.model.DpgCreditCardPaymentInfoModel
import com.sast.cis.payment.dpg.model.DpgSellerAccountModel
import com.sast.cis.test.utils.FeatureToggleRule
import com.sast.cis.test.utils.LoginUtil
import com.sast.cis.test.utils.SampleDataCreator
import com.sast.cis.test.utils.UmpWireMockRule
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.catalog.CatalogVersionService
import de.hybris.platform.catalog.model.CatalogVersionModel
import de.hybris.platform.core.enums.OrderStatus
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.core.model.user.UserGroupModel
import de.hybris.platform.order.CartService
import de.hybris.platform.payment.dto.TransactionStatus
import de.hybris.platform.payment.enums.PaymentTransactionType
import de.hybris.platform.product.UnitService
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.servicelayer.i18n.CommonI18NService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.user.UserService
import de.hybris.platform.servicelayer.user.daos.UserGroupDao
import de.hybris.platform.site.BaseSiteService
import de.hybris.platform.store.services.BaseStoreService
import org.junit.Rule
import org.junit.Test
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.util.ReflectionTestUtils
import org.springframework.web.client.RestTemplate
import spock.lang.Subject

import javax.annotation.Resource

import static com.sast.cis.core.constants.CiscoreConstants.CIS_PRODUCT_CATALOG
import static com.sast.cis.core.constants.CiscoreConstants.IOT_STORE_BASE_SITE_UID
import static com.sast.cis.core.dao.CatalogVersion.ONLINE
import static com.sast.cis.core.enums.CompanyApprovalStatus.APPROVED_COMMERCIAL
import static com.sast.cis.core.enums.PaymentProvider.DPG
import static com.sast.cis.test.utils.TestDataConstants.*
import static com.sast.cis.test.utils.UmpWireMockRule.UMP_BASE_URL_KEY

@IntegrationTest
class PaymentUpdateFacadeISpec extends ServicelayerTransactionalSpockSpecification {
    private static final double TOTAL_PRICE = 279.00d

    private SpringContextService springContextService
    private SampleDataCreator sampleDataCreator = new SampleDataCreator()

    @Rule
    private FeatureToggleRule featureToggleRule = new FeatureToggleRule()

    @Rule
    public UmpWireMockRule umpWireMockRule = new UmpWireMockRule()

    @Resource
    @Subject
    private PaymentUpdateFacade paymentUpdateFacade

    @Resource
    private ModelService modelService

    @Resource
    private ConfigurationService configurationService

    @Resource
    private IotCompanyService iotCompanyService

    @Resource
    private UserService userService

    @Resource
    private TranslationService translationService

    @Resource
    private UnitService unitService

    @Resource
    private BaseStoreService baseStoreService

    @Resource
    private CartService cartService

    @Resource
    private CisCartFactory cisCartFactory

    @Resource
    private CisCartFacade cisCartFacade

    @Resource
    private RestTemplate cisRestTemplate

    @Resource
    private BaseSiteService baseSiteService

    @Resource
    private CatalogVersionService catalogVersionService

    @Resource
    private BaseStoreConfigService baseStoreConfigService

    @Resource
    private CommonI18NService commonI18NService

    @Resource
    private SiteUmpAdapterConfigResolutionService siteUmpAdapterConfigResolutionService

    @Resource
    private UserGroupDao userGroupDao

    private IoTCompanyModel sellerCompany
    private DpgSellerAccountModel dpgSellerAccount

    private IoTCompanyModel buyerCompany
    private AppLicenseModel evalLicense
    private AppLicenseModel fullLicense
    private AppLicenseModel subscriptionLicense
    private AppModel app
    private IntegratorModel buyerUser
    private CatalogVersionModel catalogVersion
    private DpgCreditCardPaymentInfoModel dpgCreditCardPaymentInfo
    private OrderModel order

    def setup() {
        featureToggleRule.enable(Feature.FEATURE_ENABLE_DPG_CC)
        featureToggleRule.enable(Feature.FEATURE_PAYMENT_UPDATES)

        springContextService = new SpringContextServiceMockHelper().newSpringContextServiceMock()
        String configPrefixForBaseStore = baseStoreConfigService.getConfigPrefixForBaseStore(BaseStoreEnum.AZENA);
        String key = String.format(UMP_BASE_URL_KEY, configPrefixForBaseStore);
        configurationService.getConfiguration().setProperty(key, umpWireMockRule.getUmpUrl());

        buyerCompany = iotCompanyService.getCompanyByUid(DEMO_COMPANY_UID).orElseThrow()
        buyerUser = (IntegratorModel) userService.getUserForUID(DEMO_COMPANY_INTEGRATOR_UID)

        app = sampleDataCreator.createApp('App_code', 'com.testapp', ONLINE)
        fullLicense = sampleDataCreator.createFullAppLicense(app)
        evalLicense = sampleDataCreator.createEvalForFullAppLicense(fullLicense)
        subscriptionLicense = sampleDataCreator.createSubscriptionAppLicense(app)
        sellerCompany = app.getCompany()
        dpgCreditCardPaymentInfo = createPaymentInfo()
        order = createOrder()

        buyerCompany = prepareCompany(buyerCompany, APPROVED_COMMERCIAL, true, BigDecimal.valueOf(10000))
        sellerCompany = prepareCompany(sellerCompany, APPROVED_COMMERCIAL, false, BigDecimal.ZERO)
        dpgSellerAccount = sampleDataCreator.createDpgSellerAccount(sellerCompany)

        LoginUtil loginUtil = new LoginUtil(siteUmpAdapterConfigResolutionService, cisRestTemplate)
        SecurityContextHolder.getContext().setAuthentication(loginUtil.generateKeycloakAuthenticationObject())
        userService.setCurrentUser(userService.getUserForUID(DEMO_COMPANY_INTEGRATOR_UID))
        baseSiteService.setCurrentBaseSite(baseSiteService.getBaseSiteForUID(IOT_STORE_BASE_SITE_UID), false)
        catalogVersion = catalogVersionService.getCatalogVersion(CIS_PRODUCT_CATALOG, ONLINE.getVersionName())
        catalogVersionService.setSessionCatalogVersions(Collections.singletonList(catalogVersion))

        ReflectionTestUtils.setField(translationService, "springContextService", springContextService)
    }

    @Test
    void 'createPaymentUpdate for an order paid with DPG/CC will return a checkout for DPG/CC'() {
        when:
        def actualOrderPaymentUpdateData = paymentUpdateFacade.createPaymentUpdate(order.getCode())

        then:
        verifyAll(actualOrderPaymentUpdateData.order) {
            it.code == order.getCode()
        }

        actualOrderPaymentUpdateData.checkoutInfo.size() == 1

        verifyAll(actualOrderPaymentUpdateData.checkoutInfo[0]) {
            it.paymentMethod == PaymentMethodType.CREDIT_CARD
            it.paymentProvider == PaymentProvider.DPG
            it.userActionParameters.keySet() == ['dpgjsUrl', 'sessionId', 'paymentId', 'sessionConfig'] as Set
            it.paymentInfos.size() == 1
            verifyAll(it.paymentInfos[0] as CreditCardPaymentInfoData) {
                it.paymentProvider == PaymentProvider.DPG
                it.paymentMethod == PaymentMethodType.CREDIT_CARD
                it.cardNumber == dpgCreditCardPaymentInfo.number
                it.expiryMonth == dpgCreditCardPaymentInfo.validToMonth
                it.expiryYear == dpgCreditCardPaymentInfo.validToYear
                it.accountHolderName == dpgCreditCardPaymentInfo.ccOwner
            }
        }
    }

    @Test
    void 'payment update with a new card is performed for an order initially successfully paid with DPG/CC'() {
        given:
        def givenToken = Base58UUIDCodeGenerator.generateCode("ccToken")
        def givenCcInfoData = new CreditCardPaymentInfoData()
        givenCcInfoData.setPaymentMethod(PaymentMethodType.CREDIT_CARD)
        givenCcInfoData.setPaymentProvider(PaymentProvider.DPG)
        givenCcInfoData.setReusable(false)
        givenCcInfoData.setAccountHolderName("Herbert")
        givenCcInfoData.setCardNumber("123456******5678")
        givenCcInfoData.setExpiryMonth("10")
        givenCcInfoData.setExpiryYear("29")
        givenCcInfoData.setToken(givenToken)

        when: 'payment update is initialized'
        def actualOrderPaymentUpdateData = paymentUpdateFacade.createPaymentUpdate(order.getCode())

        then: 'payment update data is returned with order details and checkoutinfo for DPG/CC'
        verifyAll(actualOrderPaymentUpdateData.order) {
            it.code == order.getCode()
        }

        actualOrderPaymentUpdateData.checkoutInfo.size() == 1

        verifyAll(actualOrderPaymentUpdateData.checkoutInfo[0]) {
            paymentMethod == PaymentMethodType.CREDIT_CARD
            paymentProvider == PaymentProvider.DPG
            userActionParameters.keySet() == ['dpgjsUrl', 'sessionId', 'paymentId', 'sessionConfig'] as Set
            paymentInfos.size() == 1
            verifyAll(it.paymentInfos[0] as CreditCardPaymentInfoData) {
                paymentProvider == PaymentProvider.DPG
                paymentMethod == PaymentMethodType.CREDIT_CARD
                cardNumber == dpgCreditCardPaymentInfo.number
                expiryMonth == dpgCreditCardPaymentInfo.validToMonth
                expiryYear == dpgCreditCardPaymentInfo.validToYear
                accountHolderName == dpgCreditCardPaymentInfo.ccOwner
            }
        }

        when: 'payment update is performed with a new payment info for given paymentInfoData'
        paymentUpdateFacade.createPaymentInfoAndAuthorize(order.getCode(), givenCcInfoData)

        then: 'order payment info is changed'
        order.paymentInfo instanceof DpgCreditCardPaymentInfoModel

        verifyAll((DpgCreditCardPaymentInfoModel) order.paymentInfo) {
            paymentProvider == PaymentProvider.DPG
            code != dpgCreditCardPaymentInfo.code
            token == givenToken
            ccOwner == 'Herbert'
            number == '123456******5678'
            duplicate
        }

        and: 'a new payment transaction for the new order payment info exists'
        order.paymentTransactions.size() == 2
        order.paymentTransactions.get(0).info.code == dpgCreditCardPaymentInfo.code

        verifyAll(order.paymentTransactions.get(1)) {
            type == PaymentTransactionType.AUTHORIZATION
            info.code == order.paymentInfo.code
            plannedAmount == BigDecimal.valueOf(TOTAL_PRICE)
            entries.size() == 1
            verifyAll(entries.get(0)) {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
            }
        }
    }

    @Test
    void 'payment update with saved card is performed for an order initially successfully paid with DPG/CC'() {
        given:
        def givenNewInfo = createPaymentInfo()
        def givenUpdateData = new OrderPaymentUpdate().withPaymentInfoId(givenNewInfo.pk.toString())

        when: 'payment update is initialized'
        def actualOrderPaymentUpdateData = paymentUpdateFacade.createPaymentUpdate(order.getCode())

        then: 'payment update data is returned with order details and checkoutinfo for DPG/CC'
        verifyAll(actualOrderPaymentUpdateData.order) {
            it.code == order.getCode()
        }

        actualOrderPaymentUpdateData.checkoutInfo.size() == 1

        verifyAll(actualOrderPaymentUpdateData.checkoutInfo[0]) {
            paymentMethod == PaymentMethodType.CREDIT_CARD
            paymentProvider == PaymentProvider.DPG
            userActionParameters.keySet() == ['dpgjsUrl', 'sessionId', 'paymentId', 'sessionConfig'] as Set
            paymentInfos.size() == 2
            paymentInfos*.getId().toSet() == [dpgCreditCardPaymentInfo.pk.toString(), givenNewInfo.pk.toString()] as Set
        }

        when: 'payment update is performed with an existing stored paymentInfo'
        paymentUpdateFacade.setPaymentInfoAndAuthorize(order.getCode(), givenUpdateData)

        then: 'order payment info is changed'
        order.paymentInfo instanceof DpgCreditCardPaymentInfoModel

        verifyAll((DpgCreditCardPaymentInfoModel) order.paymentInfo) {
            paymentProvider == PaymentProvider.DPG
            code == givenNewInfo.code
            token == givenNewInfo.token
            ccOwner == givenNewInfo.ccOwner
            number == givenNewInfo.number
        }

        and: 'a new payment transaction for the new order payment info exists'
        order.paymentTransactions.size() == 2
        order.paymentTransactions.get(1).info.code == givenNewInfo.code

        verifyAll(order.paymentTransactions.get(1)) {
            type == PaymentTransactionType.AUTHORIZATION
            info.code == order.paymentInfo.code
            plannedAmount == BigDecimal.valueOf(TOTAL_PRICE)
            entries.size() == 1
            verifyAll(entries.get(0)) {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
            }
        }
    }

    private IoTCompanyModel prepareCompany(IoTCompanyModel company, CompanyApprovalStatus companyApprovalStatus, boolean sepaEnabled, BigDecimal creditLimit) {
        company.setApprovalStatus(companyApprovalStatus)
        company.setAaCustomerGroup(getUsergroup())
        modelService.save(company)
        def data = umpWireMockRule.buildUmpCompanyData(company.getUid(), AZENA_MARKETPLACE_ID)
        data.withCreditLimit(creditLimit)
        umpWireMockRule.prepareGetCompanyDataResponse(company.getUid(), umpWireMockRule.buildResponseDefinition(data))
        return company
    }

    private OrderModel createOrder() {
        def order = sampleDataCreator.createOrder(buyerUser, OrderStatus.COMPLETED)
        def entry = sampleDataCreator.createOrderEntry(order, subscriptionLicense, 3)
        def subscriptions = [
                sampleDataCreator.createSubscription(entry),
                sampleDataCreator.createSubscription(entry),
                sampleDataCreator.createSubscription(entry)
        ]

        order.setPaymentInfo(dpgCreditCardPaymentInfo)
        order.setNet(true)
        modelService.saveAll(order, entry)
        def authTransaction = sampleDataCreator.createSuccessfullyCapturedTransaction(order, dpgCreditCardPaymentInfo)
        modelService.save(authTransaction)
        modelService.saveAll(subscriptions)
        return order
    }

    private DpgCreditCardPaymentInfoModel createPaymentInfo() {
        def token = Base58UUIDCodeGenerator.generateCode("dpgToken");
        DpgCreditCardPaymentInfoModel paymentInfo = modelService.create(DpgCreditCardPaymentInfoModel.class);
        paymentInfo.setCode(Base58UUIDCodeGenerator.generateCode("dpgCc"))
        paymentInfo.setToken(token)
        paymentInfo.setPaymentProvider(DPG)
        paymentInfo.setSaved(true)
        paymentInfo.setUser(buyerUser)
        paymentInfo.setCcOwner("Herr Bert")
        paymentInfo.setNumber("**************1234")
        paymentInfo.setValidToMonth("12")
        paymentInfo.setValidToYear("34")
        paymentInfo.setSubscriptionValidated(true)
        paymentInfo.setSubscriptionId(token)
        paymentInfo.setPaymentInstrumentId(Base58UUIDCodeGenerator.generateCode("dpgInstrument"))

        modelService.save(paymentInfo);
        modelService.refresh(buyerUser);
        return paymentInfo;
    }
    private UserGroupModel getUsergroup() {
        return userGroupDao.findUserGroupByUid("IDW000")
    }
}
