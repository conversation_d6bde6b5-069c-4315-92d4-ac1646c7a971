package com.sast.cis.core.service.price.licensepricehandler

import com.sast.cis.core.billingintegration.request.ExportMode
import com.sast.cis.core.billingintegration.request.ProductExport
import com.sast.cis.core.billingintegration.request.ProductExportFactory
import com.sast.cis.core.enums.BillingSystemStatus
import com.sast.cis.core.enums.LicenseType
import com.sast.cis.core.model.*
import com.sast.cis.core.service.app.PriceRowFilterStrategy
import com.sast.cis.core.service.price.PriceDraftMatchingStrategy
import com.sast.cis.core.service.price.PriceDraftService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.core.model.c2l.CurrencyModel
import de.hybris.platform.europe1.model.PriceRowModel
import de.hybris.platform.servicelayer.model.ModelService
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

@UnitTest
class ReleasedPaidLicensePriceUpdateHandlerUnitSpec extends JUnitPlatformSpecification {
    private PriceDraftService priceDraftService = Mock(PriceDraftService)
    private PriceDraftMatchingStrategy priceDraftMatchingStrategy = Mock(PriceDraftMatchingStrategy)
    private ModelService modelService = Mock(ModelService)
    private ProductExportFactory productExportFactory = Mock(ProductExportFactory)
    private ProductExport productExport = Mock(ProductExport)
    private PriceRowFilterStrategy priceRowFilterStrategy = Mock(PriceRowFilterStrategy)

    private ReleasedPaidLicensePriceUpdateHandler fullLicensePriceUpdateHandler

    private AppLicenseModel appLicense = Mock(AppLicenseModel)
    private AppModel app = Mock(AppModel)
    private IoTCompanyModel ioTCompany = Mock(IoTCompanyModel)
    private CountryModel country = Mock(CountryModel)
    private CurrencyModel eur = Mock(CurrencyModel)
    private CurrencyModel usd = Mock(CurrencyModel)
    private PriceRowModel existingPrice = Mock(PriceRowModel)

    private PriceDraftModel eurDraft = Mock(PriceDraftModel)
    private PriceDraftModel usdDraft = Mock(PriceDraftModel)
    private PendingProductInfoModel pendingProductInfo = Mock(PendingProductInfoModel)
    private VolumeDiscountModel firstVolumeDiscount = Mock(VolumeDiscountModel)
    private VolumeDiscountModel secondVolumeDiscount = Mock(VolumeDiscountModel)
    private PriceDraftModel firstEurDiscount = Mock(PriceDraftModel)
    private PriceDraftModel secondEurDiscount = Mock(PriceDraftModel)
    private PriceDraftModel firstUsdDiscount = Mock(PriceDraftModel)
    private PriceDraftModel secondUsdDiscount = Mock(PriceDraftModel)

    def setup() {
        fullLicensePriceUpdateHandler = new ReleasedPaidLicensePriceUpdateHandler(priceDraftMatchingStrategy, modelService, productExportFactory, priceDraftService, priceRowFilterStrategy)

        productExportFactory.getProductExport(ExportMode.SYNC) >> productExport

        priceDraftMatchingStrategy.matchesLatestPrices(_ as Collection, [existingPrice]) >> false
        modelService.create(PendingProductInfoModel) >> pendingProductInfo

        appLicense.getBillingSystemStatus() >> BillingSystemStatus.IN_SYNC
        appLicense.getBaseProduct() >> app
        appLicense.getEurope1Prices() >> [existingPrice]
        priceRowFilterStrategy.filterPriceRows(_) >> [existingPrice]
        appLicense.getLicenseType() >> LicenseType.FULL
        app.getCompany() >> ioTCompany
        ioTCompany.getCountry() >> country
        country.getCurrency() >> eur
    }

    @Test
    def 'when all drafts are applicable, update all prices'() {
        given:
        priceDraftService.createPriceDrafts(appLicense) >> [eurDraft, usdDraft]

        when:
        fullLicensePriceUpdateHandler.updatePrices(appLicense, Set.of(usd, eur))

        then:
        1 * pendingProductInfo.setPrices(Set.of(eurDraft, usdDraft))
        1 * appLicense.setPendingProductInfo(pendingProductInfo)
        1 * appLicense.setBillingSystemStatus(BillingSystemStatus.UPDATE_PENDING)
        1 * productExport.updateProduct(appLicense)
    }

    @Test
    def 'only given currencies are updated'() {
        given:
        priceDraftService.createPriceDrafts(appLicense) >> [usdDraft]

        when:
        fullLicensePriceUpdateHandler.updatePrices(appLicense, Set.of(usd))

        then:
        1 * pendingProductInfo.setPrices(Set.of(usdDraft))
        1 * appLicense.setPendingProductInfo(pendingProductInfo)
        1 * appLicense.setBillingSystemStatus(BillingSystemStatus.UPDATE_PENDING)
        1 * productExport.updateProduct(appLicense)
    }

    @Test
    def 'given volume discounts, additional price rows are appended'() {
        given:
        priceDraftService.createPriceDrafts(appLicense) >> [usdDraft, eurDraft, firstEurDiscount, firstUsdDiscount, secondEurDiscount, secondUsdDiscount]
        appLicense.getVolumeDiscounts() >> [firstVolumeDiscount, secondVolumeDiscount]

        when:
        fullLicensePriceUpdateHandler.updatePrices(appLicense, Set.of(usd, eur))

        then:
        1 * pendingProductInfo.setPrices(Set.of(usdDraft, eurDraft, firstUsdDiscount, secondUsdDiscount, firstEurDiscount, secondEurDiscount))
        1 * appLicense.setPendingProductInfo(pendingProductInfo)
        1 * appLicense.setBillingSystemStatus(BillingSystemStatus.UPDATE_PENDING)
        1 * productExport.updateProduct(appLicense)
    }

    @Test
    def 'given only volume discount changes, all prices are updated'() {
        given:
        priceDraftService.createPriceDrafts(appLicense) >> [usdDraft, eurDraft, firstEurDiscount, firstUsdDiscount, secondEurDiscount, secondUsdDiscount]
        appLicense.getVolumeDiscounts() >> [firstVolumeDiscount, secondVolumeDiscount]

        when:
        fullLicensePriceUpdateHandler.updatePrices(appLicense, Set.of(usd, eur))

        then:
        priceDraftMatchingStrategy.matchesLatestPrices(_ as Collection, [existingPrice]) >> false
        1 * pendingProductInfo.setPrices(Set.of(usdDraft, eurDraft, firstUsdDiscount, secondUsdDiscount, firstEurDiscount, secondEurDiscount))
        1 * appLicense.setPendingProductInfo(pendingProductInfo)
        1 * appLicense.setBillingSystemStatus(BillingSystemStatus.UPDATE_PENDING)
        1 * productExport.updateProduct(appLicense)
    }

    @Test
    def 'if everything is up to date, exit early and dont modify anything'() {
        when:
        fullLicensePriceUpdateHandler.updatePrices(appLicense, Set.of(usd, eur))

        then:
        priceDraftMatchingStrategy.matchesLatestPrices(_ as Collection, [existingPrice]) >> true
        0 * modelService._
        0 * productExport._
        0 * appLicense.setBillingSystemStatus(_)
        0 * appLicense.setPendingProductInfo(_)
    }

    @Test
    @Unroll
    def 'if license is #givenStatus, throw IllegalStateException'() {
        when:
        fullLicensePriceUpdateHandler.updatePrices(appLicense, Set.of(usd, eur))

        then:
        appLicense.getBillingSystemStatus() >> givenStatus
        thrown(IllegalStateException)
        0 * modelService._
        0 * productExport._
        0 * appLicense.setBillingSystemStatus(_)
        0 * appLicense.setPendingProductInfo(_)

        where:
        givenStatus                        || _
        BillingSystemStatus.NEW            || _
        BillingSystemStatus.UPDATE_PENDING || _
        BillingSystemStatus.CREATE_PENDING || _
        BillingSystemStatus.REJECTED       || _
        null                               || _
    }

    @Test
    @Unroll
    def 'given license of type #givenLicenseType and billing status is #givenBillingStatus, isApplicable returns #expectedResult'() {
        when:
        def actualResult = fullLicensePriceUpdateHandler.isApplicable(appLicense)

        then:
        appLicense.getLicenseType() >> givenLicenseType
        appLicense.getBillingSystemStatus() >> givenBillingStatus
        actualResult == expectedResult

        where:
        givenLicenseType         | givenBillingStatus          || expectedResult
        LicenseType.FULL         | BillingSystemStatus.IN_SYNC || true
        LicenseType.EVALUATION   | BillingSystemStatus.IN_SYNC || false
        LicenseType.SUBSCRIPTION | BillingSystemStatus.IN_SYNC || true

        LicenseType.FULL         | BillingSystemStatus.NEW     || false
        LicenseType.EVALUATION   | BillingSystemStatus.NEW     || false
        LicenseType.SUBSCRIPTION | BillingSystemStatus.NEW     || false

        LicenseType.FULL         | BillingSystemStatus.IN_SYNC || true
        LicenseType.EVALUATION   | BillingSystemStatus.IN_SYNC || false
        LicenseType.SUBSCRIPTION | BillingSystemStatus.IN_SYNC || true

        LicenseType.FULL         | BillingSystemStatus.IN_SYNC || true
        LicenseType.EVALUATION   | BillingSystemStatus.IN_SYNC || false
        LicenseType.SUBSCRIPTION | BillingSystemStatus.IN_SYNC || true

        LicenseType.FULL         | BillingSystemStatus.IN_SYNC || true
        LicenseType.EVALUATION   | BillingSystemStatus.IN_SYNC || false
        LicenseType.SUBSCRIPTION | BillingSystemStatus.IN_SYNC || true
    }
}
