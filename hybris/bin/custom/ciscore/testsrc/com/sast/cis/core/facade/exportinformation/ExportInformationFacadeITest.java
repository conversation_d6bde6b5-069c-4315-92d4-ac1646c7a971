package com.sast.cis.core.facade.exportinformation;

import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.data.ExportInformationEntryData;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.SessionCatalogRule;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class ExportInformationFacadeITest extends ServicelayerTransactionalTest {

    @Resource
    private ExportInformationFacade exportInformationFacade;
    @Resource
    private CommonI18NService commonI18NService;
    @Rule
    public SessionCatalogRule sessionCatalogRule = SessionCatalogRule.onlineCatalog();

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();

    @Before
    public void setLanguage() {
        commonI18NService.setCurrentLanguage(commonI18NService.getLanguage("en"));
    }

    @Test
    public void getCorrectExportInformationData() {

        sampleDataCreator.createApps(3, CatalogVersion.ONLINE, true);
        sampleDataCreator.createApps(2, CatalogVersion.ONLINE, false, 3);
        List<ExportInformationEntryData> datas = exportInformationFacade.getExportInformationEntryDatas();

        assertThat(datas).extracting(ExportInformationEntryData::getAppName)
            .containsExactlyInAnyOrder("name_app_0", "name_app_1", "name_app_2");
        assertThat(datas).extracting(ExportInformationEntryData::getCompanyName).containsOnly("CompanyA");
        assertThat(datas).extracting(ExportInformationEntryData::getEccn)
            .containsExactlyInAnyOrder("eccn_app_0_version", "eccn_app_1_version", "eccn_app_2_version");
    }

}
