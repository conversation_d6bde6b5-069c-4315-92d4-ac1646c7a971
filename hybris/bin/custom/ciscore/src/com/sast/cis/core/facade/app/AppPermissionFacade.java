package com.sast.cis.core.facade.app;

import com.google.common.base.Preconditions;
import com.sast.cis.core.data.ErrorMessageData;
import com.sast.cis.core.exceptions.permission.AppPermissionException;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.AppService;
import com.sast.cis.core.service.app.AppPermissionService;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.exceptions.UnknownIdentifierException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;

@Slf4j
@Component
@AllArgsConstructor
public class AppPermissionFacade {

    private final AppService appService;
    private final AppPermissionService appPermissionService;

    /**
     * throws an exception if the user has no permission to view the app
     * @param appCode
     */
    public void verifyAppCanBeViewedByIntegrator(String appCode) {
        AppModel app = appService.getOnlineAppForCode(appCode)
            .orElseThrow(() -> new UnknownIdentifierException("No app with code=" + appCode + " in online catalog"));

        Set<ErrorMessageData> errorMessageData = appPermissionService.verifyAppCanBeViewedByIntegrator(app);

        throwExceptionIfErrorsExist(errorMessageData, appCode);
    }

    public void verifyProductBeViewedByIntegrator(ProductModel product) {
        Preconditions.checkArgument(product != null, "product cannot be null");

        AppModel app = appService.getAppFromProduct(product)
            .orElseThrow(() -> new UnknownIdentifierException("No app for product with code=" + product.getCode() + " in online catalog"));

        throwExceptionIfErrorsExist(appPermissionService.verifyAppCanBeViewedByIntegrator(app), app.getCode());
    }

    protected void throwExceptionIfErrorsExist(Set<ErrorMessageData> errorMessageData, String code) {
        if (!CollectionUtils.isEmpty(errorMessageData)) {
            LOG.error("Permission denied to view app with code {}, errors={}", code, errorMessageData);
            throw new AppPermissionException("App not found");
        }
    }

}
