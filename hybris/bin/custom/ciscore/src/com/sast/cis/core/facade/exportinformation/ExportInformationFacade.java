package com.sast.cis.core.facade.exportinformation;

import com.sast.cis.core.data.ExportInformationEntryData;
import com.sast.cis.core.model.AppVersionModel;
import com.sast.cis.core.service.AppService;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ExportInformationFacade {

    @Resource
    private AppService appService;

    @Resource
    private Converter<AppVersionModel, ExportInformationEntryData> exportInformationEntryDataConverter;

    public List<ExportInformationEntryData> getExportInformationEntryDatas() {
        return appService.getLatestVersionWithEccnForAllApps().stream()
            .map(exportInformationEntryDataConverter::convert)
            .collect(Collectors.toList());
    }
}
