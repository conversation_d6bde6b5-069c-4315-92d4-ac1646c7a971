package com.sast.cis.core.constants;

import de.hybris.platform.store.BaseStoreModel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

@RequiredArgsConstructor
@Getter
public enum BaseStoreEnum {
    AZENA("iotstore"),
    AA("aastore");

    private final String baseStoreUid;

    public static Optional<BaseStoreEnum> fromBaseStoreUid(final String baseStoreUid) {
        return Arrays.stream(BaseStoreEnum.values())
            .filter(baseStore -> Objects.equals(baseStore.getBaseStoreUid(), baseStoreUid))
            .findFirst();
    }

    public static BaseStoreEnum fromBaseStore(@NonNull final BaseStoreModel baseStoreModel) {
        return Arrays.stream(BaseStoreEnum.values())
            .filter(baseStore -> Objects.equals(baseStore.getBaseStoreUid(), baseStoreModel.getUid()))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Unknown base store %s".formatted(baseStoreModel.getUid())));
    }
}
