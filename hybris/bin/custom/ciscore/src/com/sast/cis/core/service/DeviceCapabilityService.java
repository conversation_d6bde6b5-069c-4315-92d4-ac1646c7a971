package com.sast.cis.core.service;

import com.sast.cis.core.dao.DeviceCapabilityDao;
import com.sast.cis.core.model.DeviceCapabilityModel;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.site.BaseSiteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeviceCapabilityService {
    private final ModelService modelService;
    private final HtmlSanitizingService htmlSanitizingService;
    private final DeviceCapabilityDao deviceCapabilityDao;
    private final CommonI18NService commonI18NService;
    private final BaseSiteService baseSiteService;
    private final ConfigurationService configurationService;

    @Autowired
    public DeviceCapabilityService(ModelService modelService, HtmlSanitizingService htmlSanitizingService,
            DeviceCapabilityDao deviceCapabilityDao, CommonI18NService commonI18NService,
            BaseSiteService baseSiteService, ConfigurationService configurationService) {
        this.modelService = modelService;
        this.htmlSanitizingService = htmlSanitizingService;
        this.deviceCapabilityDao = deviceCapabilityDao;
        this.commonI18NService = commonI18NService;
        this.baseSiteService = baseSiteService;
        this.configurationService = configurationService;
    }

    public Set<DeviceCapabilityModel> getOrCreateDeviceCapabilities(List<String> codes) {
        return codes.stream()
                .map(htmlSanitizingService::sanitizeInput)
                .map(code -> deviceCapabilityDao.getDeviceCapabilityForTechnicalCode(code).orElseGet(() -> createDeviceCapability(code)))
                .collect(Collectors.toSet());
    }

    private DeviceCapabilityModel createDeviceCapability(String code) {
        DeviceCapabilityModel deviceCapability = modelService.create(DeviceCapabilityModel.class);
        deviceCapability.setCode(code);
        modelService.save(deviceCapability);
        return deviceCapability;
    }

    public String getDisplayName(DeviceCapabilityModel deviceCapability) {
        if (StringUtils.isNotBlank(deviceCapability.getDisplayName())) {
            return deviceCapability.getDisplayName();
        }

        Locale defaultLocale = commonI18NService.getLocaleForLanguage(baseSiteService.getCurrentBaseSite().getDefaultLanguage());
        if (StringUtils.isNotBlank(deviceCapability.getDisplayName(defaultLocale))) {
            return deviceCapability.getDisplayName(defaultLocale);
        }

        LOG.warn("No display name found for the Device Capability with code: {}", deviceCapability.getCode());
        return deviceCapability.getDisplayName();
    }

    public int getMaxNumberOfDeviceCapabilities() {
        return configurationService.getConfiguration().getInt("apk.max.number.of.device.capabilities", 100);
    }

    public String getCode(DeviceCapabilityModel deviceCapability) {
        if (StringUtils.isNotEmpty(deviceCapability.getCode())) {
            return deviceCapability.getCode();
        }

        LOG.warn("No device capabilities found for the code: {}", deviceCapability.getCode());
        return deviceCapability.getCode();
    }
}
