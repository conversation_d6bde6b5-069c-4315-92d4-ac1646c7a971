package com.sast.cis.core.service;

import com.sast.cis.core.model.NavigationItemModel;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.session.SessionService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.sast.cis.core.i18n.StoreI18nConstants.SELECTED_COUNTRY_SESSION_ATTR_KEY;

@Service
@Slf4j
@RequiredArgsConstructor
public class NavigationUrlInterpolator {
    private static final String COUNTRY_LOWERCASE_VARIABLE = "countryCode_lower";
    private static final String LANGUAGE_VARIABLE = "languageCode";

    private final SessionService sessionService;
    private final CommonI18NService commonI18NService;

    /**
     * Returns URL of the given navigation item with eventual variable interpolation.
     *
     * If the given NavigationItem has interpolatedUrl=false or its url is null, the value of the url attribute is returned as-is.
     * If the given NavigationItem has interpolatedUrl=true, the following variables are substituted:<br/>
     * <table>
     *     <tr><th>Variable             </th><th>Example Value  </th><th>Description        </th></tr>
     *     <tr><td>languageCode         </td><td>at             </td><td>ISO-2 language code of the current user language</td></tr>
     *     <tr><td>countryCode_lower    </td><td>de             </td><td>ISO country code of the currently selected country, converted to lowercase</td></tr>
     * </table>
     *
     * Variables are prefixed with '${' and are delimited by the closing curly brace '}', e.g. '${languageCode}'.<br/>
     * Default values can be defined as follows: '${languageCode:-en}' - In this case the default value 'en' would be used if the languageCode
     * could not be determined. <br/>
     * If a variable value is undetermined and the given URL has no default defined, the variable pattern passes through unchanged.
     *
     * @param navigationItem
     * @return navigation item URL with eventually performed variable substitution
     * @throws IllegalArgumentException if given navigationItem is null
     */
    public String createUrl(@NonNull NavigationItemModel navigationItem) {
        if (navigationItem.getUrl() == null || !navigationItem.isInterpolatedUrl()) {
            return navigationItem.getUrl();
        }
        return StringSubstitutor.replace(navigationItem.getUrl(), createSubstitutionContext());
    }

    private Optional<String> getSessionCountry() {
        final String sessionCountry = sessionService.getAttribute(SELECTED_COUNTRY_SESSION_ATTR_KEY);
        if (StringUtils.isNotBlank(sessionCountry)) {
            return Optional.of(sessionCountry);
        }
        return Optional.empty();
    }

    private Optional<String> getSessionLanguage() {
        final LanguageModel languageModel = commonI18NService.getCurrentLanguage();
        if (languageModel != null && StringUtils.isNotBlank(languageModel.getIsocode())) {
            return Optional.of(languageModel.getIsocode());
        }
        return Optional.empty();
    }

    private Map<String, String> createSubstitutionContext() {
        final Map<String, String> substitutionContext = new HashMap<>();
        getSessionCountry().ifPresent(countryCode ->
            substitutionContext.put(COUNTRY_LOWERCASE_VARIABLE, StringUtils.lowerCase(countryCode)));
        getSessionLanguage().ifPresent(languageCode ->
            substitutionContext.put(LANGUAGE_VARIABLE, languageCode));
        return substitutionContext;
    }
}
