package com.sast.cis.core.converter.appediting.availability;

import com.sast.cis.core.data.LicenseAvailabilityData;
import com.sast.cis.core.enums.LicenseAvailabilityStatus;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseDraftModel;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
@AllArgsConstructor
public class AppLicenseDraft2LicenseAvailabilityDataPopulator implements Populator<AppLicenseDraftModel, LicenseAvailabilityData> {

    @Override
    public void populate(AppLicenseDraftModel licenseDraft, LicenseAvailabilityData licenseAvailabilityData) {
        if (licenseDraft == null) {
            throw new ConversionException("Given AppLicenseDraftModel is null");
        }
        if (licenseAvailabilityData == null) {
            throw new ConversionException("Given LicenseAvailabilityData is null");
        }

        licenseAvailabilityData.setType(licenseDraft.getLicenseType());
        licenseAvailabilityData.setEditable(isEditable(licenseDraft));
        licenseAvailabilityData.setEnabled(LicenseAvailabilityStatus.PUBLISHED.equals(licenseDraft.getAvailabilityStatus()));
    }

    private boolean isEditable(AppLicenseDraftModel appLicenseDraft) {
        LicenseType licenseType = appLicenseDraft.getLicenseType();

        if (LicenseType.EVALUATION.equals(licenseType)) {
            return true;
        }

        if (appLicenseDraft.getCountriesAndPricesDraft() == null) {
            return false;
        }

        if (Set.of(LicenseType.FULL, LicenseType.SUBSCRIPTION).contains(licenseType)) {
            return appLicenseDraft.getSpecifiedPrice() != null;
        }

        return false;
    }
}
