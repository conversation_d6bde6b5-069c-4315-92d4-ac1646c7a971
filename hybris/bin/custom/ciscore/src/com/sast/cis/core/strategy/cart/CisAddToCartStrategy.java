package com.sast.cis.core.strategy.cart;

import com.sast.cis.core.factory.CisCartFactory;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.CisCartService;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import de.hybris.platform.commerceservices.order.CommerceCartModification;
import de.hybris.platform.commerceservices.order.CommerceCartModificationException;
import de.hybris.platform.commerceservices.order.impl.DefaultCommerceAddToCartStrategy;
import de.hybris.platform.commerceservices.service.data.CommerceCartParameter;
import de.hybris.platform.core.model.order.CartModel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import static de.hybris.platform.servicelayer.util.ServicesUtil.validateParameterNotNull;

@Slf4j
public class CisAddToCartStrategy extends DefaultCommerceAddToCartStrategy implements CartStrategySupport {
    @Getter
    private final AppLicenseService appLicenseService;
    private final CisCartService cisCartService;
    private final CisCartFactory cisCartFactory;
    private final CisCartValidator cisCartValidator;
    private final IntegratorService integratorService;

    @Autowired
    public CisAddToCartStrategy(AppLicenseService appLicenseService,
        CisCartService cisCartService,
        CisCartFactory cisCartFactory,
        CisCartValidator cisCartValidator,
        IntegratorService integratorService) {
        this.appLicenseService = appLicenseService;
        this.cisCartService = cisCartService;
        this.cisCartFactory = cisCartFactory;
        this.cisCartValidator = cisCartValidator;
        this.integratorService = integratorService;
    }

    @Override
    protected void validateAddToCart(CommerceCartParameter parameters) throws CommerceCartModificationException {
        super.validateAddToCart(parameters);

        cisCartValidator.validate(parameters);
    }

    @Override
    public CommerceCartModification addToCart(final CommerceCartParameter parameter) throws CommerceCartModificationException {
        final CartModel cart = parameter.getCart();
        validateParameterNotNull(cart, "Cart cannot be null");

        return super.addToCart(handleMultiCart(parameter));
    }

    private CommerceCartParameter handleMultiCart(CommerceCartParameter parameter) throws CommerceCartModificationException {
        IntegratorModel currentUser = integratorService.getCurrentIntegrator();
        AppLicenseModel appLicense = (AppLicenseModel) parameter.getProduct();
        IoTCompanyModel sellerCompany = getSellerCompany(parameter.getProduct());

        CartModel cart = cisCartService.getCartByUserDeveloperCompanyAndLicense(currentUser, sellerCompany, appLicense.getLicenseType())
            .orElseGet(cisCartFactory::createCart);

        cisCartService.setSessionCart(cart);
        parameter.setCart(cart);
        return parameter;
    }
}
