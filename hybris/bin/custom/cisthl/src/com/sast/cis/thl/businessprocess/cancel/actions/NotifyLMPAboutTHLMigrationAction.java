package com.sast.cis.thl.businessprocess.cancel.actions;

import com.sast.cis.core.model.SubscriptionContractModel;
import com.sast.cis.orderprocess.service.OrderReconfigurationNotifier;
import com.sast.cis.thl.model.EsiMasterTHLMigrationProcessModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class NotifyLMPAboutTHLMigrationAction extends AbstractSimpleDecisionAction<EsiMasterTHLMigrationProcessModel> {

    private final OrderReconfigurationNotifier orderReconfigurationNotifier;

    @Override
    public Transition executeAction(final EsiMasterTHLMigrationProcessModel process) {
        final OrderModel order = process.getOrder();
        final SubscriptionContractModel subscriptionContract = process.getSubscriptionContract();
        try {
            orderReconfigurationNotifier.notifyAboutOrderReconfiguration(order, subscriptionContract);
            return Transition.OK;
        } catch (final Exception e) {
            LOG.error("ALERT: Could not send notification for Order {}. Unexpected error occurred.", order.getCode(), e);
            return Transition.NOK;
        }
    }
}
