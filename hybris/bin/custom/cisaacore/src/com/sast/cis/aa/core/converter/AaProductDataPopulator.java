package com.sast.cis.aa.core.converter;

import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import de.hybris.platform.variants.model.VariantProductModel;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class AaProductDataPopulator implements Populator<ProductModel, ProductData> {

    @Override
    public void populate(ProductModel source, ProductData target) throws ConversionException {
        String sellerProductId = source.getVariants().stream()
            .map(VariantProductModel::getSellerProductId)
            .filter(Objects::nonNull)
            .findFirst().orElse("");
        target.setSellerProductId(sellerProductId);
    }
}
