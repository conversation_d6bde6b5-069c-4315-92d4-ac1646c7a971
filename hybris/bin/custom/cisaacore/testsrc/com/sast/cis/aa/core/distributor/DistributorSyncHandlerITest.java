package com.sast.cis.aa.core.distributor;

import com.sast.cis.core.model.AaDistributorCompanyModel;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.com.sast.cis.core.model.AaDistributorCompanyBuilder;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class DistributorSyncHandlerITest extends ServicelayerTransactionalTest {
    private static final String AUSTRIA = "AT";

    @Resource
    private DistributorSyncHandler distributorSyncHandler;

    @Resource
    private CommonI18NService commonI18NService;

    @Resource
    private ModelService modelService;

    private CountryModel country;

    @Before
    public void setUp() {
        country = commonI18NService.getCountry(AUSTRIA);
    }

    @Test
    public void syncDistributors_does_successfully() {
        distributorSyncHandler.syncDistributors(AUSTRIA);
        assertThat(country.getAaDistributorCompanies()).hasSize(15);
    }

    @Test
    public void syncDistributors_deletes_not_in_ump_successfully() {
        AaDistributorCompanyModel aaDistributorCompany1 = AaDistributorCompanyBuilder.generate()
            .withCompanyName("Keep me")
            .withUmpId("2")
            .withCountries(Set.of(country))
            .buildIntegrationInstance();
        AaDistributorCompanyModel aaDistributorCompany2 = AaDistributorCompanyBuilder.generate()
            .withCompanyName("Delete me")
            .withUmpId("1")
            .withCountries(Set.of(country))
            .buildIntegrationInstance();

        country.setAaDistributorCompanies(Set.of(aaDistributorCompany1, aaDistributorCompany2));
        modelService.save(country);
        assertThat(country.getAaDistributorCompanies()).hasSize(2);

        distributorSyncHandler.syncDistributors(AUSTRIA);

        assertThat(country.getAaDistributorCompanies()).hasSize(15);
        assertThat(country.getAaDistributorCompanies()).doesNotContain(aaDistributorCompany1);
        assertThat(country.getAaDistributorCompanies()).doesNotContain(aaDistributorCompany2);
    }

    @Test
    public void whenSyncDistributors_thenUpdateFieldsForExisting() {
        final String oldExternalId = "old_external_id";
        final AaDistributorCompanyModel aaDistributorCompany1 = AaDistributorCompanyBuilder.generate()
            .withUmpId("0b25ab85-7004-11ed-8422-02bf5080765c")
            .withCompanyName("Austria Distributor 1")
            .withAaExternalId(oldExternalId)
            .withCountries(Set.of(country))
            .buildIntegrationInstance();
        country.setAaDistributorCompanies(Set.of(aaDistributorCompany1));
        modelService.save(country);

        distributorSyncHandler.syncDistributors(AUSTRIA);

        modelService.refresh(aaDistributorCompany1);
        assertThat(country.getAaDistributorCompanies()).hasSize(15);
        assertThat(country.getAaDistributorCompanies()).contains(aaDistributorCompany1);
        assertThat(aaDistributorCompany1.getAaExternalId()).isEqualTo("35340311");
    }
}
