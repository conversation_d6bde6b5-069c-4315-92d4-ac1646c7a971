#!/usr/bin/env python3

import argparse
import os
import signal
import subprocess
import sys

import build_tools


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--watch', action='store_true')
    parser.add_argument('--production', action='store_true')
    parser.add_argument('--force-init', action='store_true', help='Delete node_modules folder and run initialize script.')
    parser.add_argument('--skip-typedef-creation', dest='build_types', action='store_false')
    parser.add_argument('--init-only', action='store_true')
    parser.add_argument('--fix-eslint', action='store_true', help='Enable ESLint auto fix feature, use with caution since it modifies source files!')
    parser.add_argument('--build-aa', action='store_true', help='Builds frontend for AA!')

    args = parser.parse_args()

    if args.build_aa:
        print('AA Frontend JS build started..')
    else:
        print('Frontend JS build started..')

    if args.build_types:
        status = build_tools.build_typedefs()
        if status != 0:
            sys.exit(status)

    build_tools.init_frontend_environment(args.force_init)
    if args.init_only:
        sys.exit(0)

    if args.build_aa:
        build_cmd = ['node', './node_modules/.bin/webpack', '--config', os.path.join(os.getcwd(), 'iot_store/webpack.aa.config.js')]
    else:
        build_cmd = ['node', './node_modules/.bin/webpack', '--config', os.path.join(os.getcwd(), 'iot_store/webpack.config.js')]
    if args.watch:
        build_cmd += ['--watch']
    if args.production:
        build_cmd += ['--env', 'mode=production']
    else:
        build_cmd += ['--env', 'mode=development']
    if args.fix_eslint:
        build_cmd += ['--env', 'fix']

    process = subprocess.Popen(build_cmd, cwd=os.path.join(os.getcwd(), 'iot_store'))
    try:
        process.communicate()
    except KeyboardInterrupt:
        process.send_signal(signal.SIGINT)
    sys.exit(process.returncode)


if __name__ == '__main__':
    main()
