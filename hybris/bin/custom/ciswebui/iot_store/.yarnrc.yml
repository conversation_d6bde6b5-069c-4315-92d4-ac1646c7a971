compressionLevel: mixed

enableGlobalCache: false

enableStrictSsl: true

nodeLinker: node-modules

npmAuditIgnoreAdvisories:
  - 1080885 # GHSA-p3j5-c53x-9ph8: Vulnerability refers to a different cd-system package.
  - 1092310 # GHSA-c2qf-rxjj-qqgw: Transitive dependency. Not used to process untrusted user data.

npmAuditExcludePackages:
  - vue-i18n # v8 is EOL, v9 is incompatible with vue 2. we may be able to switch to vue-i18n-bridge completely
  - vuetify  # v2 is EOL, yay

npmAuditRegistry: "https://registry.npmjs.org"

npmRegistries:
  //nexus.ci.sastdev.net/repository/npm-all/:
    npmAlwaysAuth: true

npmRegistryServer: "https://nexus.ci.sastdev.net/repository/npm-all"

npmScopes:
  neverendingsupport:
    npmAlwaysAuth: true
    npmRegistryServer: "https://registry.nes.herodevs.com/npm/pkg/"

unsafeHttpWhitelist:
  - registry.nes.herodevs.com

yarnPath: .yarn/releases/yarn-4.6.0.cjs
