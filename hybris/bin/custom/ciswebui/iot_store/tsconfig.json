{"compilerOptions": {"moduleResolution": "node", "sourceMap": true, "module": "es2015", "target": "es5", "allowJs": false, "experimentalDecorators": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "noImplicitAny": true, "strict": true, "strictBindCallApply": true, "baseUrl": ".", "paths": {"*": ["./src/js/*", "./node_modules/*"], "@/*": ["./src/*"]}, "lib": ["DOM", "ES5", "ScriptHost", "ES2016", "es2018.promise", "es2021"], "esModuleInterop": true, "typeRoots": ["./node_modules/@types"], "types": ["vuetify", "node", "cd-system", "jest"]}, "include": ["./src/js/**/*.ts", "./src/js/**/*.vue", "./src/js/common/types.ts", "./src/js/common/generated-types/types", "./types/**/*.d.ts"], "exclude": ["node_modules", "**/__mocks__/*.ts", "**/*.test.ts"], "files": ["vue-shim.d.ts"]}