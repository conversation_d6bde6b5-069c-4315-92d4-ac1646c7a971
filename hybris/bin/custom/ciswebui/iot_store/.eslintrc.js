module.exports = {
    'parser': 'vue-eslint-parser',
    'parserOptions': {
        'parser': '@typescript-eslint/parser'
    },
    extends: [
        'standard-with-typescript',
        'eslint:recommended',
        'plugin:vue/essential',
        'plugin:@typescript-eslint/recommended',
        'plugin:@intlify/vue-i18n/recommended',
        'prettier'
    ],
    rules: {
        '@intlify/vue-i18n/no-dynamic-keys': 'off',
        '@intlify/vue-i18n/no-missing-keys': 'error',
        '@intlify/vue-i18n/no-raw-text': 'off',
        '@intlify/vue-i18n/no-v-html': 'off'
    },
    overrides: [{
        files: [
            '*.js',
            '*.vue',
        ],
        rules: {
            'vue/require-v-for-key': 'off',  // Enable this if this is implemented: https://github.com/vuejs/eslint-plugin-vue/issues/522
            'no-console': 'off',
            'no-eval': 'error',
            '@typescript-eslint/no-non-null-assertion': 'off',
            'lines-between-class-members': 'off',
            'consistent-this': ['error', 'that'],
            'semi': ['error', 'always'],
            'quotes': ['error', 'single', {'avoidEscape': true, 'allowTemplateLiterals': true}],
            'indent': 'off',
            'require-jsdoc': 'off',
            'vue/html-self-closing': ['error', {
                'html': {
                    'void': 'always',
                    'normal': 'never',
                    'component': 'never'
                },
                'svg': 'any',
                'math': 'any'
            }],
            'vue/html-end-tags': 'error',
            'vue/html-quotes': ['error', 'double'],
            'vue/no-template-shadow': 'error',
            'vue/require-prop-types': 'error',
            'vue/this-in-template': ['error', 'never']
        }
    },
    {
        files: ['*.ts'],
        rules: {
            'no-console': 'off',
            'no-eval': 'error',
            '@typescript-eslint/no-non-null-assertion': 'off',
            'lines-between-class-members': 'off',
            'consistent-this': ['error', 'that'],
            'semi': ['error', 'always'],
            'quotes': ['error', 'single', {'avoidEscape': true, 'allowTemplateLiterals': true}],
            'indent': 'off',
            'require-jsdoc': 'off',
            },
        parserOptions: {
            project: ['tsconfig.json'],
        }
    },
    {
        files: ['*.unit.test.js', '*.ui.test.js', '*.unit.aa.test.js', '*.ui.aa.test.js', '*/js/common/testtools/*'],
        env: {
            'jest': true
        },
        globals: {
            page: true,
            browser: true,
            context: true
        }
    }
    ],
    settings: {
        'vue-i18n': {
            localeDir: './src/js/**/i18n/*.json'
        }
    },
    reportUnusedDisableDirectives: true
};
