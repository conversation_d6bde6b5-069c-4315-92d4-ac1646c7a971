import NodeEnvironment from 'jest-environment-node';
import fs from 'fs';
import os from 'os';
import path from 'path';
import puppeteer from 'puppeteer';
import chalk from 'chalk';

const DIR = path.join(__dirname, 'test_output');
const PORT = 9010;

const createPage = async function (context) {
    const page = await context.newPage();
    await page.setViewport({width: 1200, height: 700});
    page.on('console', msg => {
        if (msg.type() === 'error') {
            if (msg.location() && msg.location().url) {
                console.log(chalk.redBright('BROWSER ERROR:'), msg.text(), msg.location().url);
            } else {
                console.log(chalk.redBright('BROWSER ERROR:'), msg.text());
                throw new Error(`BROWSER ERROR: ${msg.text()}`);
            }
        } else {
            console.log(chalk.cyan('BROWSER INFO:'), msg.text());
        }
    });

    return page;
};

// A reference implementation can be found at: https://github.com/smooth-code/jest-puppeteer/blob/master/packages/jest-environment-puppeteer/src/PuppeteerEnvironment.js
class PuppeteerEnvironment extends NodeEnvironment {
    constructor({globalConfig, projectConfig}, context) {
        super({globalConfig, projectConfig}, context);
    }

    async setup() {
        await super.setup();

        const RUNNING_IN_IDE = process.env.RUNNING_TESTS_FROM_CMD === undefined;
        if (this.global.__BROWSER_GLOBAL__ === false) {
            return;
        }

        let endPoints;
        if (process.env.slow_mo || RUNNING_IN_IDE) {
            endPoints = JSON.parse(fs.readFileSync(path.join(DIR, 'wsEndpoint-non-docker'), 'utf8'));
        } else {
            endPoints = JSON.parse(fs.readFileSync(path.join(DIR, 'wsEndpoint-docker'), 'utf8'));
        }

        if (!endPoints) {
            throw new Error('wsEndpoint not found');
        }

        this.global.browser = await puppeteer.connect({
            browserWSEndpoint: endPoints.wsEndpoint,
        });

        if (process.env.slow_mo || RUNNING_IN_IDE) {
            this.global.__DEV_ADDRESS__ = `http://localhost:${PORT}`;
        } else if (os.platform() === 'win32' || os.platform() === 'darwin') {
            this.global.__DEV_ADDRESS__ = `http://host.docker.internal:${PORT}`;
        } else {
            this.global.__DEV_ADDRESS__ = `http://**********:${PORT}`;
        }
        console.log('dev server address: ' + this.global.__DEV_ADDRESS__);

        this.global.context = await this.global.browser.browserContexts()[0];
        this.global.page = await createPage(this.global.context);

        this.global.resetPage = async () => {
            if (this.global.page) {
                await this.global.page.close();
            }

            this.global.page = await createPage(this.global.context);
        };
    }

    async teardown() {
        if (this.global.page !== undefined) {
            await this.global.page.close();
        }
        if (this.global.browser !== undefined) {
            await this.global.browser.disconnect()
        }

        await super.teardown();
    }
}

module.exports = PuppeteerEnvironment;
