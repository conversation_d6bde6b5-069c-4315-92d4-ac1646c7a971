class RetryReporter {
    recursiveRetryReporter(testResults) {
        let retries = 0;

        for (let result of testResults) {
            if (result.testResults) {
                retries += this.recursiveRetryReporter(result.testResults);
            }

            if (result.invocations && result.invocations > 1) {
                console.log(' ' + (result.invocations - 1) + ' | ' + result.fullName);
                retries += 1;
            }
        }

        return retries;
    }

    onRunComplete(contexts, results) {
        console.log('\nRetry summary:');
        console.log('   | test name');
        let totalRetriedTests = this.recursiveRetryReporter(results.testResults);
        console.log('\n' + totalRetriedTests + ' test(s) required a retry.')
    }
}

module.exports = RetryReporter; 
