const {exec} = require('child_process');

module.exports = async function (config) {
    if(global.__BROWSER_GLOBAL__ === false){
        console.log('Skipping browser tear down.')
    }
    else if (global.__BROWSER_GLOBAL__ !== undefined) {
        await global.__BROWSER_GLOBAL__.close();
    } else if (!(config.watch || config.watchAll)) {
        // In watch mode, teardown of chrome/docker is handled from run_tests script to improve performance.
        exec('docker stop puppeteer-chrome', (err, stdout, stderr) => {
            if (err) {
                console.error('Could not find docker stop command.');
                console.log(err);
                return;
            }

            if (stderr) {
                console.error('Failed to stop puppeteer-chrome docker container.');
                console.log(stderr);
            }
        });
    }
};
