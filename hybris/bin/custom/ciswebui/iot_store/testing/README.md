# Testing

For testing, all files in ciswebui/src/js of these types are auto detected:

* `*.unit.test.js` : Unit test file, executed by Jest test runner, has access to DOM via jsdom
* `*.ui.test.js` : UI test file, executed by Jest test runner, has access to a real browser via Puppeteer API
* `*.testpage.js`: Creates a test page, executed by webpack serve

For AA pages use different pattern

*    `*.ui.aa.test.js` : UI test file, executed by Jest test runner, has access to a real browser via Puppeteer API
*    `*.aa.testpage.js`: Creates a test page, executed by webpack serve


## Unit Tests

A unit test is meant to test the basic functionality of a single file component. Common test scenarios are e.g.

*    verify that a clicked button triggers an event with certain parameters
*    verify that a certain dynamic css class is assigned to an element
*    verify that the correct data is set

How to add new tests:

*    Start test runner via shell script `ciswebui/run_tests` with "–watch" option
*    Create a new unit test:
* * Any `*.unit.test.js` file in "src/js" will be auto detected by the Jest test runner and is a test suite
  * Use Vue Test Utils to mock vue components. Especially, mount() can be helpful. Jest provides the expect function to check desired behavior.
  * Jest provides manual mocks to mock whole modules like util.ts. The mock implementation of a module is located in the __mocks__ directory.
  * An actual implementation of a function can be exchanged with Jest's mock functions
* Check if your test passes

## UI Tests

UI tests should check UI and UX. Common test scenarios are e.g.:

* UI and layout persistence → screenshots to check if it still looks the same and if still all strings/labels are rendered and present
* UX persistence → browser events (scroll/click/file upload/..) to check if user interaction flows still work

How to add new tests:

1. Start tests:
```
# in ciswebui:
./run_tests --watch
```

in case of AA
```
# in ciswebui:
./run_tests --aa --watch
```

If you get error "ENOSPC ": There are too many open file handlers: For a solution, see e.g. https://stackoverflow.com/questions/22475849/node-js-error-enosp
2. Create test data by creating a test page
   * Any *.testpage.js in "src/js" creates a test page and becomes the corresponding index.ts file).
   * Example: A file "src/js/mycomponent/myscenario.testpage.js" will create a test page accessible in the UI tests at "/mycomponent-myscenario".
   * The style.css is always automatically included.
3. Create a UI test
   * Any `*.ui.test.js` in "src/js" is a test suite.
   * Use the Puppeteer API to navigate to your test page and verify its content via Jest expect.
   * Use the jest-image-snapshot plugin to verify that the ui looks as expected. Persist a reference snapshot in the ciswebui/iot_store/testing/reference_screenshots directory. The name of the screenshot is derived by the name of your test.
4. Check if your test passes
5. For AA UI tests use AA naming patterns `*.ui.aa.test.js` and `*.aa.testpage.js`
