const util = {
    axios: {
        post: jest.fn(() => Promise.resolve({
            status: 204,
            statusText: 'OK',
        })),
    },
    isElementInViewport: jest.fn(),
    isElementInOrAboveViewport: jest.fn(),
    lockScrolling: jest.fn(),
    unlockScrolling: jest.fn(),
    loadLogoImage: jest.fn(),
    scriptLoader: jest.fn(),
    isDACHRegion: jest.fn((countryISOCode) => {
        if (!countryISOCode) {
            return false;
        }
        const DACH_ISO_CODES = ['DE', 'AT', 'CH']; // Germany, Austria, Switzerland
        return DACH_ISO_CODES.includes(countryISOCode.toUpperCase());
    }),
};

export default util;