<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/scss" media="all" href="/ciswebui/iot_store/testing/assets/overview.scss">
</head>
<body>
<div class="main-frame">
    <h1>LOCAL DEV SERVER</h1>
    <p>Inline test page creator: <a href="/vue-app">go here</a></p>
    <h2>Test pages overview</h2>
    <div>
        <% for(const page of pages){ %>
        <% if(page.ts) { %>
        <li>TS: <a href="<%= page.url%>"><%= page.title %></a></li>
        <% } else { %>
        <li>JS: <a href="<%= page.url%>"><%= page.title %></a></li>
        <% } %>
        <% } %>
    </div>
</div>
</body>
</html>
