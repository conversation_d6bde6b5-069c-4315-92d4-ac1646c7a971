/**
 * This file contains sample command to debug/verify if puppeteer/chrome can connect the dev server.
 * It can be run either locally our from within the docker container.
 * It can be run as a script (type: node <script_name>.js) or interactively by typing node and
 * all commands manually.
 * To enter the docker container use can use: docker run -ti puppeteer-chrome /bin/bash
 */

const puppeteer = require('puppeteer');

let WS_END_POINT_FILE = '/test_output/wsEndpoint'; // location within docker container

let endPoints = JSON.parse(fs.readFileSync(WS_END_POINT_FILE, 'utf8'));

let port = endPoints.devServerPort || 9011;
let server = 'host.docker.internal:' + port;

let browser;
let connect = async function() {
    browser = await puppeteer.connect({
        browserWSEndpoint: endPoints.wsEndpoint,
    });
};

let context;
let getContext = async function() { context = await browser.browserContexts()[0] };

let page;
let newPage = async function() { page = await context.newPage() };

let goto = async function() { await page.goto(server + '/') };

let getTitle = async function() { console.log(await page.title()) };

let verifyConnection = async function() {
    await connect();
    await getContext();
    await newPage();
    await goto();
    await getTitle();
};

verifyConnection();