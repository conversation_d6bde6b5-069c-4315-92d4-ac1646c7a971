const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const CHROME_PORT = 43323;
const DIR = path.join(__dirname, '../test_output');

let browser;
let puppeteerConfig = {
    args: ['--no-sandbox',
        '--window-size=1400,800',
        '-remote-debugging-address=0.0.0.0',
        `--remote-debugging-port=${CHROME_PORT}`,
        '--disable-dev-shm-usage'],
    defaultViewport: {
        width: 800,
        height: 600
    }
};

launcher = async function () {
    browser = await puppeteer.launch(puppeteerConfig);

    let endPoints = {
        wsEndpoint: browser.wsEndpoint()
    };

    fs.writeFileSync(path.join(DIR, 'wsEndpoint-docker'), JSON.stringify(endPoints));
};

launcher();
console.log('Browser is running!');
