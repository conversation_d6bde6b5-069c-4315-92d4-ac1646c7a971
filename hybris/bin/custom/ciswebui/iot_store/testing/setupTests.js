import Vue from 'vue';
import Vuetify from 'vuetify';
Vue.config.productionTip = false;
Vue.use(Vuetify);

const path = require('path');
const kebabCase = require('lodash/kebabCase');
const {configureToMatchImageSnapshot} = require('jest-image-snapshot');

const originalSnapshotFunc = configureToMatchImageSnapshot({
    customSnapshotsDir: path.resolve('testing/reference_screenshots')
});

const getSnapshotIdentifier = function (value) {
    if (value === undefined) {
        throw new Error("Specify a snapshot identifier.");
    }

    if (typeof value === "string") {
        return value
    }
    return value.customSnapshotIdentifier;
};

const toMatchImageSnapshot = function () {
    let identifier = kebabCase(`${path.basename(this.testPath)}-${this.currentTestName}-${getSnapshotIdentifier(arguments[1])}`);

    let customConfig = {customSnapshotIdentifier: identifier};

    if (process.env.RUNNING_TESTS_FROM_CMD === undefined) {
        // skip screenshot tests when running from the IDE
        let skipMsg = `Running tests from IDE: Skipping screenshot test for ${identifier}`;
        console.log(skipMsg);
        return {
            message: () => {
                return skipMsg
            }, pass: true
        };
    } else if (process.env.strict) {
        // discard adjusted tolerance in strict mode -> zero tolerance == strict
        return originalSnapshotFunc.apply(this, [arguments[0], customConfig]);
    } else if (process.env.percent_tolerance) {
        customConfig.failureThresholdType = 'percent';
        customConfig.failureThreshold = process.env.percent_tolerance;
    } else if (process.env.pixel_tolerance) {
        customConfig.failureThresholdType = 'pixel';
        customConfig.failureThreshold = parseInt(process.env.pixel_tolerance, 10);
    } else if (arguments[1] && arguments[1].failureThreshold) {
        customConfig.failureThresholdType = arguments[1].failureThresholdType;
        customConfig.failureThreshold = arguments[1].failureThreshold;
    }

    return originalSnapshotFunc.apply(this, [arguments[0], customConfig]);
};

expect.extend({toMatchImageSnapshot});

if (process.env.strict === undefined && process.env.RUNNING_TESTS_FROM_CMD !== undefined) {
    jest.retryTimes(6, {logErrorsBeforeRetry: true});
}

if (process.env.RUNNING_TESTS_FROM_CMD) {
    jest.setTimeout(3 * 60 * 1000); // 3 min
} else {
    jest.setTimeout(5 * 60 * 1000); // 5 min
}

global.window = {};
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

global.beforeEach(async () => {
    if (global.resetPage) {
        await global.resetPage();
    }
});
