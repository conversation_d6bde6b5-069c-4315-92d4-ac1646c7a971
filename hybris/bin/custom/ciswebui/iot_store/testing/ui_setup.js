const puppeteer = require('puppeteer');
const mkdirp = require('mkdirp');
const path = require('path');
const fs = require('fs');
const {exec} = require('child_process');
const net = require('net');

const PORT = 9010;
const DIR = path.join(__dirname, 'test_output');
const SLOW_MO_DEFAULT = 500;

let start_slow_mo_chrome_with_open_window = async (slow_mo) => {
    let browser = await puppeteer.launch({
        args: ['--no-sandbox', '--window-size=1400,800', '--disable-dev-shm-usage'],
        defaultViewport: {
            width: 1000,
            height: 800
        },
        slowMo: parseInt(slow_mo, 10),
        headless: false
    });

    let endPoints = {
        wsEndpoint: browser.wsEndpoint(),
    };

    // expose wsEndpoint via file system for PuppeteerEnvironment class
    mkdirp.sync(DIR);
    fs.writeFileSync(path.join(DIR, 'wsEndpoint-non-docker'), JSON.stringify(endPoints));

    return browser;
};


let get_self_container = function() {
    return new Promise((resolve, reject) => {
        exec(`docker ps -q -f "label=com.gitlab.gitlab-runner.job.id=${process.env.CI_JOB_ID}" -f "label=com.gitlab.gitlab-runner.type=build"`, (err, stdout, stderr) => {
            if (err) {
                console.log(err);
                return;
            }
        console.log(`Container ID: ${stdout}`);
        resolve(stdout.trimRight());
        });
    });
}

let start_chrome_in_docker = async () => {
    const container_id = await get_self_container();

    let args = ['docker',
        'run',
        '-p=43323:43323',
        '--rm',
        '--detach',
    container_id? `--volumes-from=${container_id}` : `-v=${__dirname}/:/builds/SAST/store/iot_store/hybris/bin/custom/ciswebui/iot_store/testing`,
    `-w /builds/SAST/store/iot_store/hybris/bin/custom/ciswebui/iot_store/testing`,
        '--name=puppeteer-chrome',
        'docker.sastdev.net/sast/ci/puppeteer-chrome',
        'node docker/launcher.js'].join(' ');

    console.log(args);

    await exec(args, (err, stdout, stderr) => {
        if (err) {
            console.error('Failed to run browser in docker container.');
            console.log(err);
            return;
        }

        console.log(`\nchrome stdout: ${stdout}`);
        console.log(`chrome stderr: ${stderr}\n`);
    });
    await checkForFileContent(path.join(DIR, 'wsEndpoint-docker'), 3000);
};

let sleep = function (ms) {
    return new Promise(resolve => {
        setTimeout(resolve, ms)
    })
};

let probeConnection = function (port, host, timeout) {
    return new Promise((resolve => {
        const socket = new net.Socket();

        const onError = () => {
            socket.destroy();
            resolve(false);
        };

        socket.setTimeout(timeout);
        socket.once('error', onError);
        socket.once('timeout', onError);

        socket.connect(port, host, () => {
            socket.end();
            resolve(true);
        });
    }));
};

module.exports = async function (args) {
    const RUNNING_IN_IDE = process.env.RUNNING_TESTS_FROM_CMD === undefined;
    const UNIT_TESTS_ONLY = args.testPathPattern && !args.testPathPattern.endsWith(".ui.test.js");

    if (RUNNING_IN_IDE && UNIT_TESTS_ONLY) {
        console.log("No browser for unit tests started from IDE.");
        global.__BROWSER_GLOBAL__ = false;
    } else if (process.env.slow_mo) {
        console.log('Slow mo requested: Using non-headless chrome, slow-mo factor ' + process.env.slow_mo);
        global.__BROWSER_GLOBAL__ = await start_slow_mo_chrome_with_open_window(process.env.slow_mo);
    } else if (RUNNING_IN_IDE) {
        console.log('Running tests from IDE: Using non-headless chrome, slow-mo factor ' + SLOW_MO_DEFAULT);
        global.__BROWSER_GLOBAL__ = await start_slow_mo_chrome_with_open_window(SLOW_MO_DEFAULT);
    } else if (global.__DOCKER_GLOBAL__ === undefined) {
        await start_chrome_in_docker();

        global.__DOCKER_GLOBAL__ = true;
    }

    if (RUNNING_IN_IDE && UNIT_TESTS_ONLY) {
        console.log('Connection to development server not required.')
    } else if (RUNNING_IN_IDE) {
        console.log('Pinging local development server...');

        if (await probeConnection(PORT, 'localhost', 500)) {
            console.log('Ping: OK')
        } else {
            throw new Error('Start local dev server before running tests from the IDE.');
        }
    }
};

let checkForFileContent = function(filepath, timeout) {
    return new Promise((resolve, reject) => {

        var timer = setTimeout(() => {
            watcher.close();
            reject(new Error("No file content written"));
        }, timeout);

        fs.readFile(filepath, 'utf8', (err, data) => {
            if (!err && data.length != 0) {
                clearTimeout(timer);
                watcher.close();
                resolve();
            }
        });

        const basename = path.basename(filepath);
        var watcher = fs.watch(path.dirname(filepath), (vent, filename) => {
            if (filename === basename) {
        fs.readFile(filepath, 'utf8', (err, data) => {
            if (!err && data.length != 0) {
                clearTimeout(timer);
                watcher.close();
                resolve();
            }
        });
            }
        });

    });
}

