@import "design";
@import "shop/core/constants";

.item-list {
  border-radius: 6px;
  border: solid 1px var(--v-grey-lighten1);
  box-shadow: 0 2px 40px 0 rgba(176, 192, 237, 0.16);
  background-color: var(--v-grey-lighten3);
}

.item {
  display: grid;
  grid-column-gap: 20px;
  grid-column: 1 / -1;
  align-items: center;
  align-content: center;
  box-shadow: inset 0 -1px 0 0 #DFDFE0;
  min-height: 50px;
  padding-left: 20px;
  padding-right: 20px;

  div {
    text-align: left;

    strong {
      font-size: 12px;
    }
  }

  &.header {
    height: 60px;
  }

  &.pagination {
    height: 60px;
    width: 100%;
    padding-right: 25px;
    grid-template-columns: 1fr;
    box-shadow: none;
  }

  &:not(.no-hover):hover {
    cursor: pointer;
  }
}
