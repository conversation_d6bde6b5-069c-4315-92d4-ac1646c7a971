.invisible-input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  display: table;
  transition: opacity .3s ease;
}

.modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.modal-container {
  font-size: 20px;
  line-height: initial;
  width: 60%;
  max-width: 600px;
  margin: 0 auto;
  padding: 70px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, .33);
  transition: all .3s ease;
}

.modal-header {
  margin-bottom: 20px;
  border-bottom: none;
}

.modal-body {
  margin-bottom: 45px;
  text-align: left;
  font-size: 18px;
  line-height: 32px;
}

.modal-footer {
  text-align: center;
  border-top: none;
  display: inline-block;
  width: 100%;

  button {
    float: right;
  }
}
