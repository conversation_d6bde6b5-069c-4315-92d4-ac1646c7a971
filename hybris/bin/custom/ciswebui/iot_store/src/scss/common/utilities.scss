@import "breakpoints";

// Limits a wrapper element to 'x' number of text lines
@mixin lineClamp($lines, $minHeight, $maxHeight) {
  min-height: $minHeight;
  max-height: $maxHeight;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  word-wrap: break-word;
  overflow: hidden;
  overflow-wrap: break-word;
}

br {
  &.mobileOnly {
    @include respond-to('M') {
      display: none;
    }
  }
}

.wrap-list {
  margin-left: 0 !important;
  padding-left: 0;
  list-style-type: disc;
  list-style-position: outside;
}

.no-bullets {
  list-style-type: none !important;
}