/*
importing this file enables usage of vuetify default variables, e.g:
h1 {
  padding: map-get($spacers, 5) 0;
}
for importing cd-system overrides and variables, see 'common/cdsystem'
*/
@import '~vuetify/src/styles/styles.sass';




// mixins allow usage of vuetify based media queries
@mixin isMobile() {
  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    @content;
  }
};

@mixin isXL() {
  @media #{map-get($display-breakpoints, 'xl-only')} {
    @content;
  }
};

@mixin isDesktop() {
  @media #{map-get($display-breakpoints, 'lg-and-up')} {
    @content;
  }
};

@mixin isMdAndDown() {
  @media #{map-get($display-breakpoints, 'md-and-down')} {
    @content;
  }
};
