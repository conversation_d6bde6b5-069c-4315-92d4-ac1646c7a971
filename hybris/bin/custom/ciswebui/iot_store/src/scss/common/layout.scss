@import "breakpoints";

// Mixin for column width of grid child element
// defaults are 1 / -1 is 'full width'
@mixin grid-columns($start: 1, $end: -1) {
  grid-column-start: $start;
  grid-column-end: $end;
}

// Evently spaces a group of elements or components,
// removing the leading (top) margin for first element, trailing (bottom) margin for last element
@mixin rhythm-spacer($vertical-space, $horizontal-space: 0) {
  margin: $vertical-space $horizontal-space;
  &:first-child {
    margin-top: 0
  }
  &:last-child  {
    margin-bottom: 0
  }
}

// 'double ampersand' selector applies styles to repeated sibling elements (but not the first sibling)
@mixin repeating-siblings() {
  & + & {
    @content
  }
}
