%-logo-inner {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

%-logo-large-outer {
  width: 140px;
  height: 140px;
  border-radius: 19px;
  border: 7px solid var(--v-primary-base);
  box-shadow: 0 0 4px 0 rgba(255, 255, 255, 0.75), 0 8px 20px 2px rgba(0, 0, 0, 0.3);
}

%-logo-large-inner {
  border: 5px solid white;
  border-radius: 12px;
  @extend %-logo-inner;
}

%-logo-medium-outer {
  width: 120px;
  height: 120px;
  border-radius: 16px;
  border: 6px solid var(--v-primary-base);
  box-shadow: 0 0 4px 0 rgba(255, 255, 255, 0.75), 0 8px 20px 2px rgba(0, 0, 0, 0.3);
}

%-logo-medium-inner {
  border: 4px solid white;
  border-radius: 10px;
  @extend %-logo-inner;
}

%-logo-small-outer {
  width: 110px;
  height: 110px;
  border-radius: 15px;
  border: 6px solid var(--v-primary-base);
  box-shadow: 0 0 4px 0 rgba(255, 255, 255, 0.75), 0 8px 20px 2px rgba(0, 0, 0, 0.3);
}

%-logo-small-inner {
  border: 4px solid white;
  border-radius: 9px;
  @extend %-logo-inner;
}

%-logo-mini-outer {
  width: 90px;
  height: 90px;
  border-radius: 7px;
}

%-logo-mini-inner {
  @extend %-logo-inner;
}

%-logo-tiny-outer {
  width: 75px;
  height: 75px;
  border-radius: 10px;
  border: 4px solid var(--v-primary-base);
  box-shadow: 0 0 4px 0 rgba(255, 255, 255, 0.75), 0 8px 20px 2px rgba(0, 0, 0, 0.3);
}

%-logo-tiny-inner {
  border: 3px solid white;
  border-radius: 6px;
  @extend %-logo-inner;
}


%-logo-tiny {
  width: 60px;
  height: 60px;
  border-radius: 10px;

  img {
    width: 60px;
    height: 60px;
  }
}
