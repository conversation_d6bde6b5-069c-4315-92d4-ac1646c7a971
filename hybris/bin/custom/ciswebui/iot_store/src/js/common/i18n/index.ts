import en from 'common/i18n/en.json';
import de from 'common/i18n/de.json';
import VueI18n, {type DateTimeFormats, type FallbackLocale, type Locale, type LocaleMessages} from 'vue-i18n';
import {i18nService} from 'common/services';
import {castToVueI18n, createI18n} from 'vue-i18n-bridge';
import Vue from 'vue';

declare global {
    interface Window {
        frontendData: any;
    }
}

Vue.use(VueI18n, { bridge: true });


const datetimeFormats: DateTimeFormats = {
    en: {
        time: {
            hour: 'numeric', minute: 'numeric'
        },
        short: {
            year: 'numeric', month: 'short', day: 'numeric'
        },
        long: {
            year: 'numeric', month: 'short', day: 'numeric',
            hour: 'numeric', minute: 'numeric'
        }
    },
    de: {
        time: {
            hour: 'numeric', minute: 'numeric'
        },
        short: {
            year: 'numeric', month: 'short', day: 'numeric'
        },
        long: {
            year: 'numeric', month: 'short', day: 'numeric',
            hour: 'numeric', minute: 'numeric'
        }
    }
};

const messages: LocaleMessages  = {
    en,
    de
};

const fallbackLocale: FallbackLocale = 'en';

const numberFormats = {
    en: {
        decimal: {
            style: 'decimal',
            locale: 'en',
            maximumFractionDigits: 1
        },
        price: {
            style: 'decimal',
            locale: 'en',
            maximumFractionDigits: 2,
            minimumFractionDigits: 2
        }
    },
    de: {
        decimal: {
            style: 'decimal',
            locale: 'de',
            maximumFractionDigits: 1
        },
        price: {
            style: 'decimal',
            locale: 'de',
            maximumFractionDigits: 2,
            minimumFractionDigits: 2
        }
    }
};

const locale: Locale = i18nService.getUserLanguage() || fallbackLocale;

const options = {
    legacy: false,
    locale,
    messages,
    datetimeFormats,
    numberFormats
};

export const i18n = castToVueI18n(createI18n(options, VueI18n));
