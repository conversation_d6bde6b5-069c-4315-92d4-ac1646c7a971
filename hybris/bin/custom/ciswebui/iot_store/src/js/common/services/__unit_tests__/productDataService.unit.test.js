import {productDataService} from 'common/services';
import {LicenseType} from '../../types';
import {StoreAvailabilityMode} from 'common/generated-types/types';

describe("productDataService.readOnly", () => {
    it('is true for null product', () => {
        expect(productDataService.readOnly(null)).toBeTruthy();
    });

    it('is true for empty product', () => {
        expect(productDataService.readOnly({})).toBeTruthy();
    });

    it('is true for product with empty enabled countries', () => {
        expect(productDataService.readOnly({"enabledCountries": []})).toBeTruthy();
    });

    it('is true for product with null enabled countries', () => {
        expect(productDataService.readOnly({"enabledCountries": null})).toBeTruthy();
    });

    it('is false for product with nonempty enabled countres', () => {
        expect(productDataService.readOnly({"enabledCountries": [{"isocode": "DE"}]})).toBeFalsy();
    });

    it('is false for private apps regardless of the enabled countries property', () => {
        expect(productDataService.readOnly({
            "enabledCountries": [],
            "storeAvailabilityMode": StoreAvailabilityMode.RESTRICTED_BUYER
        })).toBeFalsy();
    });
});

describe("productDataService.availableInCountry", () => {
    let validCountry;
    let validProduct;

    beforeEach(() => {
        validCountry = {"isocode": "DE"};
        validProduct = {"enabledCountries": [validCountry]};
        mockCurrentCountry(validCountry);
    });

    const mockCurrentCountry = (currentCountry) => {
        window.frontendData = {
            coreData: {
                currentCountry
            }
        };
    };

    it('is false for readonly products', () => {
        mockCurrentCountry(null);

        expect(productDataService.availableInCountry(null)).toBeFalsy();
        expect(productDataService.availableInCountry({})).toBeFalsy();
        expect(productDataService.availableInCountry({"enabledCountries": []})).toBeFalsy();
        expect(productDataService.availableInCountry({"enabledCountries": null})).toBeFalsy();
    });
    it('is false for invalid country', () => {
        mockCurrentCountry(null);
        validProduct = {"enabledCountries": ["DE"]};
        expect(productDataService.availableInCountry(validProduct)).toBeFalsy();

        mockCurrentCountry({});
        expect(productDataService.availableInCountry(validProduct)).toBeFalsy();
    });

    it('is false if product is not available in given country', () => {
        mockCurrentCountry({"isocode": "US"});
        expect(productDataService.availableInCountry(validProduct)).toBeFalsy();
    });

    it('is true if app is private', () => {
        expect(productDataService.availableInCountry(
            {
                "enabledCountries": [],
                "storeAvailabilityMode": StoreAvailabilityMode.RESTRICTED_BUYER
            }
        )).toBeTruthy();
    });

    it('is true if product is available in given country', () => {
        expect(productDataService.availableInCountry(validProduct)).toBeTruthy();
    });
});

describe("productDataService.trialOnly", () => {
    const evaluationLicense = {"licenseType": {"code": LicenseType.EVALUATION}};
    const fullLicense = {"licenseType": {"code": LicenseType.FULL}};
    const subscriptionLicense = {"licenseType": {"code": LicenseType.SUBSCRIPTION}};

    it('is false for null product', () => {
        expect(productDataService.trialOnly(null)).toBeFalsy();
    });
    it('is false for empty product', () => {
        expect(productDataService.trialOnly({})).toBeFalsy();
    });
    it('is false for product with no licenses', () => {
        expect(productDataService.trialOnly({"licenses": []})).toBeFalsy();
    });

    it('is false for product with a full license', () => {
        expect(productDataService.trialOnly({"licenses": [fullLicense, evaluationLicense]})).toBeFalsy();
    });
    it('is false for product with a subscription license', () => {
        expect(productDataService.trialOnly({"licenses": [subscriptionLicense, evaluationLicense]})).toBeFalsy();
    });

    it('is true for product with evaluation license only', () => {
        expect(productDataService.trialOnly({"licenses": [evaluationLicense]})).toBeTruthy();
    });
});
describe("productDataService.isToolApp", () => {
    const evaluationLicense = {"licenseType": {"code": LicenseType.EVALUATION}};
    const fullLicense = {"licenseType": {"code": LicenseType.FULL}};
    const subscriptionLicense = {"licenseType": {"code": LicenseType.SUBSCRIPTION}};
    const toolLicense = {"licenseType": {"code": LicenseType.TOOL}};

    it('isToolApp returns false for null product', () => {
        expect(productDataService.isToolApp(null)).toBeFalsy();
    });
    it('isToolApp returns false for empty product', () => {
        expect(productDataService.isToolApp({})).toBeFalsy();
    });
    it('isToolApp returns false for product with no licenses & no supportedLicenses', () => {
        expect(productDataService.isToolApp({"licenses": [], "supportedLicenses": []})).toBeFalsy();
    });

    it('isToolApp returns false for product with null licenses & null supportedLicenses', () => {
        expect(productDataService.isToolApp({"licenses": null, "supportedLicenses": null})).toBeFalsy();
    });

    it('isToolApp returns false for product with empty licenses & null supportedLicenses', () => {
        expect(productDataService.isToolApp({"licenses": [], "supportedLicenses": null})).toBeFalsy();
    });

    it('isToolApp returns false for product with null licenses & empty supportedLicenses', () => {
        expect(productDataService.isToolApp({"licenses": null, "supportedLicenses": []})).toBeFalsy();
    });

    it('isToolApp returns true for no license and with tool license in supported licenses', () => {
        expect(productDataService.isToolApp({"licenses": [], "supportedLicenses": [toolLicense]})).toBeTruthy();
    });

    it('isToolApp returns true for product with tool license only and any supported licenses', () => {
        expect(productDataService.isToolApp({"licenses": [toolLicense], "supportedLicenses": [evaluationLicense]})).toBeTruthy();
    });

    it('isToolApp return false for product with a full license', () => {
        expect(productDataService.isToolApp({"licenses": [fullLicense, evaluationLicense], "supportedLicenses": []})).toBeFalsy();
    });
    it('isToolApp return false for product with a subscription license', () => {
        expect(productDataService.isToolApp({"licenses": [subscriptionLicense, evaluationLicense]})).toBeFalsy();
    });
});
