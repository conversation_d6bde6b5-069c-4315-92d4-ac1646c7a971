import {DetailProductData, SimpleProductData, StoreAvailabilityMode} from 'common/generated-types/types';
import {LicenseType, FollowAppChangesData} from 'common/types';
import {has, isEmpty} from 'lodash';
import {shopCoreDataProvider} from 'common/provider';

let productDataService = {
    readOnly: function (product: SimpleProductData) {
        const isPrivate = this.isPrivate(product);
        return !isPrivate && (!has(product, 'enabledCountries') || isEmpty(product.enabledCountries));
    },
    fullLicenseDisabled: function (product: SimpleProductData) {
        return !product.licenses.map(l => l.licenseType.code).includes(LicenseType.FULL);
    },
    trialOnly: function (product: SimpleProductData) {
        if (!product || !product.licenses) {
            return false;
        }
        const licenseTypes = product.licenses.map(l => l.licenseType.code);
        return licenseTypes.includes(LicenseType.EVALUATION)
            && !licenseTypes.includes(LicenseType.FULL)
            && !licenseTypes.includes(LicenseType.SUBSCRIPTION);
    },
    isToolApp: function (product: DetailProductData) {
        if (!product || (isEmpty(product.licenses) && isEmpty(product.supportedLicenses))) {
            return false;
        }
        const licensesData = !isEmpty(product.licenses) ? product.licenses : product.supportedLicenses;
        return licensesData.map(l => l.licenseType.code).includes(LicenseType.TOOL);
    },
    isThirdPartyApp: function (product: DetailProductData) {
        if (!product || (isEmpty(product.licenses) && isEmpty(product.supportedLicenses))) {
            return false;
        }
        const licensesData = !isEmpty(product.licenses) ? product.licenses : product.supportedLicenses;
        return licensesData.map(l => l.licenseType.code).includes(LicenseType.EVALUATION || LicenseType.FULL ||
            LicenseType.FULL || LicenseType.SUBSCRIPTION);
    },
    availableInCountry: function (product: SimpleProductData) {
        // Countries restrictions do not apply to private apps
        const isPrivate = this.isPrivate(product);
        if (isPrivate) {
            return true;
        }
        const country = shopCoreDataProvider.data.currentCountry;

        return !this.readOnly(product)
            && has(country, 'isocode')
            && product.enabledCountries.some(productCountry => productCountry.isocode === country.isocode)
    },
    isPrivate: function (product: SimpleProductData) {
        return product && product.storeAvailabilityMode === StoreAvailabilityMode.RESTRICTED_BUYER;
    },
    followAppChangesData: function (productData: DetailProductData) {
        return productData as FollowAppChangesData;
    }
};

export {productDataService}
