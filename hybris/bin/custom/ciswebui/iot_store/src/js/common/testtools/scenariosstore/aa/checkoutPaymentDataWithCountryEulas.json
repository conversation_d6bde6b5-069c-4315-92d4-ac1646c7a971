{"paymentAddress": {"id": "8820669054999", "title": "", "titleCode": "", "firstName": "", "lastName": "", "companyName": "ESItronic Onboarded Test 1 GmbH", "line1": "Innsbrucker Str.", "line2": "56", "town": "<PERSON><PERSON><PERSON><PERSON>", "region": "", "district": "", "postalCode": "6300", "phone": "", "cellphone": "", "email": "", "country": {"isocode": "AT", "name": "Österreich", "canBuy": false}, "shippingAddress": true, "billingAddress": true, "defaultAddress": false, "visibleInAddressBook": true, "formattedAddress": "Innsbrucker Str., 56, <PERSON><PERSON><PERSON><PERSON>, 6300"}, "totalPrice": {"symbol": "EUR", "value": "900.00"}, "totalTax": {"symbol": "EUR", "value": "114.00"}, "totalPriceWithTax": {"symbol": "EUR", "value": "714.00"}, "entries": [{"appCode": "AA_04015100", "productName": "CRIN Bosch Hauptlizenz", "productCode": "AA_04015100_sub", "productUrl": "/p/AA_04015100", "companyName": "<PERSON>", "versionName": "", "licenseName": "Abonnement", "licenseType": "SUBSCRIPTION", "runtime": {"code": "runtime_subs_unlimited"}, "logoUrl": "/sample-data/aa/medium-logo.png", "smallLogoUrl": "/sample-data/aa/logo.png", "itemPrice": {"symbol": "EUR", "value": "300.00"}, "totalPrice": {"symbol": "EUR", "value": "600.00"}, "productFuturePrices": [{"symbol": "EUR", "value": "400.00"}], "quantity": 2, "entryNumber": 0, "scalePrices": [], "bundleInfo": null, "countryEulas": [{"label": "ESI[tronic] EULA", "url": "https://cdn.esitronic.de/eula/ESItronic/AT_EULA.html"}], "addOnThl": "thlTestUserGroup", "addOnUg": "thlTestUserGroup"}, {"appCode": "AA_04015101", "productName": "CRIN Bosch Z<PERSON>z", "productCode": "AA_04015101_sub", "productUrl": "/p/AA_04015101", "companyName": "<PERSON>", "versionName": "1.0.1", "licenseName": "Subscription", "licenseType": "SUBSCRIPTION", "runtime": {"code": "runtime_subs_unlimited"}, "logoUrl": "/sample-data/aa/medium-logo.png", "itemPrice": {"symbol": "EUR", "value": "240.00"}, "totalPrice": {"symbol": "EUR", "value": "390.00"}, "productFuturePrices": [{"symbol": "EUR", "value": "390.00"}], "quantity": 2, "entryNumber": 1, "scalePrices": [], "bundleInfo": null, "countryEulas": [{"label": "THL EULA 1", "url": "https://cdn.esitronic.de/eula/THL/AT_EULA.html"}, {"label": "THL EULA 2", "url": "https://cdn.esitronic.de/eula/THL/AT_EULA.html"}, {"label": "THL EULA 3", "url": "https://cdn.esitronic.de/eula/THL/AT_EULA.html"}], "addOnThl": "thlTestUserGroup", "addOnUg": "thlTestUserGroup"}, {"appCode": "AA_04015103", "sellerProductId": "1 687 P15 100", "productName": "Technical Hot Line CRIN Bosch Hauptlizenz", "productCode": "AA_04015101_sub", "productUrl": "/p/AA_04015103", "companyName": "<PERSON>", "versionName": "", "licenseName": "Abonnement", "licenseType": "SUBSCRIPTION", "runtime": {"code": "runtime_subs_unlimited"}, "logoUrl": "/sample-data/aa/medium-logo.png", "smallLogoUrl": "/sample-data/aa/logo.png", "itemPrice": {"symbol": "EUR", "value": "150.00"}, "totalPrice": {"symbol": "EUR", "value": "300.00"}, "quantity": 2, "entryNumber": 0, "scalePrices": [], "bundleInfo": null, "specialOffer": false, "countryEulas": [], "addOnUg": "thlTestUserGroup", "addOnThl": ""}, {"appCode": "AA_04015104", "sellerProductId": "1 687 P15 100", "productName": "Technical Hot Line CRIN Bosch Z<PERSON>z", "productCode": "AA_04015101_sub", "productUrl": "/p/AA_04015104", "companyName": "<PERSON>", "versionName": "", "licenseName": "Abonnement", "licenseType": "SUBSCRIPTION", "runtime": {"code": "runtime_subs_unlimited"}, "logoUrl": "/sample-data/aa/medium-logo.png", "smallLogoUrl": "/sample-data/aa/logo.png", "itemPrice": {"symbol": "EUR", "value": "150.00"}, "totalPrice": {"symbol": "EUR", "value": "300.00"}, "quantity": 2, "entryNumber": 0, "scalePrices": [], "bundleInfo": null, "specialOffer": false, "countryEulas": [], "addOnUg": "thlTestUserGroup"}], "paymentMethod": "SEPA_CREDIT", "invoicePaymentInfos": [{"id": "*************", "paymentMethod": "SEPA_CREDIT", "saved": true, "reusable": false, "defaultPaymentInfo": true, "paymentProvider": "DPG", "enabled": true, "disableReason": "", "accountHolder": "Security & Safety Things GmbH", "iban": "**********************", "bankName": "Deutsche Bank", "bic": "DEUTDEFFVAC"}], "dpgjsUrl": "http://127.0.0.1:8091/dpgjs/js", "sessionConfig": "some client configuration", "sessionId": "J#LGB_.GM%:bx%$(yGoRI2", "showInvoiceNotes": true, "invoiceNoteSizeLimit": 50, "ownAppsPurchase": false, "listOfDistributors": [{"id": "id_1", "name": "Zeta distributor"}, {"id": "id_2", "name": "Alpha distributor"}, {"id": "id_3", "name": "Gamma distributor"}, null, {"id": "id_4", "name": "Beta distributor"}], "checkoutInfos": [{"paymentInfos": [{"id": "*************", "paymentMethod": "CREDIT_CARD", "saved": true, "defaultPaymentInfo": false, "accountHolderName": "CHRIS PAUL", "cardType": "visa", "cardTypeData": {"code": "visa", "name": "Visa"}, "cardNumber": "***4321", "startMonth": null, "startYear": null, "expiryMonth": "11", "expiryYear": "2025", "issueNumber": null, "subscriptionId": null}], "paymentProvider": "DPG", "paymentMethod": "CREDIT_CARD", "userActionParameters": {"dpgjsUrl": "http://127.0.0.1:8091/dpgjs/js", "sessionConfig": "some client configuration", "paymentId": "payment_WKU6o28LYfQYCp5iz5UgKx", "sessionId": "1/DJ;9v{<f7kZQt*2G;8r*"}, "userCreatable": true, "savableForReuse": true}, {"paymentInfos": [{"id": "*************", "paymentMethod": "SEPA_CREDIT", "saved": true, "reusable": false, "defaultPaymentInfo": true, "paymentProvider": "DPG", "enabled": true, "disableReason": "", "accountHolder": "Security & Safety Things GmbH", "iban": "**********************", "bankName": "Deutsche Bank", "bic": "DEUTDEFFVAC"}], "paymentProvider": "DPG", "paymentMethod": "SEPA_CREDIT", "userActionParameters": {}, "userCreatable": false, "savableForReuse": false}]}