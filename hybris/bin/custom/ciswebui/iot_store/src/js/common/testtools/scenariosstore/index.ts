import pageDataProductDetails from 'common/testtools/scenariosstore/pageDataProductDetails.json';
import pageDataProductDetailsAA from 'common/testtools/scenariosstore/aa/pageDataProductDetails.json';
import pageDataProductDetailsHasCompanyProfile from 'common/testtools/scenariosstore/pageDataProductDetailsHasCompanyProfile.json';
import pageDataToolProductDetails from 'common/testtools/scenariosstore/pageDataToolProductDetails.json';
import productsData from 'common/testtools/scenariosstore/productsData.json';
import productsDataAA from 'common/testtools/scenariosstore/aa/productsData.json';
import toolsData from 'common/testtools/scenariosstore/toolsData.json';
import minimalPageDataProductDetails from 'common/testtools/scenariosstore/minimalPageDataProductDetails.json';
import orderHistoryData from 'common/testtools/scenariosstore/orderHistoryData.json';
import orderHistoryDataAA from 'common/testtools/scenariosstore/aa/orderHistoryData.json';
import orderDetailsData from 'common/testtools/scenariosstore/orderDetailsData.json';
import updatePaymentMethodInitData from 'common/testtools/scenariosstore/aa/updatePaymentMethodInitData.json';
import orderDetailsDataAA from 'common/testtools/scenariosstore/aa/orderDetailsData.json';
import paymentDetailsData from 'common/testtools/scenariosstore/paymentDetailsData.json';
import paymentDetailsDataAA from 'common/testtools/scenariosstore/aa/paymentDetailsData.json';
import productSelectionAA from 'common/testtools/scenariosstore/aa/productSelection.json';
import checkoutConfirmationData from 'common/testtools/scenariosstore/checkoutConfirmationData.json';
import checkoutConfirmationDataAA from 'common/testtools/scenariosstore/aa/checkoutConfirmationData.json';
import ownAppCheckoutConfirmationData from 'common/testtools/scenariosstore/ownAppCheckoutConfirmationData.json';
import trialCheckoutConfirmationData from 'common/testtools/scenariosstore/trialCheckoutConfirmationData.json';
import checkoutPaymentData from 'common/testtools/scenariosstore/checkoutPaymentData.json';
import checkoutPaymentDataAA from 'common/testtools/scenariosstore/aa/checkoutPaymentData.json';
import cartData from 'common/testtools/scenariosstore/cartData.json';
import ownAppCartData from 'common/testtools/scenariosstore/ownAppCartData.json';
import cartDataAA from 'common/testtools/scenariosstore/aa/cartData.json';
import coreDataDefault from 'common/testtools/scenariosstore/coreDataDefault.json';
import coreDataDefaultAA from 'common/testtools/scenariosstore/aa/coreDataDefault.json';
import sidebarData from 'common/testtools/scenariosstore/sidebarData.json';
import companyProfileData from 'common/testtools/scenariosstore/companyProfileData.json';
import pageDataToolProductDetailsWithFollowTool from 'common/testtools/scenariosstore/pageDataToolProductDetailsWithFollowTool.json';
import cartWithSubscriptionFuturePrices from 'common/testtools/scenariosstore/cartWithSubscriptionFuturePrices.json';
import {validateData} from 'common/testtools/validator';
import {ShopRoute} from 'common/constants';

const testMap = {
    Products: {
        coreData: validateData(coreDataDefault, 'ShopCoreData'),
        pageData: productsData,
        url: `${ShopRoute.PRODUCTS}/`
    },
    ProductsAA: {
        coreData: validateData(coreDataDefaultAA, 'ShopCoreData'),
        pageData: productsDataAA,
        url: `${ShopRoute.PRODUCTS_SEARCH}/`
    },
    ProductsOverviewAA: {
        coreData: validateData(coreDataDefaultAA, 'ShopCoreData'),
        url: `${ShopRoute.PRODUCTS_OVERVIEW}/`
    },
    Tools: {
        coreData: validateData(coreDataDefault, 'ShopCoreData'),
        pageData: toolsData,
        url: `${ShopRoute.TOOLS}/`
    },
    ProductDetails: {
        coreData: validateData(coreDataDefault, 'ShopCoreData'),
        pageData: pageDataProductDetails,
        url: ShopRoute.PRODUCT_DETAIL
    },
    ProductDetailsAA: {
        coreData: validateData(coreDataDefaultAA, 'ShopCoreData'),
        pageData: pageDataProductDetailsAA,
        url: ShopRoute.PRODUCT_DETAIL
    },
    ToolProductDetails: {
        coreData: validateData(coreDataDefault, 'ShopCoreData'),
        pageData: pageDataToolProductDetails,
        url: ShopRoute.PRODUCT_DETAIL
    },
    MinimalProductDetails: {
        coreData: coreDataDefault,
        pageData: minimalPageDataProductDetails,
        url: ShopRoute.PRODUCT_DETAIL
    },
    OrderHistory: {
        coreData: coreDataDefault,
        pageData: orderHistoryData,
        url: ShopRoute.ORDER_HISTORY
    },
    OrderHistoryAA: {
        coreData: coreDataDefaultAA,
        pageData: orderHistoryDataAA,
        url: ShopRoute.ORDER_HISTORY
    },
    OrderDetails: {
        coreData: coreDataDefault,
        pageData: orderDetailsData,
        url: ShopRoute.ORDER_DETAILS
    },
    UpdatePaymentMethodAA: {
        coreData: coreDataDefaultAA,
        pageData: updatePaymentMethodInitData,
        url: ShopRoute.UPDATE_PAYMENT_METHOD
    },
    OrderDetailsAA: {
        coreData: coreDataDefaultAA,
        pageData: orderDetailsDataAA,
        url: ShopRoute.ORDER_DETAILS
    },
    PaymentDetails: {
        coreData: coreDataDefault,
        pageData: paymentDetailsData,
        url: ShopRoute.PAYMENT_DETAILS
    },
    PaymentDetailsAA: {
        coreData: coreDataDefaultAA,
        pageData: paymentDetailsDataAA,
        url: ShopRoute.PAYMENT_DETAILS
    },
    ProductSelectionAA: (productCode: string = ':productCode') => ({
        coreData: coreDataDefaultAA,
        pageData: productSelectionAA,
        url: `${ShopRoute.PRODUCT_SELECTION(productCode)}/`
    }),
    CheckoutPayment: {
        coreData: coreDataDefault,
        pageData: checkoutPaymentData,
        url: ShopRoute.CHECKOUT_PAYMENT,
    },
    CheckoutPaymentAA: {
        coreData: coreDataDefaultAA,
        pageData: checkoutPaymentDataAA,
        url: ShopRoute.CHECKOUT_PAYMENT,
    },
    CheckoutConfirmation: {
        coreData: coreDataDefault,
        pageData: checkoutConfirmationData,
        url: ShopRoute.CHECKOUT_CONFIRMATION
    },
    CheckoutConfirmationAA: {
        coreData: coreDataDefaultAA,
        pageData: checkoutConfirmationDataAA,
        url: ShopRoute.CHECKOUT_CONFIRMATION
    },
    PendingCheckoutConfirmationAA: {
        coreData: coreDataDefaultAA,
        pageData: checkoutConfirmationDataAA,
        url: ShopRoute.PENDING_CHECKOUT_CONFIRMATION
    },
    DelayedCheckoutConfirmationAA: {
        coreData: coreDataDefaultAA,
        pageData: checkoutConfirmationDataAA,
        url: ShopRoute.DELAYED_CHECKOUT_CONFIRMATION
    },
    OwnAppCheckoutConfirmation: {
        coreData: coreDataDefault,
        pageData: ownAppCheckoutConfirmationData,
        url: ShopRoute.CHECKOUT_CONFIRMATION
    },
    OwnAppCheckoutConfirmationAA: {
        coreData: coreDataDefaultAA,
        pageData: checkoutConfirmationDataAA,
        url: ShopRoute.CHECKOUT_CONFIRMATION
    },
    TrialCheckoutConfirmation: {
        coreData: coreDataDefault,
        pageData: trialCheckoutConfirmationData,
        url: ShopRoute.CHECKOUT_CONFIRMATION
    },
    Cart: {
        coreData: coreDataDefault,
        pageData: cartData,
        url: ShopRoute.CART
    },
    CartAA: {
        coreData: coreDataDefaultAA,
        pageData: cartDataAA,
        url: ShopRoute.CART
    },
    OwnAppCart: {
        coreData: coreDataDefault,
        pageData: ownAppCartData,
        url: ShopRoute.CART
    },
    OwnAppCartAA: {
        coreData: coreDataDefaultAA,
        pageData: cartDataAA,
        url: ShopRoute.CART
    },
    CartEmpty: {
        coreData: coreDataDefault,
        pageData: [],
        url: ShopRoute.CART
    },
    CartEmptyAA: {
      coreData: coreDataDefaultAA,
      pageData: [],
      url: ShopRoute.CART
    },
    CartFuturePrices: {
      coreData: coreDataDefault,
      pageData: cartWithSubscriptionFuturePrices,
      url: ShopRoute.CART
    },
    SideBar: {
        coreData: coreDataDefault,
        pageData: sidebarData,
        url: ShopRoute.PRODUCT_DETAIL
    },
    CompanyProfile: {
        coreData: coreDataDefault,
        pageData: companyProfileData,
        url: ShopRoute.COMPANY_PROFILE
    },
    FollowAppSection: {
        coreData: coreDataDefault,
        pageData: sidebarData,
        url: ShopRoute.PRODUCT_DETAIL
    },
    FollowAppSectionToolProductDetails: {
        coreData: validateData(coreDataDefault, 'ShopCoreData'),
        pageData: pageDataToolProductDetailsWithFollowTool,
        url: ShopRoute.PRODUCT_DETAIL
    },
    ProductDetailsWithCompanyProfileSection: {
        coreData: validateData(coreDataDefault, 'ShopCoreData'),
        pageData: pageDataProductDetailsHasCompanyProfile,
        url: ShopRoute.PRODUCT_DETAIL
    },
    HelpAndResources: {
        coreData: coreDataDefault,
        pageData: [],
        url: ShopRoute.HELP_AND_RESOURCES
    },
};

export {testMap};
