import {shopCoreDataProvider} from 'common/provider';
import {PaymentMethodType} from 'common/generated-types/types';
import {paymentMethodsService} from 'common/services/paymentMethodsService';
import {BaseStores} from 'common/constants';

const companyPermissionService = {
    allowCurrentCompanyCreationOfSepaDDPaymentInfo(): boolean {
        const company = shopCoreDataProvider.data.currentCompany;
        const countrySupportsDD = paymentMethodsService.countrySupportsPaymentMethod(company.companyCountry, PaymentMethodType.SEPA_DIRECTDEBIT);
        return shopCoreDataProvider.data.basestore === BaseStores.AA && countrySupportsDD;
    }
}

export {companyPermissionService};
