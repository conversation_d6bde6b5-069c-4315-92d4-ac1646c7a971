{"baseUrl": "/shop/", "basestore": "aastore", "defaultLanguage": "de", "userName": "<PERSON>", "csrfToken": "3764a985-13d3-44d5-9fa3-203deaefc9a6", "currencySymbols": {"EUR": "€", "USD": "$"}, "moduleConfig": {"BRIM_PAYLOAD_SET_TIMEZONE": true, "ALLOW_SELF_PURCHASE": true, "ALLOW_VIDEO_FOR_DEVELOPER": true, "DIRECT_SALES": false, "ENFORCE_EULA_ACCEPTANCE": true, "ENABLE_IMPROVED_NAVIGATION": true, "NEW_INFO_FOR_APP": false, "LANGUAGE_SWITCHER": true, "ENABLE_SEPA_MANDATE": false}, "currentCurrency": "EUR", "globalMessages": {"info": [], "warning": [], "error": [], "messages": []}, "domainUrl": ".dev.local", "httpStatus": 200, "navigationItems": [{"id": "aa_globalHelpSupport", "url": "https://support.securityandsafetythings.com/hc", "type": "GLOBAL", "group": "HELP", "text": "Besuchen Sie Hilfe und Support", "description": "", "target": "_blank", "icon": "", "index": 0, "entryPage": "", "itemCode": "globalHelpSupport", "customAttributes": {}}, {"id": "aa_storeRegionAndLanguageFooter", "url": "", "type": "GLOBAL", "group": "FOOTER", "text": "Location", "description": "", "target": "", "icon": "", "index": 1, "entryPage": "", "itemCode": "storeRegionAndLanguageFooter", "customAttributes": {}}, {"id": "aa_globalImprint", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/imprint", "type": "GLOBAL", "group": "FOOTER", "text": "Impressum", "description": "", "target": "_blank", "icon": "", "index": 2, "entryPage": "", "itemCode": "globalImprint", "customAttributes": {}}, {"id": "aa_globalHelpContact", "url": "https://support.securityandsafetythings.com/hc/requests/new", "type": "GLOBAL", "group": "HELP", "text": "Kontaktieren Sie Uns", "description": "", "target": "_blank", "icon": "", "index": 3, "entryPage": "", "itemCode": "globalHelpContact", "customAttributes": {}}, {"id": "aa_storePrivacyPolicy", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/privacy-policy", "type": "STORE", "group": "FOOTER", "text": "Datenschutz", "description": "", "target": "_blank", "icon": "", "index": 4, "entryPage": "", "itemCode": "storePrivacyPolicy", "customAttributes": {}}, {"id": "aa_globalLegal", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/legal-notice", "type": "GLOBAL", "group": "FOOTER", "text": "Rechtliche Grundlagen", "description": "", "target": "_blank", "icon": "", "index": 5, "entryPage": "", "itemCode": "globalLegal", "customAttributes": {}}, {"id": "aa_storeTermsAndConditions", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/terms", "type": "STORE", "group": "FOOTER", "text": "Nutzungsbedingungen", "description": "", "target": "_blank", "icon": "", "index": 6, "entryPage": "", "itemCode": "storeTermsAndConditions", "customAttributes": {}}, {"id": "aa_storeInfringement", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/report-infringement", "type": "STORE", "group": "FOOTER", "text": "<PERSON>ers<PERSON>ß <PERSON>", "description": "", "target": "_blank", "icon": "", "index": 5, "entryPage": "", "itemCode": "storeInfrigment", "customAttributes": {}}, {"id": "aa_globalSupport", "url": "https://support.securityandsafetythings.com/hc", "type": "GLOBAL", "group": "HEADER", "text": "Unterstützung", "description": "", "target": "_blank", "icon": "support", "index": 5, "entryPage": "", "itemCode": "globalSupport", "customAttributes": {}}, {"id": "aa_storeRegister", "url": "https://sso.twbd.io/company-register", "type": "STORE", "group": "HEADER", "text": "Registrieren", "description": "", "target": "_self", "icon": "", "index": 7, "entryPage": "", "itemCode": "storeRegister", "customAttributes": {}}, {"id": "aa_getInTouch", "url": "https://www.boschaftermarket.com/de/de/esitronic-kontaktformular/", "type": "GLOBAL", "group": "HELP", "text": "Kontakt", "description": "", "target": "_blank", "icon": "", "index": 8, "entryPage": "", "itemCode": "getInTouch", "customAttributes": {}}, {"id": "aa_storeLogin", "url": "/shop/login", "type": "STORE", "group": "HEADER", "text": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "target": "_self", "icon": "", "index": 8, "entryPage": "", "itemCode": "storeLogin", "customAttributes": {}}, {"id": "aa_deviceManagement", "url": "http://localhost:8091/page/cameras", "type": "STORE", "group": "HOME_SWITCHER", "text": "Lizenzmanagement", "description": "", "target": "_blank", "icon": "", "index": 10, "entryPage": "", "itemCode": "deviceManagement", "customAttributes": {}}, {"id": "aa_marketplaceComponent", "url": "/shop", "type": "STORE", "group": "COMPONENTS", "text": "", "description": "", "target": "", "icon": "$store", "index": 1, "entryPage": "", "itemCode": "marketplaceComponent", "customAttributes": {}}, {"id": "aa_licenseManagementComponent", "url": "http://localhost:8091/page/cameras", "type": "STORE", "group": "COMPONENTS", "text": "", "description": "", "target": "", "icon": "$licenses", "index": 2, "entryPage": "", "itemCode": "licenseManagementComponent", "customAttributes": {}}, {"id": "aa_accountComponent", "url": "http://localhost:8091/page/myprofile", "type": "STORE", "group": "COMPONENTS", "text": "", "description": "", "target": "", "icon": "$user", "index": 3, "entryPage": "", "itemCode": "accountComponent", "customAttributes": {}}, {"id": "aa_storeOrderHistory", "url": "/shop/my-account/orders", "type": "STORE", "group": "HEADER", "text": "Order History", "description": "", "target": "", "icon": "$sheet", "index": 3, "entryPage": "", "itemCode": "storeOrderHistory", "customAttributes": {}}, {"id": "aa_storePaymentDetails", "url": "/shop/my-account/payment-details", "type": "STORE", "group": "HEADER", "text": "Payment Details", "description": "", "target": "", "icon": "$payment", "index": 4, "entryPage": "", "itemCode": "storePaymentDetails", "customAttributes": {}}], "userCompanyUnderReview": false, "supportUrl": "https://support.securityandsafetythings.com/hc", "contactUrl": "https://www.securityandsafetythings.com/getintouch", "infoUrl": "https://info.securityandsafetythings.com", "corporateHome": "https://www.securityandsafetythings.com", "accountsUrl": "https://accounts.securityandsafetythings.com", "currentCountry": {"isocode": "AT", "name": "Österreich", "currencyIsoCode": "EUR", "canBuy": true, "sepaEnabled": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_DIRECTDEBIT", "SEPA_CREDIT"]}, "currentCompany": {"name": "austria buyer 1", "friendlyName": "", "companyUid": "197575f5-ec56-40af-84dc-c07d95899c52", "companyCountry": "AT", "hasPublishedProfile": false, "profileUrl": "", "managedAccount": false, "companyApproved": true, "distributor": {"name": "Distributor GmbH", "id": "company-id-000", "externalCustomerId": "********"}, "userGroup": "thlTestUserGroup"}, "activeCountries": [{"isocode": "BE", "name": "Belgium", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "AU", "name": "Australia", "canBuy": false, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "ACH_INTERNATIONAL"]}, {"isocode": "AT", "name": "Austria", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_DIRECTDEBIT", "SEPA_CREDIT"]}, {"isocode": "BG", "name": "Bulgaria", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "CA", "name": "Canada", "canBuy": true, "blockedCountriesCommercial": ["CA"], "supportedPaymentMethodTypes": ["CREDIT_CARD", "ACH_INTERNATIONAL"]}, {"isocode": "CH", "name": "Switzerland", "canBuy": true, "blockedCountriesCommercial": ["CH"], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "CY", "name": "Cyprus", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "CZ", "name": "Czech Republic", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "DE", "name": "Germany", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_DIRECTDEBIT", "SEPA_CREDIT"]}, {"isocode": "DK", "name": "Denmark", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "EE", "name": "Estonia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "ES", "name": "Spain", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "FI", "name": "Finland", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "FR", "name": "France", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "GB", "name": "United Kingdom", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "GR", "name": "Greece", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "HR", "name": "Croatia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "HU", "name": "Hungary", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "IE", "name": "Ireland", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "IN", "name": "India", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "IT", "name": "Italy", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "KR", "name": "Republic of Korea", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "LT", "name": "Lithuania", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "LU", "name": "Luxembourg", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "LV", "name": "Latvia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "MT", "name": "Malta", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "NO", "name": "Norway", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "NL", "name": "Netherlands", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "PL", "name": "Poland", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "RO", "name": "Romania", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "PT", "name": "Portugal", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "SI", "name": "Slovenia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "SE", "name": "Sweden", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "SK", "name": "Slovakia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "TW", "name": "Taiwan", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "US", "name": "United States of America", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "ACH_INTERNATIONAL"]}], "numberOfCartItems": 0, "thlAppCodeInCart": "AA_04015101", "camerasUrl": "http://localhost:8091/page/cameras", "myAppsUrl": "http://localhost:8091/page/myapps", "allowedToBuy": false, "allowedMaxQuantityLineItemFullLicense": 1000, "allowedMaxQuantityLineItemSubsLicense": 1000, "globalDefaultLanguage": "en", "globalFallbackCountry": "AT", "guideCustomerToProvideDirectDebitMandate": false, "purchaseDisabledBannerInfo": {"showPurchaseDisabledBanner": false, "date": "01.01.2025"}, "translationConfig": {"remoteTranslationsEnabled": false, "baseUrl": "", "projectId": "", "cdsProjectId": ""}, "idp": {"loginIdp": "skid", "loginIdpAccountUrl": "http://localhost:8080/auth/realms/baam/account/"}}