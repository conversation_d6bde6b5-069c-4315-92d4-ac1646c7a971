import { wrapperComponentFactory, resizeWidth, resizeHeight, resizeScreen, resetScreenSize } from './unit-test-utils';

describe('allows screen resizing in unit tests', () => {

    it('shows the default 1024 jsdom width', () => {
        expect(window.innerWidth).toBe(1024);
        expect(window.innerHeight).toBe(768);
    });

    it('resizes the screen to 400 width', () => {
        resizeWidth(400);
        expect(window.innerWidth).toBe(400);
    });

    it('resizes the screen to 728 height', () => {
        resizeHeight(728);
        expect(window.innerHeight).toBe(728);
    });

    it('resizes the screen to 300 width and 400 height', () => {
        resizeScreen(300, 400);
        expect(window.innerWidth).toBe(300);
        expect(window.innerHeight).toBe(400);
    });

    it('resetScreenSize resets to 1024 x 768', () => {
        resetScreenSize();
        expect(window.innerWidth).toBe(1024);
        expect(window.innerHeight).toBe(768);
    });

});