import Vue from 'vue';
import Vuetify from 'vuetify/lib';
import {i18n} from 'aa/shop/i18n';

import {
    boschOptions,
    CDRadio,
    CDCheckbox,
    CDButton,
    CDButtonText,
    CDButtonTextIcon,
    CDButtonIcon,
    CDChip,
    CDIcon,
    CDNavbar,
    CDNavbarLink,
    CDFlyoutMenu,
    CDFlyoutMenuItem,
    CDSeparator,
    CDHomeSwitcher,
    CDNotificationPersistent,
    CDNotificationToast,
    CDNotificationToastWrapper,
    CDNeedHelpButton,
    CDInput,
    CDSelect,
    CDDatePicker,
    CDTextArea,
    CDToggle,
    CDBlock,
    CDFooter,
    CDDataTable,
    CDSearch,
    CDAccordion,
    CDTabs,
    CDQuantity,
    CDNavbar2,
    CDNavbar2Link,
    CDUserFlyout,
    CDUserFlyoutMenuItem,
    CDBento,
    CDBreadcrumbs
} from 'cd-system';
import 'cd-system/tokens/fonts/bosch/fonts.css';

Vue.component('CDRadio', CDRadio);
Vue.component('CDCheckbox', CDCheckbox);

Vue.component('CDButton', CDButton);
Vue.component('CDButtonIcon', CDButtonIcon);
Vue.component('CDButtonText', CDButtonText);
Vue.component('CDButtonTextIcon', CDButtonTextIcon);
Vue.component('CDNeedHelpButton', CDNeedHelpButton);

Vue.component('CDChip', CDChip);
Vue.component('CDIcon', CDIcon);

Vue.component('CDNavbar', CDNavbar);
Vue.component('CDNavbarLink', CDNavbarLink);
Vue.component('CDFlyoutMenu', CDFlyoutMenu);
Vue.component('CDFlyoutMenuItem', CDFlyoutMenuItem);
Vue.component('CDSeparator', CDSeparator);
Vue.component('CDHomeSwitcher', CDHomeSwitcher);

Vue.component('CDNotificationPersistent', CDNotificationPersistent);
Vue.component('CDNotificationToast', CDNotificationToast);
Vue.component('CDNotificationToastWrapper', CDNotificationToastWrapper);

Vue.component('CDInput', CDInput);
Vue.component('CDSelect', CDSelect);
Vue.component('CDTextArea', CDTextArea);
Vue.component('CDDatePicker', CDDatePicker);
Vue.component('CDToggle', CDToggle);
Vue.component('CDBlock', CDBlock);

Vue.component('CDFooter', CDFooter);
Vue.component('CDDataTable', CDDataTable);

Vue.component('CDSearch', CDSearch);
Vue.component('CDAccordion', CDAccordion);

Vue.component('CDTabs', CDTabs);
Vue.component('CDQuantity', CDQuantity);

Vue.component('CDNavbar2', CDNavbar2);
Vue.component('CDNavbar2Link', CDNavbar2Link);
Vue.component('CDUserFlyout', CDUserFlyout);
Vue.component('CDUserFlyoutMenuItem', CDUserFlyoutMenuItem);
Vue.component('CDBento', CDBento);
Vue.component('CDBreadcrumbs', CDBreadcrumbs);

Vue.use(Vuetify);

boschOptions.lang.current = i18n.locale;
boschOptions.lang.t = (key, ...params) => {
    return i18n.t(key, params);
};

const vuetify = new Vuetify(boschOptions);
Object.entries(vuetify.preset.lang.locales).forEach(
    ([locale, translations]) => {
        i18n.mergeLocaleMessage(locale, {$vuetify: translations});
    }
);
export default vuetify;
