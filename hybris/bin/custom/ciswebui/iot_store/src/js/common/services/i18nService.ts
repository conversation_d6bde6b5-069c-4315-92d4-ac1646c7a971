import type {Locale, LocaleMessages} from 'vue-i18n';
import Cookies from 'js-cookie';
import customerStoreSelectionResource from 'shop/resources/customerStoreSelectionResource';
import axios from 'axios';
import {shopCoreDataProvider} from 'common/provider';
import {ShopCoreDataProvider} from "common/provider/coreDataProvider";
import {BaseStores} from "common/constants";

const LANGUAGE_COOKIE_NAME = 'st_selected_lang';
const COUNTRY_COOKIE_NAME = 'st_selected_country';

const i18nService = {
    getUserLanguage(): Locale {
        return Cookies.get(LANGUAGE_COOKIE_NAME) ?? '';
    },
    getUserCountry(): Locale {
        return Cookies.get(COUNTRY_COOKIE_NAME) ?? '';
    },
    getCompanyCountry(): Locale {
        return shopCoreDataProvider.data.currentCompany?.companyCountry ?? '';
    },
    setUserLanguage(userLanguage: string): void {
        userLanguage && Cookies.set(LANGUAGE_COOKIE_NAME, userLanguage);
    },
    setUserCountry(userCountry: string): void {
        userCountry && Cookies.set(COUNTRY_COOKIE_NAME, userCountry);
    },
    isUserInCompanyCountry(): boolean {
        const companyCountry = this.getCompanyCountry();
        const userCountry = this.getUserCountry();
        return userCountry?.length > 0 && userCountry === companyCountry;
    },
    async fetchTranslation(locale: Locale, baseUrl: string, projectId: string): Promise<LocaleMessages> {
        const response = await axios.create({baseURL: baseUrl, timeout: 20000})
            .get(`/project/${projectId}/locale/${locale}`);
        return response.data;
    },
    async goToSelectedMarketPlace(selectedCountry: string, selectedLanguage: string) {
        return customerStoreSelectionResource.goToSelectedMarketPlace(selectedCountry, selectedLanguage);
    },
    async goToCompanyDefaultMarketPlace() {
        return customerStoreSelectionResource.goToCompanyDefaultMarketPlace();
    },
    getLocaleForFormats(): string {
        const coreData = ShopCoreDataProvider.data;
        if (coreData.basestore === BaseStores.AZENA) {
            return 'en';
        }

        const userCountry = this.getUserCountry()
        switch (userCountry.toUpperCase()) {
            case 'DE': return 'de';
            case 'AT': return 'de';
            case 'DK': return 'da';
            case 'GR': return 'el';
            case 'ES': return 'es';
            case 'EE': return 'et';
            case 'FI': return 'fi';
            case 'FR': return 'fr';
            case 'HR': return 'hr';
            case 'LT': return 'lt';
            case 'LV': return 'lv';
            case 'NL': return 'nl';
            case 'NO': return 'no';
            case 'PT': return 'pt';
            case 'SI': return 'sl';
            case 'SE': return 'sv';
        }

        return 'en'
    },

    stringToDefaultNumericFormat(value: string) : number {
        const numericString = value.replace(/[^0-9]/g, '');
        return parseFloat(numericString.slice(0, -2) + '.' + numericString.slice(-2));
    }
};

export {i18nService};
