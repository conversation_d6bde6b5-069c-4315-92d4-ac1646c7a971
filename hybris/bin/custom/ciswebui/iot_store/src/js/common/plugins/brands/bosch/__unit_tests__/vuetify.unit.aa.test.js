import 'common/testtools/unit_tests_mock'
import 'common/test-directive'
import { i18n } from 'aa/shop/i18n'
import vuetify from 'common/plugins/brands/bosch/vuetify'

describe('AA vuetify', () => {

  describe('translations', () => {

    it('use i18n', async () => {
      i18n.mergeLocaleMessage('en', { $vuetify: { 'cds_key': 'cds_value_en' } })
      i18n.mergeLocaleMessage('de', { $vuetify: { 'cds_key': 'cds_value_de' } })

      i18n.locale = 'en'
      expect(vuetify.framework.lang.t('$vuetify.cds_key')).toBe('cds_value_en')

      i18n.locale = 'de'
      expect(vuetify.framework.lang.t('$vuetify.cds_key')).toBe('cds_value_de')
    })

    it('are merged into i18n translations', async () => {
      expect(i18n.t('$vuetify.needHelpButton', 'en')).toBe('Need help? <b>Contact us</b>')
      expect(i18n.t('$vuetify.needHelpButton', 'de')).toBe('Hilfe benötigt? <b>Kontaktieren Sie uns</b>')
    })

  })
})
