import en from 'common/i18n/en.json';
import de from 'common/i18n/de.json';

describe('The i18n AZENA message objects', () => {

    it('have the same entries per language', async () => {
        expectComponentsHaveEqualKeys(en, de);
    });
});

function expectComponentsHaveEqualKeys(component1, component2) {
    expect(Object.keys(component1).sort()).toEqual(Object.keys(component2).sort());
    const subComponentsInComponent1 = getSubComponentKeys(component1);
    const subComponentsInComponent2 = getSubComponentKeys(component2);
    expect(subComponentsInComponent1.sort()).toEqual(subComponentsInComponent2.sort());
    subComponentsInComponent1.forEach(key => expectComponentsHaveEqualKeys(component1[key], component2[key]));
}

function getSubComponentKeys(component) {
    return Object.keys(component).filter(key => (typeof component[key]) === 'object');
}
