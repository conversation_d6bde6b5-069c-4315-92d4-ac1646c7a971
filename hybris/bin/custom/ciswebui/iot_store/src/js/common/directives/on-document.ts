import {DirectiveBinding} from "vue/types/options";

export const OnDocumentDirective = () => {
    const handlers: Map<any,any> = new Map<any, any>();
    return {
        bind: function(el: HTMLElement, binding: DirectiveBinding) {
            let handler:(event: Event) => void;
            const eventType = binding.arg ;
            if (binding.modifiers && eventType === 'keyup') {
                const keyCodes = Object.keys(binding.modifiers);
                handler = function (event:KeyboardEvent) {
                    keyCodes.includes(event.code) && binding.value(event);
                } as (event: Event) => void
            } else {
                handler = binding.value;
            }
            handlers.set(el, handler);
            document.addEventListener(eventType as keyof DocumentEventMap, handler);
        },

        unbind: (el: HTMLElement) => {
            const handler = handlers.get(el);
            document.removeEventListener('keyup', handler);
        },
    };
}