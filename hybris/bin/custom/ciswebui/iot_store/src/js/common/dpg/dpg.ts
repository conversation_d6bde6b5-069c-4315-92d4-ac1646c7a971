import {Field, IntegrationOptions, IntegrationOptionsName} from "common/dpg/dpg.model";
import {COMPONENT_OPTIONS} from "common/dpg/dpg-component.style";

export default function prepareOptions(clientSession: string, clientConfiguration: string): IntegrationOptions {
    const holder: Field = {
        selector: '#card-holder',
        placeholder: 'Cardholder Name',
        required: true
    };

    const number: Field = {
        selector: '#card-number',
        placeholder: '0000 0000 0000 0000'
    };

    const expiry: Field = {
        selector: '#card-expiry',
        placeholder: 'MM/YY'
    };

    const code: Field = {
        selector: '#card-cvc',
        placeholder: 'nnn'
    };

    const options: IntegrationOptions = {
        clientSession: clientSession,
        clientConfiguration: clientConfiguration,
        type: IntegrationOptionsName.CREDIT_CARD,
        acs_window: {
            width: 700,
            height: 600
        },
        fields: {
            holder,
            number,
            expiry,
            code
        },
        styles: COMPONENT_OPTIONS.style,
    };
    return options;
}