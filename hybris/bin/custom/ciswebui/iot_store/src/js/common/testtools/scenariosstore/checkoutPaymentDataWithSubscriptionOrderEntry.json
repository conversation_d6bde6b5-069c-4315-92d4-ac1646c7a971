{"paymentAddress": {"id": "8796093120535", "title": null, "titleCode": null, "firstName": "<PERSON><PERSON>", "lastName": "User", "companyName": null, "line1": "Sample Street", "line2": "123", "town": "Sample Town", "region": null, "district": null, "postalCode": "12345", "phone": "+1234567890", "cellphone": null, "email": "<EMAIL>", "country": {"isocode": "DE", "name": "Germany", "canBuy": false}, "shippingAddress": false, "billingAddress": true, "defaultAddress": false, "visibleInAddressBook": true, "formattedAddress": "Sample Street, 123, 12345, Sample Town, Germany"}, "totalPrice": {"symbol": "EUR", "value": "40.00"}, "totalTax": {"symbol": "EUR", "value": "00.00"}, "totalPriceWithTax": {"symbol": "EUR", "value": "40.00"}, "entries": [{"appCode": "A_00000119", "productName": "SampleSubscription 1", "productCode": "A_00000119_subscription", "productUrl": "/p/A_00000119", "companyName": "DPG Company", "versionName": "1.0.0", "licenseName": "Subscription", "licenseType": "SUBSCRIPTION", "logoUrl": "/sample-data/icon-subscription-app-license.png", "itemPrice": {"symbol": "EUR", "value": "20.00"}, "totalPrice": {"symbol": "EUR", "value": "40.00"}, "productFuturePrices": [], "quantity": 2, "entryNumber": 1, "scalePrices": [], "bundleInfo": null, "countryEulas": []}], "paymentMethod": "SEPA_CREDIT", "cartHash": "", "showInvoiceNotes": false, "invoiceNoteSizeLimit": 50, "checkoutInfos": []}