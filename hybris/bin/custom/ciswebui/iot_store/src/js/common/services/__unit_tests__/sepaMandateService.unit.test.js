import { sepaMandateService } from '../sepaMandateService';

describe("sepaMandateService", () => {
    
    describe("isValidIban", () => {
        it("returns true for valid IBANs", () => {
            expect(sepaMandateService.isValidIban("**********************")).toBe(true);
            expect(sepaMandateService.isValidIban("***************************")).toBe(true);
            expect(sepaMandateService.isValidIban("******************5432")).toBe(true);
            expect(sepaMandateService.isValidIban("************************")).toBe(true);
        });

        it("returns false for IBANs with invalid characters", () => {
            expect(sepaMandateService.isValidIban("de44500105175407324931")).toBe(false);
            expect(sepaMandateService.isValidIban("******************-5432")).toBe(false);
            expect(sepaMandateService.isValidIban("FR76$0006000011234567890189")).toBe(false);
        });

        it("returns false for IBANs with incorrect length", () => {
            expect(sepaMandateService.isValidIban("DE4450010517")).toBe(false);
            expect(sepaMandateService.isValidIban("**********************45678901234567")).toBe(false);
        });

        it("returns false for IBANs shorter than 15 characters", () => {
            expect(sepaMandateService.isValidIban("DE12")).toBe(false);
            expect(sepaMandateService.isValidIban("GB82WEST12")).toBe(false);
            expect(sepaMandateService.isValidIban("FR76300060001")).toBe(false);
        });

        it("returns true for IBANs with spaces, as they are cleaned before validation", () => {
            expect(sepaMandateService.isValidIban("DE44 5001 0517 5407 3249 31")).toBe(true);
            expect(sepaMandateService.isValidIban("FR76 3000 6000 0112 3456 7890 189")).toBe(true);
            expect(sepaMandateService.isValidIban("GB82 WEST 1234 5698 7654 32")).toBe(true);
        });
    });

    describe("isValidMandateReference", () => {
        it("returns true for valid mandate references", () => {
            expect(sepaMandateService.isValidMandateReference("REF12345678901234567890")).toBe(true);
            expect(sepaMandateService.isValidMandateReference("A1+B?C/D-E:F(G)H.I,'J")).toBe(true);
            expect(sepaMandateService.isValidMandateReference("12345678901234567890123456789012345")).toBe(true);
        });

        it("returns false for mandate references with invalid characters", () => {
            expect(sepaMandateService.isValidMandateReference("ref123")).toBe(false);
            expect(sepaMandateService.isValidMandateReference("REF123€")).toBe(false);
            expect(sepaMandateService.isValidMandateReference("REF 123")).toBe(false);
            expect(sepaMandateService.isValidMandateReference("REFÄÖÜ123")).toBe(false);
            expect(sepaMandateService.isValidMandateReference("A1+B?C/D-E:F(G)H.I,`J")).toBe(false);
        });

        it("returns false for mandate references that are too long or contain spaces", () => {
            expect(sepaMandateService.isValidMandateReference("R")).toBe(true);
            expect(sepaMandateService.isValidMandateReference("REF123456789012345678901234567890123456")).toBe(false);
            expect(sepaMandateService.isValidMandateReference("VALID REF123")).toBe(false);
        });
    });
});
