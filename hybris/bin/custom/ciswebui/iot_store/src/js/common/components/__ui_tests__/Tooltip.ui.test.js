describe('Tooltip', () => {
    it('position', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/common-components-__ui_tests__-Tooltip');
        expect(await page.title()).toBe('UI test - common-components-__ui_tests__-Tooltip');

        await changeViewport({width: 600, height: 300});
        await hoverAndTakeScreenshot('#text', 'is right w one line');
        await page.waitFor(1000);
        await hoverAndTakeScreenshot('#icon', 'is right w multi lines');

        await changeViewport({width: 450, height: 300});
        await page.waitFor(1000);
        await hoverAndTakeScreenshot('#icon', 'is moved left');
        await page.waitFor(1000);

        await changeViewport({width: 350, height: 300});
        await page.waitFor(1000);
        await hoverAndTakeScreenshot('#text', 'is left');
        await page.waitFor(1000);
        await hoverAndTakeScreenshot('#icon', 'is central with resized width');
    });
});

async function changeViewport(size) {
    await page.setViewport(size);
    await page.evaluate(() => {location.reload(true);});
    await page.waitFor(1500);
}

async function hoverAndTakeScreenshot(id, text) {
    await page.hover(id);
    await page.waitFor(1000);
    expect(await page.screenshot()).toMatchImageSnapshot(text);
}
