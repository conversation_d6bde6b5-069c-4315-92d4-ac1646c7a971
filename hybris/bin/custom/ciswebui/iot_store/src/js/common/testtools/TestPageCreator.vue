<template>
    <div class="root" v-bind:class="{hidden: hidden}">
        <h1>Test Page Creator</h1>
        <div class="spacer"></div>
        <h4>Select Scenario: </h4>

        <select v-model="selectedMap">
            <option v-for="scenarioMap in scenariosstore" v-bind:value="scenarioMap[1]">STORE: {{scenarioMap[0]}}</option>
        </select>
        <div class="spacer"></div>
        <p v-if="selectedMap !== null">Matching route: {{selectedMap.url}}</p>
        <input type="button" value="Create Page" v-on:click="createPage" />
    </div>
</template>
<script lang="ts">
    import {Component, Vue} from 'vue-property-decorator';
    import {testMap as testMapStore} from 'common/testtools/scenariosstore';
    import {TestScenario} from 'common/types';

    @Component
    export default class TestPageCreator extends Vue {
        hidden = window.location.pathname !== '/vue-app';

        selectedMap: null | TestScenario = null;

        scenariosstore = Object.entries(testMapStore);

        createPage(): void {
            if(this.selectedMap === null) {
                return;
            }

            window.frontendData = {
                coreData: this.selectedMap.coreData,
                pageData: this.selectedMap.pageData
            };
            window.history.replaceState({}, 'Auto created page', this.selectedMap.url);
            this.hidden = true;

            type WindowExt = Window & {createStoreApp: () => void};
            ((window as unknown) as WindowExt).createStoreApp();
        }
    }
</script>

<style lang="scss" scoped>
    .root textarea {
        width: 100%;
    }

    .root.hidden {
        display: none;
    }

    .spacer {
        width: 100%;
        height: 20px;
    }

    select, input {
      border: 1px solid #000000;
    }
</style>

<style>
    * {
        transition: none !important;
        transition-duration: unset !important;
    }
</style>
