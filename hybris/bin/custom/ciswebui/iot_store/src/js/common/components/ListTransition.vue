<template>
    <transition-group name="list" class="main">
        <slot></slot>
    </transition-group>
</template>

<script lang="ts">
    import { Component, Vue } from 'vue-property-decorator';

    @Component
    export default class ListTransition extends Vue {}
</script>

<style scoped lang="scss">
    .main {
        /* moving */
        & :deep(.list-move) {
            transition: all 600ms ease-in-out 50ms;
        }

        /* appearing */
        & :deep(.list-enter-active) {
            transition: all 400ms ease-out;
        }

        /* disappearing */
        & :deep(.list-leave-active) {
            transition: all 200ms ease-in;
            position: absolute;
            width: 100%;
        }

        /* appear at / disappear to */
        & :deep(.list-enter),
        & :deep(.list-leave-to) {
            opacity: 0;
        }
    }
</style>
