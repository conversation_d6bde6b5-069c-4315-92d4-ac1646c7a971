describe('ConfirmationPopup', ()=>{
   it('Press cancel and ok', async ()=>{
       await page.goto(global.__DEV_ADDRESS__ + '/common-components-popups-__ui_tests__-confirmationdialog');
       expect(await page.screenshot()).toMatchImageSnapshot("init");

       await page.waitForSelector('.open-popup');
       await page.click(".open-popup");
       await page.waitFor(500);
       await page.waitForSelector('.modal-footer');
       await page.waitFor(500); // wait for components to finish rendering
       expect(await page.screenshot()).toMatchImageSnapshot("popup open");

       await page.click(".modal-footer button:first-of-type");
       await page.waitFor(500);
       expect(await page.screenshot()).toMatchImageSnapshot("popup closed ok");

       await page.click(".open-popup");
       await page.waitFor(500);
       await page.waitForSelector('.modal-footer');
       expect(await page.screenshot()).toMatchImageSnapshot("popup open again");

       await page.click(".modal-footer button:last-of-type");
       await page.waitFor(500);
       expect(await page.screenshot()).toMatchImageSnapshot("popup closed cancel");

       await page.click(".open-popup");
       await page.waitFor(500);
       await page.waitForSelector('.modal-footer');

       await page.keyboard.press('Escape');
       await page.waitFor(500);

       await page.click(".open-popup");
       await page.waitFor(500);
       await page.waitForSelector('.modal-footer');

       await page.waitForSelector('.confirmation-dialog');
       await page.$eval('.v-overlay__scrim', el => el.click());
       await page.waitFor(500);
       expect(await page.screenshot()).toMatchImageSnapshot("popup closed by click in background");
   });
    it('Press cancel and ok multi line', async ()=>{
        await page.goto(global.__DEV_ADDRESS__ + '/common-components-popups-__ui_tests__-confirmationdialoglines');
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.waitForSelector('.open-popup');
        await page.click(".open-popup");
        await page.waitForSelector('.modal-footer');
        await page.waitFor(500); // wait for components to finish rendering
        expect(await page.screenshot()).toMatchImageSnapshot("popup open");
    });
});
