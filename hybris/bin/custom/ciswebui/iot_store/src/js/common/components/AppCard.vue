<template>
  <v-hover>
    <template v-slot:default="{ hover }">
      <v-card
          outlined
          :target="appCardData.target"
          :ripple="false"
          :href="appCardData.url"
          rounded="lg"
          :elevation="hover ? 1 : 0"
          class="pa-2 d-flex flex-wrap align-content-space-between">
        <div class="content-wrapper">
          <v-card-title
              class="flex-column black--text">
            <img :src="appCardData.img"
                 class="mb-3 rounded-lg"
                 alt="logo" />
            <span class="text-h3 mb-1">{{ appCardData.title }}</span>
          </v-card-title>

          <v-card-subtitle
              v-if="appCardData.sub">
            <p class="body-2 mb-2">
              <span class="sub-prefix">
                {{ appCardData.subPrefix }}
              </span>
              <template v-if="appCardData.subAsLink">
                <a :href="appCardData.subLinkHref"
                   data-id="link-sub">
                  {{ appCardData.sub }}
                </a>
              </template>
              <template v-else>
                {{ appCardData.sub }}
              </template>
            </p>
          </v-card-subtitle>

          <v-card-text
              v-if="appCardData.body">
            <p class="body-1 mb-0 black--text">
              {{ appCardData.body }}
            </p>
          </v-card-text>
        </div>

        <v-card-actions
            v-if="appCardData.chips"
            class="px-4">
          <div class="chip-wrapper mb-2">
            <CDChip
                v-for="chip in appCardData.chips"
                :key="chip.id"
                :disabled="true">
              {{ chip.text }}
            </CDChip>
          </div>
        </v-card-actions>
      </v-card>
    </template>
  </v-hover>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { VCard, VCardActions, VCardSubtitle, VCardText, VCardTitle, VRating, VChip, VChipGroup, VHover } from 'vuetify/lib';
import { AppCardInterface } from 'common/types';

@Component({
  components: { VCard, VCardActions, VCardSubtitle, VCardText, VCardTitle, VRating, VChip, VChipGroup, VHover }
})

export default class AppCard extends Vue {
  @Prop() appCardData!: AppCardInterface | null;
}
</script>

<style lang="scss" scoped>
@import 'cd-system/tokens/scss/mixins';

::v-deep.v-card {
  &:hover {
    text-decoration: none !important;
  }
  cursor: pointer;
  width: 100%;
  .content-wrapper {
    width: 100%;
  }
  .theme--light.v-card {
    .v-card__text, .v-card__subtitle {
      color: inherit
    }
  }
  .v-card__title {
    align-items: start;
    width: 100%;
    > span {
      @include cd-line-clamp(2);
    }
    img {
      width: 64px;
      height: 64px;
    }
  }
  .v-card__subtitle {
    .sub-prefix, .install-count {
      color: var(--v-grey-darken2);
    }
    .install-count {
      display: inline-flex;
      transform: translateY(-2px);
    }
    p {
       @include cd-line-clamp(1);
    }
  }
  .v-card__text {
    @include cd-breakpoint('lg') {
      p {
        @include cd-line-clamp(5);
      }
    }
  }
  .v-rating {
    .v-icon {
      padding: 0;
    }
  }
  .v-card__actions .chip-wrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    gap: 8px;
  }

}
</style>
