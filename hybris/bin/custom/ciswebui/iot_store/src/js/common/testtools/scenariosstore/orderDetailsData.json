{"paymentAddress": {"id": "8796289925143", "title": null, "titleCode": null, "firstName": "<PERSON><PERSON>", "lastName": "User", "companyName": null, "line1": "Sample Street", "line2": "123", "town": "Sample Town", "region": null, "district": null, "postalCode": "12345", "phone": "+1234567890", "cellphone": null, "email": "<EMAIL>", "country": {"isocode": "DE", "name": "Germany", "currencyIsoCode": null, "canBuy": false, "sepaEnabled": false}, "shippingAddress": false, "billingAddress": true, "defaultAddress": false, "visibleInAddressBook": true, "formattedAddress": "Sample Street, 123, 12345, Sample Town, Germany"}, "totalPrice": {"symbol": "EUR", "value": "16,445.00"}, "totalTax": {"symbol": "EUR", "value": "3,124.55"}, "paymentMethod": "CREDIT_CARD", "totalPriceWithTax": {"symbol": "EUR", "value": "19,569.55"}, "entries": [{"appCode": "A_00001007", "productName": "Smoke App", "productCode": "A_00001007_full", "productUrl": "/p/A_00001007", "companyName": "De<PERSON> Apps", "versionName": "1.0.0", "licenseName": "Purchase", "licenseType": "FULL", "logoUrl": "/sample-data/logo.svg", "smallLogoUrl": "/sample-data/logo_small.jpeg", "itemPrice": {"symbol": "EUR", "value": "299.00"}, "totalPrice": {"symbol": "EUR", "value": "16,445.00"}, "quantity": 55, "entryNumber": 0}], "placedBy": "<PERSON>", "code": "00004003", "status": "completed", "date": "2020-03-08T20:15:53.879Z", "invoices": [{"externalId": "2510797230546635", "documentUrl": "/shop/order/invoices/invoice_2510797230546635_6a7ec485-8e19-493f-9a84-898bb3d7bb5a", "displayName": "20220408_2510797230546635 invoice", "netAmount": {"symbol": "EUR", "value": "16,445.00"}, "taxAmount": {"symbol": "EUR", "value": "00.00"}, "grossAmount": {"symbol": "EUR", "value": "16,445.00"}, "creationTime": "2022-04-08T12:20:01.305Z", "invoiceStatus": "PAID", "invoiceDate": "2022-04-08T00:00:00.000Z", "creditNotes": []}]}