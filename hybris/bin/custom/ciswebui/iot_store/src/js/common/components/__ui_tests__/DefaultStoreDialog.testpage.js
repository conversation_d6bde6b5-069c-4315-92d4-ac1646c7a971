import Vue from 'vue';
import vuetify from 'common/plugins/brands/azena/vuetify';
import DefaultStoreDialog from '../DefaultStoreDialog.vue';
import { CDButton, CDIcon } from 'cd-system';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import { VContainer } from 'vuetify/lib/components';

new Vue({
    el: '#vue-app',
    vuetify,
    data: {
        dialog: true
    },
    components: {
        TestPageRoot,
        DefaultStoreDialog,
        CDButton,
        CDIcon,
        VContainer
    },
    template: `
      <test-page-root>
        <v-container>
          <v-row>
            <v-col cols="12">
              <DefaultStoreDialog
                  v-model="dialog"
                  @close="dialog = false"
                  :scrollable="true"
                  scrollheight="600px"
                  :fullscreen="$vuetify.breakpoint.smAndDown">
                <template #activator="{ on, attrs }">
                  <CDButton large
                            block
                            v-bind="attrs"
                            v-on="on">
                    Compare
                  </CDButton>
                </template>

                <template #header>Products</template>

                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias animi commodi consectetur debitis
                  distinctio eaque excepturi itaque magnam maiores minus molestiae nemo non obcaecati, odit placeat sed,
                  similique velit voluptatem?</p>

                <template #actions>
                  <CDButton @click="dialog = false" color="primary">Close</CDButton>
                </template>

              </DefaultStoreDialog>
            </v-col>
          </v-row>
      </v-container>
      </test-page-root>
    `
});