import Vue from 'vue';
import Vuetify from 'vuetify/lib';

import {
    options,
    CDAccordion,
    CDRadio,
    CDCheckbox,
    CDButton,
    CDButtonText,
    CDButtonTextIcon,
    CDButtonIcon,
    CDChip,
    CDIcon,
    CDNavbar,
    CDNavbarLink,
    CDFlyoutMenu,
    CDHelpFlyout,
    CDFlyoutMenuItem,
    CDSeparator,
    CDHomeSwitcher,
    CDNotificationPersistent,
    CDNotificationToast,
    CDNotificationToastWrapper,
    CDCookieBanner,
    CDNeedHelpButton,
    CDInput,
    CDSelect,
    CDDatePicker,
    CDTextArea,
    CDToggle,
    CDBlock,
    CDFooter,
    CDDataTable,
    CDSearch,
    CDTabs,
    CDNavbar2,
    CDNavbar2Link,
    CDUserFlyout,
    CDUserFlyoutMenuItem,
    CDBento,
    CDBreadcrumbs
} from 'cd-system';
import 'cd-system/tokens/fonts/azena/fonts.css';

Vue.component('CDAccordion', CDAccordion);
Vue.component('CDRadio', CDRadio);
Vue.component('CDCheckbox', CDCheckbox);

Vue.component('CDButton', CDButton);
Vue.component('CDButtonIcon', CDButtonIcon);
Vue.component('CDButtonText', CDButtonText);
Vue.component('CDButtonTextIcon', CDButtonTextIcon);
Vue.component('CDNeedHelpButton', CDNeedHelpButton);

Vue.component('CDChip', CDChip);
Vue.component('CDIcon', CDIcon);

Vue.component('CDNavbar', CDNavbar);
Vue.component('CDNavbarLink', CDNavbarLink);
Vue.component('CDFlyoutMenu', CDFlyoutMenu);
Vue.component('CDHelpFlyout', CDHelpFlyout);
Vue.component('CDFlyoutMenuItem', CDFlyoutMenuItem);
Vue.component('CDSeparator', CDSeparator);
Vue.component('CDHomeSwitcher', CDHomeSwitcher);

Vue.component('CDNotificationPersistent', CDNotificationPersistent);
Vue.component('CDNotificationToast', CDNotificationToast);
Vue.component('CDNotificationToastWrapper', CDNotificationToastWrapper);

Vue.component('CDCookieBanner', CDCookieBanner);

Vue.component('CDInput', CDInput);
Vue.component('CDSelect', CDSelect);
Vue.component('CDTextArea', CDTextArea);
Vue.component('CDDatePicker', CDDatePicker);
Vue.component('CDToggle', CDToggle);
Vue.component('CDBlock', CDBlock);

Vue.component('CDFooter', CDFooter);
Vue.component('CDDataTable', CDDataTable);

Vue.component('CDSearch', CDSearch);

Vue.component('CDTabs', CDTabs);

Vue.component('CDNavbar2', CDNavbar2);
Vue.component('CDNavbar2Link', CDNavbar2Link);
Vue.component('CDUserFlyout', CDUserFlyout);
Vue.component('CDUserFlyoutMenuItem', CDUserFlyoutMenuItem);
Vue.component('CDBento', CDBento);
Vue.component('CDBreadcrumbs', CDBreadcrumbs);

Vue.use(Vuetify);

export default new Vuetify(options);
