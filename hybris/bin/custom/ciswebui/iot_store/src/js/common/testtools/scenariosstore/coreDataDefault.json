{"baseUrl": "/shop/", "basestore": "iotstore", "currentLanguage": "en", "defaultLanguage": "en", "defaultCurrency": "USD", "currentCurrency": "USD", "userPermissions": [], "currentCountry": {"isocode": "DE", "name": "Germany", "currencyIsoCode": "EUR", "canBuy": true, "sepaEnabled": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, "currentCompany": {"name": "De<PERSON> Apps", "friendlyName": "", "companyUid": "a7293bd2-203d-4589-bde6-0373101d8126", "companyCountry": "DE", "hasPublishedProfile": true, "profileUrl": "/company-profile/a7293bd2-203d-4589-bde6-0373101d8126", "managedAccount": false, "companyApproved": true, "distributor": {"name": "Distributor GmbH", "id": "company-id-000", "externalCustomerId": ""}, "userGroup": "thlTestUserGroup"}, "domainUrl": "some-domain.com", "userName": "<PERSON>", "httpStatus": 200, "csrfToken": "01b420ab-3c26-41ce-a0ae-3e86de21e894", "currencySymbols": {"EUR": "€", "USD": "$"}, "moduleConfig": {"ENABLE_IMPROVED_NAVIGATION": true, "ENABLE_SEPA_MANDATE": false}, "numberOfCartItems": 0, "thlAppCodeInCart": "", "camerasUrl": "https://www.google.com", "myAppsUrl": "https://duckduckgo.com", "contactUrl": "https://www.azena.com/contact", "scrollingOffsetY": 0, "alert": "", "error": false, "allowedToBuy": true, "sepaEnabled": true, "globalMessages": {"info": [], "warning": [], "error": [], "messages": []}, "supportUrl": "https://google.com", "userCompanyUnderReview": false, "infoUrl": "https://google.com", "accountsUrl": "https://google.com", "corporateHome": "https://google.com", "globalDefaultLanguage": "en", "globalFallbackCountry": "", "guideCustomerToProvideDirectDebitMandate": false, "purchaseDisabledBannerInfo": {"showPurchaseDisabledBanner": false, "date": ""}, "navigationItems": [{"id": "appProductCategory", "itemCode": "appProductCategory", "url": "/shop", "type": "STORE", "group": "PRODUCT_CATEGORIES", "text": "Apps", "description": "", "target": "_self", "icon": "", "index": 1, "entryPage": "", "customAttributes": {}}, {"id": "toolProductCategory", "itemCode": "toolProductCategory", "url": "/shop/tools", "type": "STORE", "group": "PRODUCT_CATEGORIES", "text": "<PERSON><PERSON><PERSON>", "description": "", "target": "_self", "icon": "", "index": 2, "entryPage": "", "customAttributes": {}}, {"url": "https://www.google.com", "type": "GLOBAL", "group": "HEADER", "text": "My Profile", "target": "_blank", "icon": "user", "index": 1, "id": "myProfile", "itemCode": "myProfile", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "https://duckduckgo.com", "type": "GLOBAL", "group": "HEADER", "text": "My Company", "target": "_blank", "icon": "company", "index": 2, "id": "myCompany", "itemCode": "myCompany", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "/shop/my-account/orders", "type": "STORE", "group": "HEADER", "text": "Order History", "target": "", "icon": "$user", "index": 3, "id": "storeOrderHistory", "itemCode": "storeOrderHistory", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "/shop/my-account/payment-details", "type": "STORE", "group": "HEADER", "text": "Payment Details", "target": "", "icon": "$payment", "index": 4, "id": "storePaymentDetails", "itemCode": "storePaymentDetails", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "https://support.azena.com/hc", "type": "GLOBAL", "group": "HEADER", "text": "Support", "target": "_blank", "icon": "support", "index": 5, "id": "globalSupport", "itemCode": "globalSupport", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "/shop/logout", "type": "STORE", "group": "HEADER", "text": "Sign Out", "target": "", "icon": "$logout", "index": 6, "id": "storeSignOut", "itemCode": "storeSignOut", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "https://accounts.azena.com/legal/terms.html", "type": "STORE", "group": "FOOTER", "text": "Terms and Conditions", "target": "_blank", "icon": "", "index": 1, "id": "termsAndConditions", "itemCode": "storeTermsAndConditions", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "https://www.azena.com/privacy-policy", "type": "STORE", "group": "FOOTER", "text": "Privacy Policy", "target": "_blank", "icon": "", "index": 2, "id": "privacyPolicy", "itemCode": "storePrivacyPolicy", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "https://azena.com/legal-notice", "type": "GLOBAL", "group": "FOOTER", "text": "Legal", "target": "_blank", "icon": "", "index": 3, "id": "legal", "itemCode": "globalLegal", "entryPage": "", "description": "", "customAttributes": {}}, {"url": "https://azena.com/imprint", "type": "GLOBAL", "group": "FOOTER", "text": "Imprint", "target": "_blank", "icon": "", "index": 5, "id": "imprint", "itemCode": "globalImprint", "entryPage": "", "description": "", "customAttributes": {}}, {"id": "deviceManagement", "url": "http://localhost:8091/page/cameras", "type": "STORE", "group": "HOME_SWITCHER", "text": "Device Management Portal", "description": "", "target": "_blank", "icon": "", "index": 10, "entryPage": "", "itemCode": "deviceManagement", "customAttributes": {}}, {"id": "storeHelpStoreDocs", "url": "https://support.azena.com/hc/sections/360011381714-Application-Store", "type": "STORE", "group": "HELP", "text": "Application Store help", "description": "Find help articles about Application Store.", "target": "_blank", "icon": "/sample-data/help-and-resources/store-docs.svg", "index": 1, "entryPage": "", "itemCode": "storeHelpStoreDocs", "customAttributes": {}}, {"id": "storeHelpAzenaHome", "url": "https://www.azena.com", "type": "STORE", "group": "HELP", "text": "Azena home", "description": "Visit our website to learn more about <PERSON>zen<PERSON> and our offering.", "target": "_blank", "icon": "/sample-data/help-and-resources/azena-home.svg", "index": 2, "entryPage": "", "itemCode": "storeHelpAzenaHome", "customAttributes": {}}, {"id": "storeHelpPlatformOverview", "url": "https://www.azena.com/our-platform", "type": "STORE", "group": "HELP", "text": "Our platform", "description": "Get an overview of available tools and products.", "target": "_blank", "icon": "/sample-data/help-and-resources/our-platform.svg", "index": 3, "entryPage": "", "itemCode": "storeHelpPlatformOverview", "customAttributes": {}}, {"id": "storeHelpCompatibleCameras", "url": "https://www.azena.com/cameras", "type": "STORE", "group": "HELP", "text": "Compatible cameras", "description": "Browse and buy cameras with Azena OS.", "target": "_blank", "icon": "/sample-data/help-and-resources/compatible-cameras.svg", "index": 4, "entryPage": "", "itemCode": "storeHelpCompatibleCameras", "customAttributes": {}}, {"id": "storeHelpAndResources", "url": "/shop/help-resources", "type": "STORE", "group": "HEADER", "text": "Help & Resources", "description": "", "target": "", "icon": "$help", "index": 5, "entryPage": "", "itemCode": "storeHelpAndResources", "customAttributes": {}}, {"id": "globalHelpSupport", "url": "https://support.azena.com/hc", "type": "GLOBAL", "group": "HELP", "text": "Visit Help & Support", "description": "", "target": "_blank", "icon": "", "index": 0, "entryPage": "", "itemCode": "globalHelpSupport", "customAttributes": {}}, {"id": "globalHelpContact", "url": "https://support.azena.com/hc/requests/new", "type": "GLOBAL", "group": "HELP", "text": "Contact Us", "description": "", "target": "_blank", "icon": "", "index": 1, "entryPage": "", "itemCode": "globalHelpContact", "customAttributes": {}}], "allowedMaxQuantityLineItemFullLicense": 1000, "allowedMaxQuantityLineItemSubsLicense": 1000, "activeCountries": [{"isocode": "BE", "name": "Belgium", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "AU", "name": "Australia", "canBuy": false, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "ACH_INTERNATIONAL"]}, {"isocode": "AT", "name": "Austria", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_DIRECTDEBIT", "SEPA_CREDIT"]}, {"isocode": "BG", "name": "Bulgaria", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "CA", "name": "Canada", "canBuy": true, "blockedCountriesCommercial": ["CA"], "supportedPaymentMethodTypes": ["CREDIT_CARD", "ACH_INTERNATIONAL"]}, {"isocode": "CH", "name": "Switzerland", "canBuy": true, "blockedCountriesCommercial": ["CH"], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "CY", "name": "Cyprus", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "CZ", "name": "Czech Republic", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "DE", "name": "Germany", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_DIRECTDEBIT", "SEPA_CREDIT"]}, {"isocode": "DK", "name": "Denmark", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "EE", "name": "Estonia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "ES", "name": "Spain", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "FI", "name": "Finland", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "FR", "name": "France", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "GB", "name": "United Kingdom", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "GR", "name": "Greece", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "HR", "name": "Croatia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "HU", "name": "Hungary", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "IE", "name": "Ireland", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "IN", "name": "India", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "IT", "name": "Italy", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "KR", "name": "Republic of Korea", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "LT", "name": "Lithuania", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "LU", "name": "Luxembourg", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "LV", "name": "Latvia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "MT", "name": "Malta", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "NO", "name": "Norway", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "NL", "name": "Netherlands", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "PL", "name": "Poland", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "RO", "name": "Romania", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "PT", "name": "Portugal", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "SI", "name": "Slovenia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "SE", "name": "Sweden", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "SK", "name": "Slovakia", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "TW", "name": "Taiwan", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "SEPA_CREDIT"]}, {"isocode": "US", "name": "United States of America", "canBuy": true, "blockedCountriesCommercial": [], "supportedPaymentMethodTypes": ["CREDIT_CARD", "ACH_INTERNATIONAL"]}], "listOfDistributors": [{"id": "id_1", "name": "Zeta distributor"}, {"id": "id_2", "name": "Alpha distributor"}, {"id": "id_3", "name": "Gamma distributor"}, null, {"id": "id_4", "name": "Beta distributor"}], "translationConfig": {"remoteTranslationsEnabled": false, "baseUrl": "", "projectId": "", "cdsProjectId": ""}, "idp": {"loginIdp": "", "loginIdpAccountUrl": ""}}