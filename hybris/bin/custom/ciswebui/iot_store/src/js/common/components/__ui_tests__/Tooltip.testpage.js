import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import Tooltip from 'common/components/Tooltip';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    data: {
        hintText: {
            short: 'Short text in one line.',
            long: 'Very long text which uses max-width and needs to be wrapped to be displayed properly.'
        }
    },
    components: {
        TestPageRoot,
        Tooltip
    },
    methods: {},
    template: `
      <test-page-root>
        <div style="width: 300px;">
          <tooltip v-bind:text="hintText.short" style="float: right; display: block; clear: both;">
            <div id="text">
              Hover me
            </div>
          </tooltip>
          <div style="margin: 20px; float: right; display: block; clear: both;"></div>
          <tooltip v-bind:text="hintText.long" style="float: right; display: block; clear: both;">
            <div style="width: 117px; height: 30px;  background-image: url('/sample-data/logo.svg');" id="icon">
            </div>
          </tooltip>
        </div>
      </test-page-root>
    `
});
