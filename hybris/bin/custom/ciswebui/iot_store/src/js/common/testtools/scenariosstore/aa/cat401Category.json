{"code": "cat_401", "name": "CRI/CRIN", "order": 4, "iconCode": null, "picture": {"url": "/sample-data/aa/categories/cat_401-picture.jpeg", "altText": null}, "thumbnail": {"url": "/sample-data/aa/categories/cat_401-thumbnail.jpeg", "altText": null}, "subcategories": [{"code": "cat_40101", "name": "Prüfwerte", "order": 1, "iconCode": null, "picture": null, "thumbnail": null, "subcategories": [], "products": [{"code": "AA_04012500", "name": "Infoart W <PERSON>tlizenz", "url": null, "description": "<p>Die <b>ESI[tronic] 2.0-Infoart W</b> beinhaltet Informationen zu Diesel Prüfwerten für Reihenpumpen-Kombinationen sowie für VE-Pumpen, den kompletten Prüfvorgang von der Messwerteermittlung bis zum Protokollausdruck und die Anzeige der Prüfschritte in optimaler Reihenfolge.</p>", "summary": "Beinhaltet Informationen zu Diesel-Einspritzpumpen und Prüfwert von <PERSON>", "minPrice": {"currencyIso": "EUR", "value": 310.0, "formattedValue": " 310,00"}, "billsOfMaterials": [{"code": "MAT_040A", "name": "A (Allgemeine Fahrzeuginformationen)", "description": "Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.", "videoUrl": null}, {"code": "MAT_040SDSDA", "name": "SD incl. SDA (Diagnose)", "description": "Zeigt verbaute Systeme sowie gespeicherte Fehlercodes incl SDA und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt <PERSON>t-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.", "videoUrl": "https://www.youtube.com/watch?v=uWV9dySPUcs"}, {"code": "MAT_040EBR", "name": "EBR (Erfahrungsbasierte Reparatur)", "description": "Enthält eine Beschreibung bekannter Fehler am individuellen Fahrzeug, sortiert nach System (ABS, Airbag, Motorsteuerung usw.) und nach Symptomen. Jeder Fehler wird mit seiner Ursache beschrieben, und es wird angegeben, welche Maßnahmen für eine Reparatur empfohlen werden.", "videoUrl": "https://www.youtube.com/watch?v=mWKh2gnCGH0"}, {"code": "MAT_040TSB", "name": "Technische Service Info", "description": null, "videoUrl": null}], "features": [{"code": "aaClassificationCatalog/1.0/100.hardware-requirements,100", "name": "Benötigte Hardware", "featureValues": [{"value": "KTS 560/590", "iconUrl": "/sample-data/aa/products/hardware-features/kts-560.png"}]}, {"code": "aaClassificationCatalog/1.0/100.package,100", "name": "<PERSON><PERSON>", "featureValues": [{"value": "Reparatur-Info"}]}, {"code": "aaClassificationCatalog/1.0/100.license-types,100", "name": "Lizenztyp", "featureValues": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"code": "aaClassificationCatalog/1.0/100.vehicle-types,100", "name": "Fahrzeugtyp", "featureValues": [{"value": "PKW"}]}]}, {"code": "AA_04015015", "name": "Infoart Testdata", "url": null, "description": "<p>Die <b>ESI[tronic] 2.0-Infoart Testdata (CD)</b> enthält Prüfwerte für Bosch Common Rail Hochdruckpumpen, Common Rail Injektoren und VP 29 / 30 / 44 Verteilereinspritzpumpen.</p>", "summary": "Umfasst Prüfwerte zu Diesel-Common-Rail-Einspritzpumpen und Injektoren von <PERSON>", "minPrice": {"currencyIso": "EUR", "value": 310.0, "formattedValue": " 310,00"}, "billsOfMaterials": [{"code": "MAT_040A", "name": "A (Allgemeine Fahrzeuginformationen)", "description": "Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.", "videoUrl": null}, {"code": "MAT_040SDSDA", "name": "SD incl. SDA (Diagnose)", "description": "Zeigt verbaute Systeme sowie gespeicherte Fehlercodes incl SDA und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt <PERSON>t-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.", "videoUrl": "https://www.youtube.com/watch?v=uWV9dySPUcs"}, {"code": "MAT_040EBR", "name": "EBR (Erfahrungsbasierte Reparatur)", "description": "Enthält eine Beschreibung bekannter Fehler am individuellen Fahrzeug, sortiert nach System (ABS, Airbag, Motorsteuerung usw.) und nach Symptomen. Jeder Fehler wird mit seiner Ursache beschrieben, und es wird angegeben, welche Maßnahmen für eine Reparatur empfohlen werden.", "videoUrl": "https://www.youtube.com/watch?v=mWKh2gnCGH0"}, {"code": "MAT_040TSB", "name": "Technische Service Info", "description": null, "videoUrl": null}], "features": [{"code": "aaClassificationCatalog/1.0/100.hardware-requirements,100", "name": "Benötigte Hardware", "featureValues": [{"value": "KTS 560/590", "iconUrl": "/sample-data/aa/products/hardware-features/kts-560.png"}]}, {"code": "aaClassificationCatalog/1.0/100.package,100", "name": "<PERSON><PERSON>", "featureValues": [{"value": "Reparatur-Info"}]}, {"code": "aaClassificationCatalog/1.0/100.license-types,100", "name": "Lizenztyp", "featureValues": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"code": "aaClassificationCatalog/1.0/100.vehicle-types,100", "name": "Fahrzeugtyp", "featureValues": [{"value": "PKW"}]}]}, {"code": "AA_04012640", "name": "Infoart W Zusatzlizenz", "url": null, "description": "<p><p><b><em>Bestellbar nur in Verbindung mit der Hauptlizenz.</em></b></p> Die <b>ESI[tronic] 2.0-Infoart W</b> beinhaltet Informationen zu Diesel Prüfwerten für Reihenpumpen-Kombinationen sowie für VE-Pumpen, den kompletten Prüfvorgang von der Messwerteermittlung bis zum Protokollausdruck und die Anzeige der Prüfschritte in optimaler Reihenfolge.</p>", "summary": "Beinhaltet Informationen zu Diesel-Einspritzpumpen und Prüfwert von <PERSON>", "minPrice": {"currencyIso": "EUR", "value": 310.0, "formattedValue": " 310,00"}, "billsOfMaterials": [{"code": "MAT_040A", "name": "A (Allgemeine Fahrzeuginformationen)", "description": "Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.", "videoUrl": null}, {"code": "MAT_040SDSDA", "name": "SD incl. SDA (Diagnose)", "description": "Zeigt verbaute Systeme sowie gespeicherte Fehlercodes incl SDA und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt <PERSON>t-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.", "videoUrl": "https://www.youtube.com/watch?v=uWV9dySPUcs"}, {"code": "MAT_040EBR", "name": "EBR (Erfahrungsbasierte Reparatur)", "description": "Enthält eine Beschreibung bekannter Fehler am individuellen Fahrzeug, sortiert nach System (ABS, Airbag, Motorsteuerung usw.) und nach Symptomen. Jeder Fehler wird mit seiner Ursache beschrieben, und es wird angegeben, welche Maßnahmen für eine Reparatur empfohlen werden.", "videoUrl": "https://www.youtube.com/watch?v=mWKh2gnCGH0"}, {"code": "MAT_040TSB", "name": "Technische Service Info", "description": null, "videoUrl": null}], "features": [{"code": "aaClassificationCatalog/1.0/100.hardware-requirements,100", "name": "Benötigte Hardware", "featureValues": [{"value": "KTS 560/590", "iconUrl": "/sample-data/aa/products/hardware-features/kts-560.png"}]}, {"code": "aaClassificationCatalog/1.0/100.package,100", "name": "<PERSON><PERSON>", "featureValues": [{"value": "Reparatur-Info"}]}, {"code": "aaClassificationCatalog/1.0/100.license-types,100", "name": "Lizenztyp", "featureValues": [{"value": "Zusatzlizenz"}]}, {"code": "aaClassificationCatalog/1.0/100.vehicle-types,100", "name": "Fahrzeugtyp", "featureValues": [{"value": "PKW"}]}]}]}], "products": [{"code": "AA_04015090", "name": "CRI Bosch Hauptlizenz", "url": null, "description": "<p>Die <b>ESI[tronic] CRI für DCI 700</b> Software liefert aktuelle Daten, sorgt für reibungslose Abläufe und umfasst die Prüfung von Piezo-Injektoren für Common Rail Systeme.</p>", "summary": "Umfasst die Prüfung von Piezo-Injektoren für Common Rail Systeme", "minPrice": {"currencyIso": "EUR", "value": 310.0, "formattedValue": " 310,00"}, "billsOfMaterials": [{"code": "MAT_040A", "name": "A (Allgemeine Fahrzeuginformationen)", "description": "Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.", "videoUrl": null}, {"code": "MAT_040SDSDA", "name": "SD incl. SDA (Diagnose)", "description": "Zeigt verbaute Systeme sowie gespeicherte Fehlercodes incl SDA und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt <PERSON>t-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.", "videoUrl": "https://www.youtube.com/watch?v=uWV9dySPUcs"}, {"code": "MAT_040EBR", "name": "EBR (Erfahrungsbasierte Reparatur)", "description": "Enthält eine Beschreibung bekannter Fehler am individuellen Fahrzeug, sortiert nach System (ABS, Airbag, Motorsteuerung usw.) und nach Symptomen. Jeder Fehler wird mit seiner Ursache beschrieben, und es wird angegeben, welche Maßnahmen für eine Reparatur empfohlen werden.", "videoUrl": "https://www.youtube.com/watch?v=mWKh2gnCGH0"}, {"code": "MAT_040TSB", "name": "Technische Service Info", "description": null, "videoUrl": null}], "features": [{"code": "aaClassificationCatalog/1.0/100.hardware-requirements,100", "name": "Benötigte Hardware", "featureValues": [{"value": "DCI 700", "iconUrl": "/sample-data/aa/products/hardware-features/dci-700.png"}]}, {"code": "aaClassificationCatalog/1.0/100.package,100", "name": "<PERSON><PERSON>", "featureValues": [{"value": "Hardwarespezifisch"}]}, {"code": "aaClassificationCatalog/1.0/100.license-types,100", "name": "Lizenztyp", "featureValues": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"code": "aaClassificationCatalog/1.0/100.vehicle-types,100", "name": "Fahrzeugtyp", "featureValues": [{"value": "PKW"}]}]}, {"code": "AA_04015105", "name": "CRIN Bosch Z<PERSON>z", "url": null, "description": "<p><p><b><em>Bestellbar nur in Verbindung mit der Hauptlizenz.</em></b></p> Die <b>ESI[tronic] CRIN für DCI 700</b> Software liefert aktuelle Daten, sorgt für reibungslose Abläufe und umfasst die Prüfung von Magnetventil-Injektoren für Common Rail Systeme.</p>", "summary": "Umfasst die Prüfung von Magnetventil-Injektoren für Common Rail Systeme", "minPrice": {"currencyIso": "EUR", "value": 310.0, "formattedValue": " 310,00"}, "billsOfMaterials": [{"code": "MAT_040A", "name": "A (Allgemeine Fahrzeuginformationen)", "description": "Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.", "videoUrl": null}, {"code": "MAT_040SDSDA", "name": "SD incl. SDA (Diagnose)", "description": "Zeigt verbaute Systeme sowie gespeicherte Fehlercodes incl SDA und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt <PERSON>t-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.", "videoUrl": "https://www.youtube.com/watch?v=uWV9dySPUcs"}, {"code": "MAT_040EBR", "name": "EBR (Erfahrungsbasierte Reparatur)", "description": "Enthält eine Beschreibung bekannter Fehler am individuellen Fahrzeug, sortiert nach System (ABS, Airbag, Motorsteuerung usw.) und nach Symptomen. Jeder Fehler wird mit seiner Ursache beschrieben, und es wird angegeben, welche Maßnahmen für eine Reparatur empfohlen werden.", "videoUrl": "https://www.youtube.com/watch?v=mWKh2gnCGH0"}, {"code": "MAT_040TSB", "name": "Technische Service Info", "description": null, "videoUrl": null}], "features": [{"code": "aaClassificationCatalog/1.0/100.hardware-requirements,100", "name": "Benötigte Hardware", "featureValues": [{"value": "DCI 700", "iconUrl": "/sample-data/aa/products/hardware-features/dci-700.png"}]}, {"code": "aaClassificationCatalog/1.0/100.package,100", "name": "<PERSON><PERSON>", "featureValues": [{"value": "Hardwarespezifisch"}]}, {"code": "aaClassificationCatalog/1.0/100.license-types,100", "name": "Lizenztyp", "featureValues": [{"value": "Zusatzlizenz"}]}, {"code": "aaClassificationCatalog/1.0/100.vehicle-types,100", "name": "Fahrzeugtyp", "featureValues": [{"value": "PKW"}]}]}, {"code": "AA_04015095", "name": "CRI Bosch <PERSON>", "url": null, "description": "<p><p><b><em>Bestellbar nur in Verbindung mit der Hauptlizenz.</em></b></p> Die <b>ESI[tronic] CRI für DCI 700</b> Software liefert aktuelle Daten, sorgt für reibungslose Abläufe und umfasst die Prüfung von Piezo-Injektoren für Common Rail Systeme.</p>", "summary": "Umfasst die Prüfung von Piezo-Injektoren für Common Rail Systeme", "minPrice": {"currencyIso": "EUR", "value": 310.0, "formattedValue": " 310,00"}, "billsOfMaterials": [{"code": "MAT_040A", "name": "A (Allgemeine Fahrzeuginformationen)", "description": "Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.", "videoUrl": null}, {"code": "MAT_040SDSDA", "name": "SD incl. SDA (Diagnose)", "description": "Zeigt verbaute Systeme sowie gespeicherte Fehlercodes incl SDA und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt <PERSON>t-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.", "videoUrl": "https://www.youtube.com/watch?v=uWV9dySPUcs"}, {"code": "MAT_040EBR", "name": "EBR (Erfahrungsbasierte Reparatur)", "description": "Enthält eine Beschreibung bekannter Fehler am individuellen Fahrzeug, sortiert nach System (ABS, Airbag, Motorsteuerung usw.) und nach Symptomen. Jeder Fehler wird mit seiner Ursache beschrieben, und es wird angegeben, welche Maßnahmen für eine Reparatur empfohlen werden.", "videoUrl": "https://www.youtube.com/watch?v=mWKh2gnCGH0"}, {"code": "MAT_040TSB", "name": "Technische Service Info", "description": null, "videoUrl": null}], "features": [{"code": "aaClassificationCatalog/1.0/100.hardware-requirements,100", "name": "Benötigte Hardware", "featureValues": [{"value": "DCI 700", "iconUrl": "/sample-data/aa/products/hardware-features/dci-700.png"}]}, {"code": "aaClassificationCatalog/1.0/100.package,100", "name": "<PERSON><PERSON>", "featureValues": [{"value": "Hardwarespezifisch"}]}, {"code": "aaClassificationCatalog/1.0/100.license-types,100", "name": "Lizenztyp", "featureValues": [{"value": "Zusatzlizenz"}]}, {"code": "aaClassificationCatalog/1.0/100.vehicle-types,100", "name": "Fahrzeugtyp", "featureValues": [{"value": "PKW"}]}]}, {"code": "AA_04015100", "name": "CRIN Bosch Hauptlizenz", "url": null, "description": "<p>Die <b>ESI[tronic] CRIN für DCI 700</b> Software liefert aktuelle Daten, sorgt für reibungslose Abläufe und umfasst die Prüfung von Magnetventil-Injektoren für Common Rail Systeme.</p>", "summary": "Umfasst die Prüfung von Magnetventil-Injektoren für Common Rail Systeme", "minPrice": {"currencyIso": "EUR", "value": 310.0, "formattedValue": " 310,00"}, "billsOfMaterials": [{"code": "MAT_040A", "name": "A (Allgemeine Fahrzeuginformationen)", "description": "Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.", "videoUrl": null}, {"code": "MAT_040SDSDA", "name": "SD incl. SDA (Diagnose)", "description": "Zeigt verbaute Systeme sowie gespeicherte Fehlercodes incl SDA und Kilometerstände in einer Übersicht, löscht Fehlercodes, zeigt <PERSON>t-Werte an und bereitet diese grafisch auf, aktiviert Stellglieder, setzt Service-Intervalle zurück, führt automatisierte Tests durch, kalibriert Sensoren und Systeme und lernt Komponenten an.", "videoUrl": "https://www.youtube.com/watch?v=uWV9dySPUcs"}, {"code": "MAT_040EBR", "name": "EBR (Erfahrungsbasierte Reparatur)", "description": "Enthält eine Beschreibung bekannter Fehler am individuellen Fahrzeug, sortiert nach System (ABS, Airbag, Motorsteuerung usw.) und nach Symptomen. Jeder Fehler wird mit seiner Ursache beschrieben, und es wird angegeben, welche Maßnahmen für eine Reparatur empfohlen werden.", "videoUrl": "https://www.youtube.com/watch?v=mWKh2gnCGH0"}, {"code": "MAT_040TSB", "name": "Technische Service Info", "description": null, "videoUrl": null}], "features": [{"code": "aaClassificationCatalog/1.0/100.hardware-requirements,100", "name": "Benötigte Hardware", "featureValues": [{"value": "DCI 700", "iconUrl": "/sample-data/aa/products/hardware-features/dci-700.png"}]}, {"code": "aaClassificationCatalog/1.0/100.package,100", "name": "<PERSON><PERSON>", "featureValues": [{"value": "Hardwarespezifisch"}]}, {"code": "aaClassificationCatalog/1.0/100.license-types,100", "name": "Lizenztyp", "featureValues": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"code": "aaClassificationCatalog/1.0/100.vehicle-types,100", "name": "Fahrzeugtyp", "featureValues": [{"value": "PKW"}]}]}]}