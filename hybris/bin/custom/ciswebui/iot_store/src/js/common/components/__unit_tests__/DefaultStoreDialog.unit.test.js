import {wrapperComponentFactory, resizeWidth} from 'common/testtools/unit-test-utils';
import DefaultStoreDialog from '../DefaultStoreDialog';

const mountDefaultStoreDialog = (props, slots) => wrapperComponentFactory(DefaultStoreDialog, {
    shallow: false,
    props: {
        value: true,
        ...props
    },
    slots: {
        default: 'default slot content',
        ...slots
    }
});

describe('DefaultStoreDialog', () => {

    it('is is not visible', async () => {
        const wrapper = await mountDefaultStoreDialog({
            closeicon: '$test123',
            closeiconmobile: '$test456'
        });
        expect(wrapper.exists()).toBeTruthy();
    });

    it('renders content when value set to true', async () => {
       const wrapper = await mountDefaultStoreDialog({
           value: true
       });
       expect(wrapper.vm.$slots.default[0].text).toBe('default slot content');
    });

    it('renders heading and custom icon component for mobile', async () => {
        resizeWidth(400);
        const mobileWrapper = await mountDefaultStoreDialog(
            {
                closeiconmobile: '$menu'
            },
            {
                header: 'Products'
            }
        );
        const mobileHeaderComponent = mobileWrapper.find('[data-id="button-dialog-close-mobile"]');
        const mobileHeaderText = mobileWrapper.find('[data-id="text-dialog-title-mobile"]');

        expect(mobileHeaderComponent.find('v-icon').text()).toBe('$menu');
        expect(mobileHeaderComponent.exists()).toBe(true);
        expect(mobileHeaderText.element.tagName).toBe('H3');
    });

    it('only renders actions slot if it is populated', () => {
        const wrapper = mountDefaultStoreDialog({}, {
            actions: '<CDButton>An action button</CDButton>'
        });
        expect(wrapper.vm.$slots.actions[0].componentOptions.tag).toBe('CDButton');
        expect(wrapper.vm.$slots.actions[0].componentOptions.children[0].text).toBe('An action button');
    });

});