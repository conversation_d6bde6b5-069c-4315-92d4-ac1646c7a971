export interface cardBrandChangePayload {
  type: string;
  fields: {
    code: {
      label: string
    };
  };
};

export interface ibanTypeChangePayload {
  type: string;
  fields: {
    enabled: boolean;
    code: string;
    name: string;
    pattern: string;
    length: number;
    checksum: boolean;
    sepa: boolean;
    bicMandatory: boolean;
  };
};

export interface  validationChangePayload {
  validation: {
    valid: boolean;
    potentialValid: boolean;
    accepted: boolean;
    completed: boolean;
  };
  field: {
    name: string;
    label: string;
    selector: string;
  };
};

export interface formValidationChangePayload {
  valid: boolean;
  completed: boolean;
};

export interface focusChangePayload {
  focused: boolean;
  field: {
    selector: string;
    label: string;
    name: string;
  };
};

export interface activeStateChangePayload {
  isActive: boolean;
};

export interface submitRequestPayload {
  key: number;
  field: {
    name: string;
  };
};

export type IFrameEvent =
    | "cardBrandChange"
    | "ibanTypeChange"
    | "validationChange"
    | "formValidationChange"
    | "focusChange"
    | "activeStateChange"
    | "submitRequest"
    | "styleChange";

export enum FormEvent {
    CARD_BRAND_CHANGE = "cardBrandChange",
    IBAN_TYPE_CHANGE = "ibanTypeChange",
    VALIDATION_CHANGE = "validationChange",
    FORM_VALIDATION_CHANGE = "formValidationChange",
    FOCUS_CHANGE = "focusChange",
    ACTIVE_STATE_CHANGE = "activeStateChange",
    SUBMIT_REQUEST_CHANGE = "submitRequest",
    STYLE_CHANGE = "styleChange",
}

export type IFrameEventPayload =
    | cardBrandChangePayload
    | ibanTypeChangePayload
    | validationChangePayload
    | focusChangePayload
    | formValidationChangePayload
    | activeStateChangePayload
    | submitRequestPayload;

export type IFrameEventCallback = (event: IFrameEventPayload) => void;

export interface Field {
  selector: string;
  placeholder?: string;
  required?: boolean;
};

export interface CreditCardOptions {
  holder: Field;
  number: Field;
  code: Field;
  expiry: Field;
};

export enum CreditCardOptionsName {
  HOLDER = 'holder',
  NUMBER = 'number',
  CODE = 'code',
  EXPIRY = 'expiry',
}

export interface BankAccountOptions {
  holder: Field;
  iban: Field;
  bic: Field;
};

export interface AliasCreditCard {
  code: Field;
};

/**
 * the styles object for custom styles
 * @example
 * var styles {
 *   "input": {
 *     "font-size": "16px",
 *     "color": "#444444",
 *     "font-family": "monospace"
 *   },
 *   ".ifs-valid": {
 *     "color": "Green"
 *   },
 *   ".ifs-invalid":{
 *     "color": "Crimson"
 *   },
 *   ".ifs-not-accepted":{
 *     "color": "DarkGoldenRod"
 *   }
 * };
 * var options {
 *   // ...
 *   styles: styles
 * };
 */
export interface Styles {
  [key: string]: {
    [key: string]: string;
  };
};

export interface IntegrationCreateTokenResponse {
  token: string;
  info: {
    creditCard?: {
      expiry: string;
      holder: string;
      paddedPan: string;
      tx3DSecureAuthLevel: string;
    };
    bankAccount?: {
      paddedIban: string;
      bic?: string;
      bankName?: string;
    };
  };
  type: string;
};

export interface IntegrationCreateTokenError {
  category: string;
  detail: string;
  type: string;
}

export interface IntegrationOptions {
  clientSession: string;
  clientConfiguration: string;
  type: "creditCard" | "bankAccount" | "aliasCreditCard";
  fields: CreditCardOptions | BankAccountOptions | AliasCreditCard;
  styles: Styles;
  acs_window?: {
    height: number;
    width: number;
  };
};

export enum IntegrationOptionsName {
  CREDIT_CARD = 'creditCard',
  BANK_ACCOUNT = 'bankAccount',
  ALIAS_CREDIT_CARD = 'aliasCreditCard'
}

// export class Integration {
//   constructor(options: Integration.options);
//   on(event: IFrameEvent, callback: IFrameEventCallback): void;
//   /**
//    * Returns true if integration instance is active. This is only necessary if you use more than one
//    * integration instance (i.e. credit card and bank account form).
//    * @returns true if instance is active otherwise false
//    */
//   public isActive(): boolean;
//   /**
//    * Returns the state of validation. A form is valid of all required fields are completed,
//    * the payment type is accepted and all fields are valid.
//    * @returns true if form is valid otherwise false
//    */
//   public isValid(): boolean;
//   /**
//    * Create a credit card or a bank details token. The result is a {@link Promise} with a good and bad path.
//    * @param [createTokenOptions] - createTokenOptions additional options for token creation
//    * @param createTokenOptions.holder - optional card or bank account holder
//    */
//   public createToken(createTokenOptions?: {
//     holder: string;
//   }): Promise<IntegrationCreateTokenResponse>;
//   /**
//    * Clear all input fields that are associated with the instance
//    */
//   public clearFields(): void;
// }