import { ShopDomains } from 'common/constants';

export const TransformExternalLinks = {
  inserted: function(el: HTMLElement) {
      const anchorsList: NodeListOf<HTMLAnchorElement> = el.querySelectorAll('a');
      const anchorsArray: HTMLAnchorElement[] = Array.from(anchorsList);
      const domains = Object.values(ShopDomains);
      anchorsArray.forEach(a => {
          if(!domains.some(domain => a.href.includes(domain))) {
              a.setAttribute('target', '_blank');
              a.setAttribute('rel', 'noopener');
          }
      });
  }
};