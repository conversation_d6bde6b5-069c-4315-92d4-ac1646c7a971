import {VueSpinner} from './SpinnerTypes';
import PageSpinner from './PageSpinner.vue';

function mountSpinner(ctor: { new(options: object): VueSpinner }) {
    let instance = new ctor({});
    instance.$mount();
    window.document.body.appendChild(instance.$el);
    return {
        /**
         * @param delay For interaction below this threshold we do not show a spinner (We assume the user interactions are slower than this threshold).
         * @param msg Message to be shown while spinner is visible.
         */
        start(delay: number = 300, msg: string = '') {
            return instance.start(delay, msg);
        },
        /**
         *  @param delay The default is not to annoy the user with useless spinners but to prevent flickering effects which are considered more annoying.
         *  @param killrunningstart If true: If start has been called with delay and animation has not started yet, it will be discarded
         *  and no spinner appears at all. Delay has no effect in this case.
         */
        stop(delay: number = 450, killrunningstart=true) {
            return instance.stop(delay, killrunningstart);
        },
        get isActive() {
            return instance.isActive;
        }
    };
}

export const pageSpinner = mountSpinner(PageSpinner);
