import {LicenseType} from 'common/types';

let licenseTypeService = {

    isThirdPartyLicense(licenseType: LicenseType): boolean {
        return licenseType !== LicenseType.TOOL;
    },
    isTrialLicense(licenseType: LicenseType): boolean {
        return licenseType === LicenseType.EVALUATION;
    },
    isCommercialLicense(licenseType: LicenseType): boolean {
        return this.isThirdPartyLicense(licenseType) && !this.isTrialLicense(licenseType);
    }

};

export {licenseTypeService}