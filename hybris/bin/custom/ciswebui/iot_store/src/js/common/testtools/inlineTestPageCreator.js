let createInlineUiTestPage = async function (scenario) {
    let frontendData = {
        coreData: scenario.coreData,
        pageData: scenario.pageData
    };

    let url = scenario.url;

    await page.goto(__DEV_ADDRESS__ + url, { timeout: 120000 });

    await page.evaluate((frontendData) => {
        window.frontendData = frontendData
    }, frontendData);

    await page.evaluate(() => {
        return new Promise(resolve => {
            const interval = setInterval(() => {
                if (typeof window.createStoreApp === 'undefined') return;
                clearInterval(interval);
                window.createStoreApp();
                resolve();
            }, 100);
        });
    });
}.bind(this);

const scrollIntoView = async (el) => {
  el.scrollIntoView({ block: 'nearest', inline: 'start'});
};

const scrollDown = async () => {
    await page.evaluate(async () => {
        let scrollPosition = 0
        let documentHeight = document.body.scrollHeight

        while (documentHeight > scrollPosition) {
            window.scrollBy(0, documentHeight)
            await new Promise(resolve => {
                setTimeout(resolve, 1000)
            })
            scrollPosition = documentHeight
            documentHeight = document.body.scrollHeight
        }
    });
}

const acceptCookieBanner = async () => {
    if (await page.$('[data-id="button-cookie-accept"]') !== null) {
        await page.click('[data-id="button-cookie-accept"]');
    }
}

const resetMouseCoords = async () => {
    await page.mouse.move(0, 0);
}

export {createInlineUiTestPage, scrollIntoView, scrollDown, acceptCookieBanner, resetMouseCoords};
