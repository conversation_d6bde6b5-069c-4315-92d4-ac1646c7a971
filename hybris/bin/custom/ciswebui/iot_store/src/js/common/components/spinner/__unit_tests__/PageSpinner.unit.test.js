import {mount} from '@vue/test-utils';
import PageSpinner from 'common/components/spinner/PageSpinner';

const createSpinner = () => {
    return mount(PageSpinner, {});
};

function waitForMs(delay) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve();
        }, delay);
    });
}

describe('PageSpinner', () => {
    it("is displayed properly", async () => {
        let spinnerWrapper = createSpinner();

        expect(spinnerWrapper.vm.isActive).toBeFalsy();
        expect(spinnerWrapper.html()).not.toContain('loader');

        spinnerWrapper.vm.start(300);
        await waitForMs(1000);
        expect(spinnerWrapper.vm.isActive).toBeTruthy();
        expect(spinnerWrapper.html()).toContain('loader');

        spinnerWrapper.vm.stop(450);
        await waitForMs(1000);
        expect(spinnerWrapper.vm.isActive).toBeFalsy();
        expect(spinnerWrapper.html()).not.toContain('loader');

        spinnerWrapper.vm.start(300);
        spinnerWrapper.vm.stop(450);
        spinnerWrapper.vm.stop(450);
        await waitForMs(1500);
        expect(spinnerWrapper.vm.isActive).toBeFalsy();
        expect(spinnerWrapper.html()).not.toContain('loader');
    });
});