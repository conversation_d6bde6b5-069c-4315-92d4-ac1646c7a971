import {BundleInfoData} from 'common/generated-types/types';
import VueI18n from 'vue-i18n';

let bundleInfoService = {
    getBundleLabel(bundleInfo: BundleInfoData, i18n: VueI18n): string {
        return i18n.t(
            'shop.bundleInfo.description',
            {
                bundleName: bundleInfo.name,
                bundleSize: i18n.tc('shop.bundleInfo.size', bundleInfo.size)
            }
        ).toString();
    },
};

export {bundleInfoService}
