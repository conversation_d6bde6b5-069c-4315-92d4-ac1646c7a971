import {paymentMethodsService} from 'common/services';
import {PaymentMethodType} from "../../generated-types/types";

describe('PaymentMethodsService', () => {

    beforeEach(() => {
        window.frontendData = {
            coreData: {
                activeCountries: [
                    {
                        isocode: 'AT',
                        name: 'Austria',
                        canBuy: true,
                        blockedCountriesCommercial: [],
                        supportedPaymentMethodTypes: [
                            'CREDIT_CARD',
                            'SEPA_DIRECTDEBIT',
                            'SEPA_CREDIT'
                        ]
                    }
                ]
            }
        }
    });

    describe('countrySupportsPaymentMethod', () => {

        it('given active country with supported method type when countrySupportsPaymentMethod then return true', () => {
            expect(paymentMethodsService.countrySupportsPaymentMethod('AT', PaymentMethodType.SEPA_DIRECTDEBIT)).toBeTruthy();
        });

        it('given non active country when countrySupportsPaymentMethod then return false', () => {
            expect(paymentMethodsService.countrySupportsPaymentMethod('BE', PaymentMethodType.SEPA_DIRECTDEBIT)).toBeFalsy();
        });

        it('given active country with unsupported method type when countrySupportsPaymentMethod then return false', () => {
            expect(paymentMethodsService.countrySupportsPaymentMethod('AT', PaymentMethodType.ACH_INTERNATIONAL)).toBeFalsy();
        });
    });
});
