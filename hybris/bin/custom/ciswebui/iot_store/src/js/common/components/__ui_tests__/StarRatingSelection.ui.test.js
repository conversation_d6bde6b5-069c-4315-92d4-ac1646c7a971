describe('StarRatingSelection', () => {
    it('is displayed correctly', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/common-components-__ui_tests__-StarRatingSelection');
        expect(await page.title()).toBe('UI test - common-components-__ui_tests__-StarRatingSelection');

        await page.waitFor(300);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.mouse.click(12, 55);
        await page.waitFor(300);
        expect(await page.screenshot()).toMatchImageSnapshot("halfSelection");

        await page.mouse.click(20, 55);
        await page.waitFor(300);
        expect(await page.screenshot()).toMatchImageSnapshot("fullSelection");
    });
});