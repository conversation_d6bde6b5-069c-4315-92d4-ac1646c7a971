import {userPermissionService} from 'common/services';
import {BaseStores} from 'common/constants';
import Cookies from 'js-cookie';

jest.mock('common/util');

describe('User Permission Service', () => {

    beforeEach(() => {
        window.frontendData = {
            coreData: {
                userName: null,
                currentCompany: {
                    managedAccount: false
                },
                moduleConfig: {}
            }
        };
    });

    describe('isUserOfManagedAccount', () => {

        it('given anonymous user when isUserOfManagedAccount then return false', () => {
            window.frontendData.coreData.userName = null;

            expect(userPermissionService.isUserOfManagedAccount()).toBeFalsy();
        });

        it('given authenticated user and non managed company when isUserOfManagedAccount then return false', () => {
            window.frontendData.coreData.userName = 'auth';
            window.frontendData.coreData.currentCompany.managedAccount = false;

            expect(userPermissionService.isUserOfManagedAccount()).toBeFalsy();
        });

        it('given authenticated user and managed company when isUserOfManagedAccount then return true', () => {
            window.frontendData.coreData.userName = 'auth';
            window.frontendData.coreData.currentCompany.managedAccount = true;

            expect(userPermissionService.isUserOfManagedAccount()).toBeTruthy();
        });
    });

    describe('hasAccessToCart', () => {

        it('given anonymous user when hasAccessToCart then return false', () => {
            window.frontendData.coreData.userName = null;

            expect(userPermissionService.hasAccessToCart()).toBeFalsy();
        });

        it('given authenticated user and non managed company when hasAccessToCart then return true', () => {
            window.frontendData.coreData.userName = 'auth';
            window.frontendData.coreData.currentCompany.managedAccount = false;

            expect(userPermissionService.hasAccessToCart()).toBeTruthy();
        });

        it('given authenticated user and managed company when hasAccessToCart then return false', () => {
            window.frontendData.coreData.userName = 'auth';
            window.frontendData.coreData.currentCompany.managedAccount = true;

            expect(userPermissionService.hasAccessToCart()).toBeFalsy();
        });

        it.each`
            baseStore           | languageSwitcher | userCountry | companyCountry | expected
            ${BaseStores.AZENA} | ${false}         | ${'PT'}     | ${'AT'}        | ${true}
            ${BaseStores.AZENA} | ${true}          | ${'PT'}     | ${'AT'}        | ${true}
            ${BaseStores.AA}    | ${false}         | ${'PT'}     | ${'AT'}        | ${true}
            ${BaseStores.AA}    | ${true}          | ${'AT'}     | ${'AT'}        | ${true}
            ${BaseStores.AA}    | ${true}          | ${'PT'}     | ${'AT'}        | ${false}
            ${BaseStores.AA}    | ${true}          | ${''}       | ${'AT'}        | ${false}
        `('given authenticated user in \'$userCountry\' and company in \'$companyCountry\' when hasAccessToCart then return \'$expected\'', async ({baseStore, languageSwitcher, userCountry, companyCountry, expected}) => {
            window.frontendData.coreData.basestore = baseStore;
            window.frontendData.coreData.moduleConfig.LANGUAGE_SWITCHER = languageSwitcher;
            window.frontendData.coreData.userName = 'auth';
            window.frontendData.coreData.currentCompany.companyCountry = companyCountry;
            Cookies.set('st_selected_country', userCountry);

            expect(userPermissionService.hasAccessToCart()).toBe(expected);
        });
    });
});
