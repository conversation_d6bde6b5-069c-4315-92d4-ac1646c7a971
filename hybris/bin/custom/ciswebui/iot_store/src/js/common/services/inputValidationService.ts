const REGEX_QUANTITY = /^$|^[0-9]{0,4}$/;
const REGEX_DEV_PRICE = /^[0-9]{0,10}$/;
const REGEX_DEV_PRICE_LENIENT = /^[0-9]{0,10}([,.][0-9]*)?$/;

export let inputValidationService = {
    isValidQuantity(input: string) {
        return REGEX_QUANTITY.test(input) && (input === '' || Number(input) > 0);
    },
    isPermissibleQuantity(input: string, maxAllowed: number) {
        if( maxAllowed > 0) {
            return this.isValidQuantity(input) && Number(input) <= maxAllowed;
        }
        return this.isValidQuantity(input);
    },
    isQuantityOrZero(number: string) {
        return REGEX_QUANTITY.test(number);
    },
    isValidDeveloperPrice(number: string) {
        return REGEX_DEV_PRICE.test(number) && Number(number) >= 0;
    },
    isPartiallyValidDeveloperPrice(number: string) {
        return REGEX_DEV_PRICE_LENIENT.test(number) && Number(number.replace(',', '.')) >= 0;
    },
    isNumber(input: string) {
        return !isNaN(parseInt(input));
    }
};