import { navigationService } from '../navigationService'
import { NavigationItemGroup, NavigationItemType } from '../../generated-types/types'

describe('navigationService', () => {

  beforeEach(() => {
    window.frontendData = {
      coreData: {
        navigationItems: [],
      }
    }
  })

  describe('byId', () => {
    it('finds navigation item by id', () => {
      const itemId = 'itemId'
      window.frontendData.coreData.navigationItems.push({ id: itemId })

      const result = navigationService.byId(itemId)

      expect(result).not.toBeNull()
      expect(result.id).toBe(itemId)
    })

    it('returns null when not found', () => {
      window.frontendData.coreData.navigationItems.push({ id: 'another_id' })

      const result = navigationService.byId('id')

      expect(result).toBeNull()
    })
  })

  describe('byGroupAndType', () => {
    it('finds navigation item by group and type', () => {
      const itemToFind = { group: NavigationItemGroup.HELP, type: NavigationItemType.STORE }
      window.frontendData.coreData.navigationItems.push(
        itemToFind,
        { group: NavigationItemGroup.FOOTER, type: NavigationItemType.GLOBAL },
      )

      const result = navigationService.byGroupAndType(itemToFind.group, itemToFind.type)

      expect(result).not.toBeNull()
      expect(result).toContain(itemToFind)
    })

    it('returns empty when not found', () => {
      const result = navigationService.byGroupAndType(NavigationItemGroup.HELP, NavigationItemType.STORE)

      expect(result).toHaveLength(0)
    })
  })

  describe('helpItems', () => {
    it('return items of group HELP and type STORE', () => {
      const itemToFind = { group: NavigationItemGroup.HELP, type: NavigationItemType.STORE }
      window.frontendData.coreData.navigationItems.push(
        itemToFind,
        { group: NavigationItemGroup.HEADER, type: NavigationItemType.STORE },
        { group: NavigationItemGroup.HELP, type: NavigationItemType.GLOBAL },
      )

      const result = navigationService.helpItems()

      expect(result).not.toBeNull()
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            group: NavigationItemGroup.HELP,
            type: NavigationItemType.STORE
          })
        ])
      )
    })
  })

  describe('globalHelpContactItem', () => {
    it('returns globalHelpContactEmail when found', () => {
      window.frontendData.coreData.navigationItems.push({ id: 'globalHelpContactEmail' }, { id: 'globalHelpContact' })

      const result = navigationService.globalHelpContactItem()

      expect(result).not.toBeNull()
      expect(result.id).toBe('globalHelpContactEmail')
    })

    it('returns globalHelpContact when globalHelpContactEmail not found', () => {
      window.frontendData.coreData.navigationItems.push({ id: 'globalHelpContact' })

      const result = navigationService.globalHelpContactItem()

      expect(result).not.toBeNull()
      expect(result.id).toBe('globalHelpContact')
    })
  })

  describe('translate', () => {
    it('returns translation if it exists', () => {
      console.warn = jest.fn()

      let mockI18n = {
        te: function (key) {
          return true;
        },

        t: function (key) {
          return 'translated value'
        }
      }

      const result = navigationService.translate(mockI18n, {itemCode: 'lala', text: 'backend text'})

      expect(result).not.toBeNull()
      expect(result).toBe('translated value')
      expect(console.warn).not.toHaveBeenCalled()
    })

    it('returns backend text if translation does not exist', () => {
      console.warn = jest.fn()

      let mockI18n = {
        te: function (key) {
          return false;
        },

        t: function (key) {
          return 'translated value'
        }
      }

      const result = navigationService.translate(mockI18n, {itemCode: 'lala', text: 'backend text'})

      expect(result).not.toBeNull()
      expect(result).toBe('backend text')
      expect(console.warn).toHaveBeenCalled()
    })
  })
})
