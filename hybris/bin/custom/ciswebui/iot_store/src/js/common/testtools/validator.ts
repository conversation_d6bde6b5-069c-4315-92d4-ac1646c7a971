const Ajv = require('ajv');
const ajv = new Ajv({sourceCode: true});

let isValid = function (data: object, typeName: string) {
    const validate = require('common/generated-types/' + typeName);
    let valid = validate(data);
    if (!valid) {
        console.log('Validation errors:');
        console.log(ajv.errorsText(validate.errors));
    }
    return !!valid;
};

let validateData = function (data: object, typeName: string) {
    const validate = require('common/generated-types/' + typeName);
    let valid = validate(data);
    if (!valid) {
        console.log('Validation errors:');
        console.log(ajv.errorsText(validate.errors));
        console.log(JSON.stringify(validate.errors));

        throw new Error(`Validation errors for type '${typeName}': ${ajv.errorsText(validate.errors)}`);
    }
    return data;
};

export {validateData, isValid}