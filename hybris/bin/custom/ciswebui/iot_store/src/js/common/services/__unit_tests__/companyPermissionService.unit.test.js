import {companyPermissionService} from 'common/services';

describe('CompanyPermissionService', () => {
    beforeEach(() => {
        window.frontendData = {
            coreData: {
                basestore: 'aastore',
                currentCompany: {
                    name: 'austria buyer 1',
                    companyCountry: 'AT'
                },
                activeCountries: [
                    {
                        isocode: 'AT',
                        name: 'Austria',
                        supportedPaymentMethodTypes: [
                            'CREDIT_CARD',
                            'SEPA_DIRECTDEBIT',
                            'SEPA_CREDIT'
                        ]
                    }
                ]
            }
        }
    });

    describe('allowCurrentCompanyCreationOfSepaDDPaymentInfo', () => {

        it('test allow creation of SEPA DD payment info', () => {
            expect(companyPermissionService.allowCurrentCompanyCreationOfSepaDDPaymentInfo()).toBeTruthy();
        });

        it('given company country does not support payment info then payment info creation is not allowed', () => {
            window.frontendData.coreData.currentCompany.companyCountry = 'BE';
            expect(companyPermissionService.allowCurrentCompanyCreationOfSepaDDPaymentInfo()).toBeFalsy();
        });

        it('given base store not AA then payment info creation is not allowed', () => {
            window.frontendData.coreData.basestore = 'iotstore';
            expect(companyPermissionService.allowCurrentCompanyCreationOfSepaDDPaymentInfo()).toBeFalsy();
        });
    });
})
;
