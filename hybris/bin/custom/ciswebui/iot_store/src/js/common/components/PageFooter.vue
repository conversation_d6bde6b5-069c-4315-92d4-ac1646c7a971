<template>
  <CDFooter class="mt-16 mt-md-0" :links="entries" :copyright="$t('navigation.copyright')"></CDFooter>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { NavigationItemData, NavigationItemGroup } from 'common/generated-types/types';

@Component
export default class PageFooter extends Vue {
  @Prop() footerData!: NavigationItemData[];

  entries: NavigationItemData[] = [];

  created(): void {
    this.entries = this.footerData
        .filter(item => item.group === NavigationItemGroup.FOOTER)
        .sort((a, b) => a.index - b.index);
  }
}
</script>
