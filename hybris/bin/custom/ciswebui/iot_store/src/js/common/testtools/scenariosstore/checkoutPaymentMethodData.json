{"paymentAddress": {"id": "8796125888535", "title": null, "titleCode": null, "firstName": "<PERSON><PERSON>", "lastName": "User", "companyName": null, "line1": "Sample Street", "line2": "123", "town": "Sample Town", "region": null, "district": null, "postalCode": "12345", "phone": "+1234567890", "cellphone": null, "email": "<EMAIL>", "country": {"isocode": "DE", "name": "Germany", "currencyIsoCode": null, "canBuy": false, "sepaEnabled": false}, "shippingAddress": false, "billingAddress": true, "defaultAddress": false, "visibleInAddressBook": true, "formattedAddress": "Sample Street, 123, 12345, Sample Town, Germany"}, "totalPrice": {"symbol": "EUR", "value": "356.00"}, "totalTax": {"symbol": "EUR", "value": "67.64"}, "totalPriceWithTax": {"symbol": "EUR", "value": "423.64"}, "entries": [{"productName": "SuitCaseStudies 3", "productCode": "A_00000313_full", "productUrl": "/p/A_00000313", "companyName": "ABC Company", "versionName": "1.0.0", "licenseName": "Purchase", "licenseType": "FULL", "logoUrl": "/sample-data/app/app-icon-medium-1.jpeg", "itemPrice": {"symbol": "EUR", "value": "89.00"}, "totalPrice": {"symbol": "EUR", "value": "356.00"}, "quantity": 4, "entryNumber": 0}], "paymentMethod": "CREDIT_CARD", "invoicePaymentInfos": [{"id": "*************", "paymentMethod": "SEPA_CREDIT", "saved": true, "reusable": false, "defaultPaymentInfo": true, "paymentProvider": "BOSCH_TRANSFER", "enabled": true, "disableReason": null, "accountHolder": "Security & Safety Things GmbH", "iban": "**********************", "bankName": "First Ferengi Interplanetary", "bic": "BYLADEM1001"}], "checkoutInfos": [{"paymentInfos": [{"id": "*************", "paymentMethod": "CREDIT_CARD", "saved": true, "defaultPaymentInfo": false, "accountHolderName": "CHRIS PAUL", "cardType": "visa", "cardTypeData": {"code": "visa", "name": "Visa"}, "cardNumber": "***4321", "startMonth": null, "startYear": null, "expiryMonth": "11", "expiryYear": "2025", "issueNumber": null, "subscriptionId": null}], "paymentProvider": "DPG", "paymentMethod": "CREDIT_CARD", "userActionParameters": {"dpgjsUrl": "http://127.0.0.1:8091/dpgjs/js", "sessionConfig": "some client configuration", "paymentId": "payment_WKU6o28LYfQYCp5iz5UgKx", "sessionId": "1/DJ;9v{<f7kZQt*2G;8r*"}, "userCreatable": true, "savableForReuse": true}, {"paymentInfos": [{"id": "*************", "paymentMethod": "SEPA_CREDIT", "saved": true, "reusable": false, "defaultPaymentInfo": false, "paymentProvider": "DPG", "enabled": true, "disableReason": "", "accountHolder": "Security & Safety Things GmbH", "iban": "**********************", "bankName": "Deutsche Bank", "bic": "DEUTDEFFVAC"}], "paymentProvider": "DPG", "paymentMethod": "SEPA_CREDIT", "userActionParameters": {}, "userCreatable": false, "savableForReuse": false}]}