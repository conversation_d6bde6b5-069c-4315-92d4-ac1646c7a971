import {i18nService} from 'common/services';
import Cookies from 'js-cookie';
import {customerStoreSelectionResource} from 'shop/resources';

jest.mock('common/util');
jest.mock('axios');
jest.mock('shop/resources/customerStoreSelectionResource');

describe('I18n Service', () => {

    afterEach(() => {
        Cookies.remove('st_selected_lang');
        Cookies.remove('st_selected_country');
    });

    describe('getUserLanguage', () => {
        it('given language cookie when getUserLanguage then return cookie value', () => {
            const cookieValue = 'fr';
            Cookies.set('st_selected_lang', cookieValue);

            expect(i18nService.getUserLanguage()).toBe(cookieValue);
        });

        it('given no language cookie found when getUserLanguage then return empty string', () => {
            Cookies.remove('st_selected_lang');

            expect(i18nService.getUserLanguage()).toBe('');
        });
    });

    describe('setUserLanguage', () => {
        it('when setUserLanguage then set language cookie', () => {
            const cookieValue = 'fr';
            i18nService.setUserLanguage(cookieValue);

            expect(Cookies.get('st_selected_lang')).toBe(cookieValue);
        });

        it('given empty country when setUserLanguage then skip', () => {
            const cookieValue = 'fr';
            Cookies.set('st_selected_lang', cookieValue);

            i18nService.setUserLanguage('');

            expect(Cookies.get('st_selected_lang')).toBe(cookieValue);
        });
    });

    describe('getUserCountry', () => {
        it('given country cookie when getUserCountry then return cookie value', () => {
            const cookieValue = 'FR';
            Cookies.set('st_selected_country', cookieValue);

            expect(i18nService.getUserCountry()).toBe(cookieValue);
        });

        it('given no country cookie found when getUserCountry then return empty string', () => {
            Cookies.remove('st_selected_country');

            expect(i18nService.getUserCountry()).toBe('');
        });
    });

    describe('getCompanyCountry', () => {
        beforeEach(() => {
            window.frontendData = {
                coreData: {
                    currentCompany: {
                        companyCountry: 'AT'
                    }
                }
            };
        });

        it.each`
            country      | expected
            ${'PT'}      | ${'PT'}
            ${''}        | ${''}
            ${null}      | ${''}
            ${undefined} | ${''}
        `('given company country is \'$country\' when getCompanyCountry then return \'$expected\'', async ({country, expected}) => {
            window.frontendData.coreData.currentCompany.companyCountry = country;

            expect(i18nService.getCompanyCountry()).toBe(expected);
        });

        it('given no company when getCompanyCountry then return empty string', () => {
            delete window.frontendData.coreData.currentCompany;

            expect(i18nService.getCompanyCountry()).toBe('');
        });
    });

    describe('setUserCountry', () => {
        it('when setUserCountry then set country cookie', () => {
            const cookieValue = 'FR';
            i18nService.setUserCountry(cookieValue);

            expect(Cookies.get('st_selected_country')).toBe(cookieValue);
        });

        it('given empty country when setUserCountry then skip', () => {
            const cookieValue = 'FR';
            Cookies.set('st_selected_country', cookieValue);

            i18nService.setUserCountry('');

            expect(Cookies.get('st_selected_country')).toBe(cookieValue);
        });
    });

    describe('isUserInCompanyCountry', () => {
        beforeEach(() => {
            window.frontendData = {
                coreData: {
                    currentCompany: {
                        companyCountry: 'AT'
                    }
                }
            };
        });

        it.each`
            userCountry | companyCountry | expected
            ${'PT'}     | ${'PT'}        | ${true}
            ${'FR'}     | ${'PT'}        | ${false}
            ${'PT'}     | ${''}          | ${false}
            ${''}       | ${'PT'}        | ${false}
            ${''}       | ${''}          | ${false}
        `('given user country is \'$userCountry\' and company country is \'$companyCountry\' when isUserInCompanyCountry then return \'$expected\'', async ({userCountry, companyCountry, expected}) => {
            Cookies.set('st_selected_country', userCountry);
            window.frontendData.coreData.currentCompany.companyCountry = companyCountry;

            expect(i18nService.isUserInCompanyCountry()).toBe(expected);
        });
    });

    describe('goToSelectedMarketPlace', () => {
        it('should make a resource call successfully with HTTP status 200', async () => {
            const selectedCountry = 'AT';
            const selectedLanguage = 'de';

            const expectedResponse = {};

            customerStoreSelectionResource.goToSelectedMarketPlace.mockResolvedValueOnce({ status: 200, data: expectedResponse });

            // Import and call the function

            const response = await i18nService.goToSelectedMarketPlace(selectedCountry, selectedLanguage);

            expect(customerStoreSelectionResource.goToSelectedMarketPlace).toHaveBeenCalledWith(selectedCountry,selectedLanguage);
            expect(response.status).toBe(200);
            expect(response.data).toEqual(expectedResponse);
        });

        it('should handle a bad request with HTTP status 400', async () => {
            const selectedCountry = 'IN';
            const selectedLanguage = 'te';

            const expectedError = {
                message: 'api.error.countryStore.invalidCountryOrLanguage'
            };

            customerStoreSelectionResource.goToSelectedMarketPlace.mockRejectedValueOnce({ response: { status: 400, data: expectedError } });

            try {
                await i18nService.goToSelectedMarketPlace(selectedCountry, selectedLanguage);
                // Expecting the promise to reject, so the test fails if it reaches this point
                expect(true).toBe(false);
            } catch (error) {
                expect(customerStoreSelectionResource.goToSelectedMarketPlace).toHaveBeenCalledWith(selectedCountry,selectedLanguage);
                expect(error.response.status).toBe(400);
                expect(error.response.data).toEqual(expectedError);
            }
        });
    });

    describe('goToCompanyDefaultMarketPlace', () => {
        it('should make a resource call successfully with HTTP status 200', async () => {
            customerStoreSelectionResource.goToCompanyDefaultMarketPlace.mockResolvedValueOnce({ status: 200 });

            const response = await i18nService.goToCompanyDefaultMarketPlace();

            expect(response.status).toBe(200);
        });

        it('should handle a bad request with HTTP status 400', async () => {
            const expectedError = {
                message: 'api.error.countryStore.failedToSwitchToDefault'
            };

            customerStoreSelectionResource.goToCompanyDefaultMarketPlace.mockRejectedValueOnce({ response:
                    { status: 400, data: expectedError }
            });

            try {
                await i18nService.goToCompanyDefaultMarketPlace();
                expect(true).toBe(false);
            } catch (error) {
                expect(customerStoreSelectionResource.goToCompanyDefaultMarketPlace).toHaveBeenCalled();
                expect(error.response.status).toBe(400);
                expect(error.response.data).toEqual(expectedError);
            }
        });
    });
});
