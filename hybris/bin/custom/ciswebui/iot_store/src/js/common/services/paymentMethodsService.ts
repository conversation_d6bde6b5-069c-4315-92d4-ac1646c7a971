import {shopCoreDataProvider} from 'common/provider';
import {PaymentMethodType} from 'common/generated-types/types';

const paymentMethodsService = {
    countrySupportsPaymentMethod(countryCode: string, paymentMethodType: PaymentMethodType): boolean {
        const countryData = shopCoreDataProvider.data.activeCountries
            .find(country => country.isocode === countryCode);
        return !!countryData && countryData.supportedPaymentMethodTypes.includes(paymentMethodType);
    }
}

export {paymentMethodsService};
