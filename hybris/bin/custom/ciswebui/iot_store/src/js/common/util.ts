import axios from 'axios';
import ColorThief from "color-thief-browser";
import anime from 'animejs';
import def from 'common/defaults';
import {PaymentProvider} from "common/generated-types/types";
import {Vue} from "vue-property-decorator";

declare global {
    interface Window {
        frontendData: any;
        render_dummy: any;
    }
}

axios.defaults.headers.common = {
    'X-Requested-With': 'XMLHttpRequest',
    'CSRFToken': window.frontendData.coreData.csrfToken,
    'Accept-Language': window.frontendData.coreData.currentLanguage === 'de' ? 'de-DE,de;q=0.9' : 'en-US,en;q=0.9'
};

axios.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 401) {
            return window.location.assign('/logout');
        }
        return Promise.reject(error);
    }
);

let util = {
    axios: axios,
    // Based on: https://stackoverflow.com/questions/123999/how-to-tell-if-a-dom-element-is-visible-in-the-current-viewport/7557433#7557433
    isElementInViewport(element: HTMLElement): boolean {
        let rect = element.getBoundingClientRect();

        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },
    isElementInOrAboveViewport(el: HTMLElement) {
        let rect = el.getBoundingClientRect();

        return (
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },
    lockScrolling(): number {
        let scrollY = window.scrollY;
        window.document.body.style.height = '100%';
        window.document.body.style.top = `-${scrollY}px`;
        window.document.body.style.overflow = 'hidden';
        return scrollY;
    },
    unlockScrolling(scrollY: number) {
        window.document.body.style.height = 'auto';
        window.document.body.style.top = '0';
        window.document.body.style.overflow = 'auto';
        window.render_dummy = window.document.body.offsetHeight;
        const el = (document.getElementById('scrollable') ||
            document.getElementsByClassName('content-wrapper')[0]);
        if (el) {
            el.scrollTo(0, scrollY);
        }
    },
    loadLogoImage(logos: { logoUrl: string, logoElement: HTMLElement }[]) {
        let computeBorderColorForElement = (image: HTMLImageElement, logoElement: HTMLElement) => {
            let borderComputed = false;
            return function () {
                if (borderComputed) {
                    return;
                }
                borderComputed = true;

                let colorThief = new ColorThief();
                let color = colorThief.getColor(image, 40);

                logoElement.style.borderColor = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
                anime({targets: logoElement, opacity: 1, duration: 150, easing: def.animation.easeout});
            };
        };

        for (let logo of logos) {
            let logoElement = logo.logoElement;

            logoElement.style.opacity = '0';

            let image = new Image();
            image.crossOrigin = 'Anonymous';
            image.onload = computeBorderColorForElement(image, logoElement);
            image.onerror = () => {
                logoElement.style.opacity = '1';
            };
            image.src = logo.logoUrl;

            if (image.naturalWidth !== 0 && image.complete) {
                computeBorderColorForElement(image, logoElement)();
            }
        }
    },
    scriptLoader: async (url: string, type: PaymentProvider) => {
        if (type === PaymentProvider.DPG) {
            if (!(window as any).integration) {
                await Vue.loadScript(url)
            }
        }
    },
    isDACHRegion(countryISOCode: string) {
        if (!countryISOCode) {
            return false;
        }
        const DACH_ISO_CODES = ['DE', 'AT', 'CH']; // Germany, Austria, Switzerland
        return DACH_ISO_CODES.includes(countryISOCode.toUpperCase());
    }
};

export default util;