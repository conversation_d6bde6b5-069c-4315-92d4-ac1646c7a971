{"ccPaymentInfos": [{"id": "123", "accountHolderName": "Test User1", "cardTypeData": {"name": "Visa"}, "cardNumber": "***3232", "cardType": "visa", "expiryMonth": "4", "expiryYear": "2022", "saved": true, "defaultPaymentInfo": true, "billingAddress": {"companyName": "Sample Company", "line1": "Sample Street", "town": "Sample Town", "region": null, "postalCode": "12345", "country": {"isocode": "DE", "name": "Germany"}, "shippingAddress": false}}, {"id": "456", "accountHolderName": "Test User2", "cardTypeData": {"name": "MasterCard"}, "cardNumber": "***3232", "cardType": "master", "expiryMonth": "4", "expiryYear": "2022", "saved": true, "defaultPaymentInfo": false, "billingAddress": {"companyName": "Sample Company", "line1": "Sample Street", "town": "Sample Town", "region": null, "postalCode": "12345", "country": {"isocode": "DE", "name": "Germany"}, "shippingAddress": false}}], "invoicePaymentInfos": [{"id": "789", "paymentMethod": "INVOICE", "saved": true, "defaultPaymentInfo": false, "enabled": true}, {"id": "*************", "paymentMethod": "SEPA_CREDIT", "saved": true, "reusable": false, "defaultPaymentInfo": false, "paymentProvider": "BOSCH_TRANSFER", "enabled": true, "disableReason": null, "accountHolder": "Security & Safety Things GmbH", "iban": "**********************", "bankName": "First Ferengi Interplanetary", "bic": "BYLADEM1001"}, {"id": "*************", "paymentMethod": "SEPA_CREDIT", "saved": true, "reusable": false, "defaultPaymentInfo": false, "paymentProvider": "BOSCH_TRANSFER", "enabled": true, "disableReason": null, "accountHolder": "Security & Safety Things GmbH", "iban": "**********************", "bankName": "First Ferengi Interplanetary", "bic": "BYLADEM1001"}], "directDebitPaymentInfos": []}