import {NavigationItemData, NavigationItemGroup, NavigationItemType} from 'common/generated-types/types';
import {ShopRoute} from "common/constants";
import {productDataService} from "common/services/productDataService";
import {sortBy} from "lodash";
import VueI18n from "vue-i18n";

let navigationService = {

    byItemCode: function (itemCode: string): NavigationItemData {
        return window.frontendData.coreData.navigationItems.filter((item: NavigationItemData) => item.itemCode === itemCode)[0];
    },

    byGroup: function (group: NavigationItemGroup): Array<NavigationItemData> {
        let query = window.frontendData.coreData.navigationItems.filter((item: NavigationItemData) => item.group === group);
        return query.length > 1 ? query : query[0];
    },

    byGroupSorted: function (group: NavigationItemGroup): Array<NavigationItemData> {
        const groupItems = this.byGroup(group);
        return sortBy(groupItems, 'index');
    },

    byId(id: string): NavigationItemData {
        return window.frontendData.coreData.navigationItems
            .find((item: NavigationItemData) => item.id === id) || null;
    },

    byGroupAndType(group: NavigationItemGroup, type: NavigationItemType): Array<NavigationItemData> {
        return window.frontendData.coreData.navigationItems
            .filter((item: NavigationItemData) => item.type === type && item.group === group);
    },

    goTo: function (url: string) {
        window.location.href = url;
    },

    toolsActive: function (url: string) {
        if ((url === (ShopRoute.TOOLS)) || (url === (ShopRoute.HOME + '/'))) {
            return url === (ShopRoute.TOOLS);
        }
        return productDataService.isToolApp(window.frontendData.pageData);
    },

    appsActive: function (url: string) {
        if ((url === (ShopRoute.TOOLS)) || (url === (ShopRoute.HOME + '/'))) {
            return url === (ShopRoute.HOME + '/');
        }
        return productDataService.isThirdPartyApp(window.frontendData.pageData);
    },

    helpItems: function () {
        const commonHelpItems = this.byGroupAndType(NavigationItemGroup.HELP, NavigationItemType.STORE);
        return sortBy(commonHelpItems, 'index');
    },

    globalHelpContactItem: function () {
        return navigationService.byId('globalHelpContactEmail')
            || navigationService.byId('globalHelpContact');
    },

    translate: function (i18n: VueI18n, navigationItemData: NavigationItemData): string {
        const navTranslationKey : string = `navigation.items.${navigationItemData.itemCode}`
        if (i18n.te(navTranslationKey)) {
            return i18n.t(navTranslationKey).toString()
        }
        console.warn(`Missing translation key '${navTranslationKey}' for navigationItem '${navigationItemData.itemCode}'. Fallback to backend-provided text.`)
        return navigationItemData.text;
    }
};

export {navigationService}
