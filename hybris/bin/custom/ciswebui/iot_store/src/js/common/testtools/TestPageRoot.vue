<!--

This component is to be used as root for test pages only. I.e. *.testpage.js and *.aa.testpage.js.

It's purpose is to have the CD System specific SCSS imports without scoping. Those imports are automatically added
during SCSS compilation (see our Webpack configurations), but usually for test pages the SCSS is component specific
and scoped. The SCSS for this root component is specifically unscoped which results in the CD System imports being
unscoped too.

-->
<template>
  <v-app>
    <slot></slot>
  </v-app>
</template>

<script lang="ts">
//
</script>

<style lang="scss">
//
</style>
