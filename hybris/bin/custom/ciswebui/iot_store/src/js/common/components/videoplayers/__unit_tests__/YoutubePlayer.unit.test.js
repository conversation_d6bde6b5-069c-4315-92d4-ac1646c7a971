import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';
import YoutubePlayer from '../YoutubePlayer.vue';

describe('YoutubePlayer', () => {

    it('works with videoUrl prop', async () => {
        const wrapper = wrapperComponentFactory(YoutubePlayer, {
            props: {
                videoUrl: 'https://www.youtube.com/watch?v=e3hkn7gQ4uo'
            }
        });
        expect(wrapper.exists()).toBeTruthy();
    });

    it('works using video object', async () => {
        const wrapper = wrapperComponentFactory(YoutubePlayer, {
            props: {
                video: {
                    source: 'https://www.youtube.com/watch?v=e3hkn7gQ4uo',
                    type: 'YOUTUBE'
                }
            }
        });
        expect(wrapper.exists()).toBeTruthy();
    });

});