import {shallowMount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import {Pagination} from 'common/components';

const createWrapper = (options) => {
    return shallowMount(Pagination, {
        propsData: {
            pageUrl: 'someTestUrl',
            paginationData: getPaginationData(options)
        }
    });
};

const getPaginationData = (options) => {
    return {
        pageSize: 2,
        currentPage: options.currentPage,
        numberOfPages: options.numberOfPages,
        totalNumberOfResults: 10,
        needsTotal: true,
        hasNext: options.currentPage < options.numberOfPages -1,
        hasPrevious: options.currentPage > 0
    };
};

describe('Pagination numbers', () => {

    let validOptions = {
        currentPage: 2,
        numberOfPages: 5
    };
    const validPagination = createWrapper(validOptions);

    it('display correct active page', async () => {
        let activePage = validPagination.findAll('.active-page');
        expect(activePage).toHaveLength(1);
        expect(activePage.wrappers[0].text()).toBe('3');
    });

    it('are surrounded by arrows', async () => {
        console.log('validPagination');
        console.log(validPagination);
        expect(validPagination.findAll('[data-id="left-arrow-stub"]')).toHaveLength(1);
        expect(validPagination.findAll('[data-id="right-arrow-stub"]')).toHaveLength(1);
    });

    it('link to correct pages', async () => {
        let links = validPagination.findAll('.link-number > a');
        expect(links).toHaveLength(5);
        links.wrappers.forEach((link) => {
            if (!link.element.className) {
                let href = link.element.getAttribute('href');
                expect(href.endsWith(link.element.textContent - 1)).toBeTruthy();
            }
        });
    });
});

describe('Pagination arrows', () => {
    let invalidOptions = {
        currentPage: 7,
        numberOfPages: 5
    };
    const invalidPagination = createWrapper(invalidOptions);
    it('disappear if page is out of range', async () => {
        expect(invalidPagination.findAll('.active-page')).toHaveLength(0);
        expect(invalidPagination.findAll('[data-id="left-arrow-stub"]')).toHaveLength(0);
        expect(invalidPagination.findAll('[data-id="right-arrow-stub"]')).toHaveLength(0);
    });

    let validOptions = {
        currentPage: 3,
        numberOfPages: 5
    };
    const validPagination = createWrapper(validOptions);
    it('contain correct links', async () => {
        let leftLink = validPagination.find('[data-id="left-arrow-stub"]').element;
        expect(leftLink.getAttribute('href')).toBe('someTestUrl?page=2');

        let rightLink = validPagination.find('[data-id="right-arrow-stub"]').element;
        expect(rightLink.getAttribute('href')).toBe('someTestUrl?page=4');
    });

    let edgeOptions = {
        currentPage: 0,
        numberOfPages: 5
    };
    const edgePagination = createWrapper(edgeOptions);
    it('only displayed in valid direction', async () => {
        let leftLink = edgePagination.findAll('[data-id="left-arrow-stub"]');
        expect(leftLink).toHaveLength(0);

        let rightLink = edgePagination.findAll('[data-id="right-arrow-stub"]');
        expect(rightLink).toHaveLength(1);
    });
});

describe('Pagination', () => {
    let zeroOptions = {
        currentPage: 0,
        numberOfPages: 1
    };
    const zeroPagination = createWrapper(zeroOptions);
    it('is not displayed for a single page', async () => {
        let pagination = zeroPagination.findAll('.pagination-display');
        expect(pagination).toHaveLength(0);
    });
});
