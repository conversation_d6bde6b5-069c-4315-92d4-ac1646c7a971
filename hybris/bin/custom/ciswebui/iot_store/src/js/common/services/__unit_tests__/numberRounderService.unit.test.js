import { numberRounderService} from '../numberRounderService';

describe("numberRounderService", () => {

    it('rounds number between 10 and 99 down to nearest 10', () => {
        expect(numberRounderService.purchases(11)).toBe(10);
        expect(numberRounderService.purchases(62)).toBe(60);
        expect(numberRounderService.purchases(99)).toBe(90);
    });

    it('rounds number between 100 and 1000 down to nearest 100', () => {
        expect(numberRounderService.purchases(125)).toBe(100);
        expect(numberRounderService.purchases(222)).toBe(200);
        expect(numberRounderService.purchases(222)).toBe(200);
        expect(numberRounderService.purchases(499)).toBe(400);
        expect(numberRounderService.purchases(501)).toBe(500);
        expect(numberRounderService.purchases(666)).toBe(600);
        expect(numberRounderService.purchases(999)).toBe(900);
    });

    it('rounds numbers bigger than 1000 down to closest increment of 5000', () => {
        expect(numberRounderService.purchases(1234)).toBe(1000);

        expect(numberRounderService.purchases(5001)).toBe(5000);
        expect(numberRounderService.purchases(9001)).toBe(9000);
        expect(numberRounderService.purchases(10000)).toBe(10000);
        expect(numberRounderService.purchases(24098)).toBe(20000);
        expect(numberRounderService.purchases(26132)).toBe(25000);
        expect(numberRounderService.purchases(12001)).toBe(10000);
        expect(numberRounderService.purchases(49678)).toBe(45000);
    });

    it('rounds 1,500,001 down to closest increment of 5000', () => {
        expect(numberRounderService.purchases(1500001)).toBe(1000000);
    })

});