{"create": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "delete": "Löschen", "remove": "Entfernen", "ok": "Ok", "or": "oder", "by": "<PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "back": "Zurück", "continue": "<PERSON><PERSON>", "payNow": "Zahlen Sie jetzt", "quantity": "<PERSON><PERSON><PERSON>", "total": "Gesamtsumme", "update": "Aktualisieren", "select": "Wählen Sie", "add": "Hinzufügen", "edit": "<PERSON><PERSON><PERSON>", "confirm": "Bestätigen", "send": "Senden", "backendError": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.", "default": "Standard", "preference": "Präferenz", "country": {"AE": "Vereinigte Arabische Emirate", "AR": "Argentinien", "AT": "Österreich", "AU": "Australien", "BE": "Belgien", "BG": "Bulgarien", "BR": "Brasilien", "CA": "Ka<PERSON><PERSON>", "CH": "Schweiz", "CL": "Chile", "CY": "<PERSON><PERSON><PERSON>", "CZ": "Tschechien", "DE": "Deutschland", "DK": "Dänemark", "EE": "Estland", "ES": "Spanien", "FI": "Finnland", "FR": "<PERSON><PERSON><PERSON>", "GB": "Vereinigtes Königreich", "GR": "Griechenland", "HR": "<PERSON><PERSON><PERSON>", "HU": "<PERSON><PERSON><PERSON>", "ID": "Indonesien", "IE": "Irland", "IL": "Israel", "IN": "Indien", "IS": "Island", "IT": "Italien", "JP": "Japan", "KR": "Republik Korea", "LT": "Li<PERSON>uen", "LU": "Luxemburg", "LV": "Lettland", "MT": "Malta", "MX": "Mexiko", "MY": "Malaysia", "NL": "Niederlande", "NO": "Norwegen", "NZ": "Neuseeland", "PL": "<PERSON><PERSON>", "PT": "Portugal", "RO": "Rumänien", "SA": "Saudi-Arabien", "SE": "Schweden", "SG": "Singapur", "SI": "Slowenien", "SK": "Slowakei", "TH": "Thailand", "TR": "Türkei", "TW": "Taiwan", "US": "Vereinigte Staaten", "VN": "Vietnam", "ZA": "Südafrika"}, "navigation": {"loggedInAs": "<PERSON><PERSON><PERSON><PERSON> als", "copyright": "Copyright © 2021 Bosch Digital Commerce GmbH. Alle Rechte vorbehalten", "devconTitle": "Developer Console", "storeTitle": "Application Store", "deviceManagementPortal": "Device Management Portal", "helpAndResources": "Hilfe & Ressourcen", "devconPageTitle": "Dev<PERSON>per Console – <PERSON>zena", "storePageTitle": "Application Store – Azena", "items": {"appProductCategory": "Apps", "deviceManagement": "Geräteverwaltung", "globalHelpContact": "Kontaktieren Sie Uns", "globalHelpContactEmail": "Kontaktieren Sie Uns", "globalHelpSupport": "Besuchen Sie Hilfe und Support", "globalImprint": "Impressum", "globalLegal": "Rechtliche Grundlagen", "globalMyCompany": "<PERSON><PERSON>", "globalMyProfile": "<PERSON><PERSON>", "globalSupport": "Unterstützung", "snstCameras": "Get your cameras", "snstHome": "Azena Home", "snstOurPlatform": "Get to know our platform", "storeHelpAndResources": "Help & Resources", "storeHelpAzenaHome": "Azena home", "storeHelpCompatibleCameras": "Kompatible Kameras", "storeHelpIntroToStore": "Einführung in den Application Store", "storeHelpPlatformOverview": "unsere Plattform", "storeHelpStoreDocs": "Application Store Hilfe", "storeInfrigment": "<PERSON>ers<PERSON>ß <PERSON>", "storeLogin": "<PERSON><PERSON><PERSON><PERSON>", "storeOrderHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storePaymentDetails": "Zahlungsdetails", "storePrivacyPolicy": "Datenschutz", "storeRegister": "Registrieren", "storeSignOut": "Ausloggen", "storeTermsAndConditions": "AGB", "toolProductCategory": "<PERSON><PERSON><PERSON>"}}, "shop": {"reviews": {"anonymous": "Anonym"}, "license": {"evaluation": {"name": "Testversion"}, "full": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "subscription": {"name": "Abonnement"}}, "products": {"header": "Willkommen im Application Store. Finde, teste und kaufe Applikationen für deine Bedürfnisse", "title": "Anwendungen", "searchProducts": "<PERSON><PERSON>", "searchResults": "Suchergebnisse für", "noSearchResults": "Es tut uns leid! Wir haben keine passenden Apps gefunden. <PERSON>sen <PERSON> uns wissen, wonach Si<PERSON> suchen. {url}", "contactUs": "Kontaktiere uns.", "loadMore": "<PERSON><PERSON> <PERSON>", "buyButton": "<PERSON><PERSON><PERSON>", "buyButtonWithPrice": "<PERSON><PERSON><PERSON> ab {currency} {price}", "price": "ab {currency} {price}", "sorting": {"prefix": "Sortiere nach", "created": "Neueste", "acquisitionCount": "Die meisten gekauft", "relevance": "Relevanteste"}, "facets": {"availability": {"country": "In Ihrem Land Verfügbar", "trial": "Probelizenz", "privateapps": "Ihre privaten Apps"}, "group": {"availability": {"name": "Verfügbarkeit"}, "usecases": {"name": "Anwendungsfälle"}, "industries": {"name": "Industrien"}, "licenseruntimes": {"name": "License type", "values": {"runtime_subs_unlimited": "Abonnement", "runtime_full_3y": "Einmalkauf (3 Jahre)", "runtime_full_unlimited": "<PERSON><PERSON><PERSON><PERSON>"}}}, "showMore": "<PERSON><PERSON> anzeigen", "filters": "Filter", "filteredBy": "<PERSON><PERSON><PERSON><PERSON> nach", "filteredSearchResults": "Gefilterte Suchergebnisse", "filteredSearchResultsFor": "Gefilterte Suchergebnisse für"}}, "gridItem": {"notAvailableLabel": "In Ihrem Land nicht verfügbar", "notAvailableLabelShort": "Nicht verfügbar", "readOnlyLabel": "Demnächst verfügbar", "privateAppLabel": "Privat app"}, "productDetails": {"productHeader": {"trialButton": "<PERSON><PERSON><PERSON><PERSON>", "tryFreeButton": "<PERSON><PERSON><PERSON> testen", "purchaseButton": "<PERSON><PERSON><PERSON>", "getFreeToolButton": "<PERSON><PERSON><PERSON>", "byCompany": "<PERSON>", "applicationStore": "Application-Store", "reviewsOverview": {"review": "Review | Reviews", "install": "Installation | Installationen", "purchases": "{installs}+ Einkäufe"}, "noLicense": "<PERSON><PERSON> sich diese App schon mal an. Sie wird bald verfügbar sein.", "maintenance": {"title": "Wir führen eine geplante Wartung durch.", "subtitle": "Die Kauffunktionalität von Anwendungen ist derzeit nicht verfügbar."}, "privateAppLabel": "Privat app", "integration": {"title": "Integrationsmöglichkeiten:", "detail": "Details anzeigen"}}, "gallerySection": {"title": "Vorschau"}, "packageDetailsSection": {"title": "<PERSON><PERSON> im Detail", "addons": "Verwandte Add-Ons", "video": "<PERSON> ansehen"}, "pricingSection": {"addToCart": "Zum Warenkorb hinzufügen", "enableTool": "Enable <PERSON><PERSON><PERSON>", "addToCartSuccess": "Erfolgreich zum Wahrenkorb hinzugefügt", "addToCartSuccessWithDiscount": "Erfolgreich zum Wahrenkorb hinzugefügt. {percent}% Mengenrabatt wurde angewendet", "addToCartGeneralError": "Es ist ein Fehler aufgetreten. Das Produkt konnte dem Warenkorb nicht hinzugefügt werden.", "pricing": "Pre<PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "permissionText": "Sie haben keine Berechtigung zum Kauf von Produkten.", "countryPermissionText": "<PERSON>se A<PERSON> ist in ihr Land nicht verfügbar", "integratorCountryBlockedText": "Die kommerziellen Lizenzen dieser App sind in Ihrem Land gesperrt", "subscriptionPermissionText": "Die Abonnements können zu diesem Zeitpunkt mit der Zahlungsmethode Banküberweisung bestellt werden. Für Details wenden Sie sich bitte an unseren Kundensupport.", "learnMore": "<PERSON><PERSON> er<PERSON>", "free": "<PERSON><PERSON><PERSON>", "pricetext": "ein<PERSON>", "perYear": "pro Jahr", "fullLicenseName": "<PERSON><PERSON>", "fullLicenseSummary": "Eine gekaufte App wird auf einer Kamera installiert und läuft nicht aus.", "evaluationLicenseName": "Erprobung", "evaluationLicenseSummary": "Eine Probeversion einer App wird auf einer Kamera installiert und läuft nach 30 Tagen aus.", "anonymousUserInfo": "{url}, um die Preise zu sehen und kostenlos zu testen.", "logIn": "Loggen Sie sich ein", "trialNowFullSoon": "<PERSON><PERSON><PERSON> Si<PERSON> sich unsere Testanwendungen an. Volle Kaufoption in Kürze.", "quantityInvalid": "Die maximale Bestellmenge einer einzelnen App beträgt {0}.", "license": {"evaluation": {"name": "Erprobung", "summary": "Testen Sie die App 30 Tage lang kostenlos", "detailsMap": {"1": "<PERSON><PERSON> Ka<PERSON> kann jeden Versuch einmal ausführen", "2": "<PERSON>ch bestätige, dass ich mit den Nutzungsbedingungen für diese App einverstanden bin", "3": "Erweiterte Testversion kann zu Verkaufsdemonstrationen und zu ähnlichen Zwecken angefordert werden"}}, "full": {"name": "<PERSON><PERSON>", "summary": "<PERSON>n <PERSON> sich die App mit einmaliger Zahlung", "detailsMap": {"1": "Einmalige Zahlung", "2": "Ein Kauf kann auf einer Kamera installiert werden"}}, "subscription": {"name": "Abonnement", "summary": "<PERSON>n Si<PERSON> sich die App auf Abonnementbasis. Die wiederkehrenden Kosten werden alle 365 Tage in Rechnung gestellt.", "detailsMap": {"1": "Der Leistungszeitraum beginnt am Tag der Bestellung", "2": "<PERSON><PERSON><PERSON>t sich automatisch nach 365 Tagen ", "3": "30 Tage im Voraus kündigen", "4": "Ein Abonnement kann auf einer Kamera installiert werden"}, "futurePriceText": "Der Abonnementpreis ändert sich aufgrund einer Preisänderung ab dem {startDate} alle 12 Monate nach dem ersten Jahr auf {futurePrice}."}, "tool": {"name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "summary": "<PERSON>n <PERSON> sich die App kostenlos", "detailsMap": {"1": "<PERSON><PERSON><PERSON> Si<PERSON> dieses Tool Ihrem Unternehmen zur kostenlosen Installation und Nutzung zur Verfügung.", "2": "<PERSON>ch bestätige, dass ich mit den Nutzungsbedingungen für dieses Tool einverstanden bin."}, "confirmationPopup": {"header": "Kosetenloses Azena tool jetzt zur Installation verfügbar", "contentLine1": "<PERSON>e können {toolName} jetzt kostenlos installieren und verwenden.", "installInstructions": "<PERSON>n Ihre Kameras bereits zu unserem hinzugefügt wurden, können Sie die Installation direkt von dort aus remote starten.", "deviceManagementPortal": "Geräteverwaltungsportal", "offlineInstallInstructions": "<PERSON><PERSON> Ihre Kameras nur mit einem lokalen Netzwerk verbunden sind, müssen Si<PERSON> unser {linkToDMT} herunter<PERSON>n, um Apps zu installieren.", "deviceManagementTool": "Geräteverwaltungstool", "learnMore": "{linkToGuide}.", "guide": "<PERSON><PERSON> er<PERSON>", "doneButton": "<PERSON><PERSON><PERSON>"}}}, "volumeDiscounts": {"table": {"headings": {"quantity": "<PERSON><PERSON><PERSON>", "discount": "<PERSON><PERSON><PERSON>", "pricePerApp": "Preis pro app"}}, "callout": "Sparen bis zu \"{num}\"% durch den <PERSON>uf in loser Schüttung"}}, "getOwnAppSection": {"title": "Holen Sie sich eine eigene App", "description": "Sie können Ihre eigene App mit permanenter(n) Lizenz(en) erhalten. Die Plattformgebühr wird an Ihre Rechnungsadresse gesendet.", "quantity": "<PERSON><PERSON>", "getAppsButtonText": "<PERSON><PERSON> erhalten", "infoText": "Die Rechnung für die Plattformgebühr wird an Ihre Rechnungsadresse gesendet."}, "descriptionSection": {"label": "<PERSON><PERSON>"}, "changelogSection": {"label": "Änderungsprotokoll", "more": "Mehr Informationen", "less": "<PERSON><PERSON>en", "version": "Version"}, "integrationsSection": {"title": "Integrationen mit externen Systemen", "description": "Unsere Plattform bietet Apps die Flexibilität, sich in viele verschiedene externe Systeme zu integrieren. Weitere Informationen zu den möglichen {url} finden Sie in unserer Hilfe.", "appIntegrations": "App-Integrationen", "missingIntegration": "Vermissen Sie eine Integration?", "missingDescription": "<PERSON>den <PERSON> sich an den {email}, um herauszufinden, ob die gewünschten Integrationen unterstützt werden können.", "developer": "<PERSON><PERSON><PERSON><PERSON>", "downloadPdf": "PDF herunterladen", "customDescription": "Diese App unterstützt Integrationen mit anderen Video-Management-Systeme und/oder Analytics-Platforms.", "customIntegrationName": "Custom Integration"}, "documentsSection": {"title": "Dokumente"}, "appRequirementsSection": {"title": "Voraussetzungen", "requiredVersion": "Erforderliche Betriebssystemversion", "requiredVersionHint": "Minimale OS Version die auf der Kamera für diese App benötigt wird.", "orHigher": "oder höher", "permissions": "App-Berechtigungen", "permissionsHint": "Berechtigungen, die die App benötigt und denen durch einen Kauf automatisch zugestimmt wird.", "noPermissionsRequired": "Diese A<PERSON> benötigt keine besonderen Berechtigungen.", "deviceCapabilities": "Gerätefähigkeiten", "deviceCapabilitiesHint": "<PERSON> dieser A<PERSON> benötigte Gerätefähigkeiten", "noDeviceCapabilities": "Es sind keine erforderlichen Gerätefähigkeiten für diese App angegeben."}, "toolRequirementsSection": {"title": "Voraussetzungen", "requiredVersion": "Erforderliche Betriebssystemversion", "requiredVersionHint": "Minimale OS Version die auf der Kamera für dieses Tool benötigt wird.", "orHigher": "oder höher", "permissions": "Tool-Berechtigungen", "permissionsHint": "Berechtigungen, die das Tool benötigt und denen durch einen Kauf automatisch zugestimmt wird.", "noPermissionsRequired": "<PERSON><PERSON> benötigt keine besonderen Berechtigungen.", "deviceCapabilities": "Gerätefähigkeiten", "deviceCapabilitiesHint": "Von diesem Tool benötigte Gerätefähigkeiten", "noDeviceCapabilities": "Es sind keine erforderlichen Gerätefähigkeiten für dieses Tool angegeben."}, "companyProfileSection": {"title": "Unternehmen", "companyInfo": {"founded": "Gründungsjahr", "size": "Firmengröße", "headquarters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visitCompanyProfile": "Unternehmensprofil besuchen"}}, "supportSection": {"title": "Support", "phone": "Telefon", "email": "E-Mail", "website": "Website"}, "followAppSection": {"app": {"label": "App folgen", "summary": "E-Mail-Benachrichtigungen über neue App-Versionen erhalten.", "notification": {"success": {"follow": "Du folgst jetzt {appName} und wirst per E-Mail über App-Updates benachrichtigt.", "unfollow": "Sie erhalten keine E-Mail-Benachrichtigungen mehr über {appName}."}}}, "tool": {"label": "Tool folgen", "summary": "E-Mail-Benachrichtigungen über neue Tool-Versionen erhalten.", "notification": {"success": {"follow": "Du folgst jetzt {appName} und wirst per E-Mail über Tool-Updates benachrichtigt.", "unfollow": "Sie erhalten keine E-Mail-Benachrichtigungen mehr über {appName}."}}}}, "sidebar": {"version": "Aktuelle Version", "legalCompanyName": "Rechtlicher Firmenname", "companyWebsite": "Firmenwebsite", "productId": "Product ID", "partnumber": "Partnummer", "privacyPolicyApp": {"title": "Datenschutzerklärung", "description": "Die Datenschutzerklärung von Bosch Digital Commerce GmbH gilt nicht für diese Anwendung. Bitte beziehen Si<PERSON> sich auf die Datenschutzerklärung der Anwendung", "link": "Datenschutzerklärung der Anwendung"}, "privacyPolicyTool": {"title": "Datenschutzerklärung", "description": "Die Datenschutzerklärung von Bosch Digital Commerce GmbH gilt nicht für das Tool. Bitte beziehen Si<PERSON> sich auf die Datenschutzerklärung des Tools", "link": "Datenschutzerklärung des Tools"}, "termsOfUseApp": {"title": "Lizenzbestimmungen", "link": "Endbenutzer-Lizenzvereinbarung der Anwendung", "appendix": "Anhang A: <PERSON><PERSON><PERSON><PERSON>", "standardMark": "Standard"}, "termsOfUseTool": {"title": "Lizenzbestimmungen", "link": "Endbenutzer-Lizenzvereinbarung des Tools", "appendix": "Anhang A: <PERSON><PERSON><PERSON><PERSON>", "standardMark": "Standard"}, "countries": {"title": "<PERSON>erfügbar in"}, "privateOffer": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Wenden Si<PERSON> sich an den Verkäufer dieser App, um benutzerdefinierte Bedingungen für Ihren Kauf anzufordern.", "button": "Verkäufer kontaktieren"}}, "reviewSection": {"title": "Rezensionen", "notReviewed": "<PERSON><PERSON> A<PERSON> wurde noch nicht bewertet.", "addReview": "Bewertung hinzufügen", "wantToAddReview": "<PERSON><PERSON>chten Sie eine Bewertung hinzufügen?", "shareReviewTeaser": "Teilen Sie Ihre Erfahrungen mit dieser App.", "buyApp": "Sobald Ihr Unternehmen diese App gekauft hat, können Sie Bewertungen abgeben."}, "reviewCreationForm": {"create": "Rezension erstellen", "headline": "Überschrift", "comment": "Beschreibung", "ratingHeadline": "<PERSON><PERSON><PERSON>*", "rating": "Bitte geben Sie ihre Bewertung ab.", "showCompany": "Firmennamen in der Rezension anzeigen.", "showName": "Namen in der Rezension anzeigen.", "thankYouTitle": "Vielen Dank für Ihre Rezension.", "fieldValidation": "Bitte überprüfen Sie Ihre Eingabe.", "generalError": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.", "required": "*Pflichfelder"}, "dualUseInfo": {"header": "Exportinformationen", "linkText": "<PERSON>u allen Apps und ihren Exportklassifikationen."}, "privateOfferDialog": {"header": "Angebotsanfrage", "description": "Fordern Sie ein Angebot des Verkäufers dieser App an. Sie werden benachrichtigt und kommen auf Sie zurück.", "requestedConditionTitle": "Angeforderte Bedingungen (optional)", "requestedConditionDescription": "Geben Sie die gewünschten Preise für diese App an und wie viele Lizenzen Sie kaufen möchten.", "messageTitle": "Nachricht an den Verkäufer", "messageHelper": "Nachricht", "fullLicensePriceHelper": "Einmaliger Kaufpreis", "fullLicenseQuantityHelper": "Menge der App-Käufe", "subscriptionPriceHelper": "Einschreibegebühr", "subscriptionQuantityHelper": "Menge App-Einschreibegebühr", "messageLengthError": "Nachrichtenlänge muss zwischen 0 und 1000 Zeichen", "priceNegativeError": "Der Pre<PERSON> kann nicht negativ sein", "priceEqualsError": "Der Preis kann nicht dem Listenpreis entsprechen", "quantityError": "Menge kann nicht negativ sein", "success": "Ihre private Angebotsanfrage wurde erfolgreich übermittelt", "projectRegistration": {"enable": "Ich möchte ein Projekt beim Verkäufer anmelden. {linkToGuide}", "learnMore": "<PERSON><PERSON> er<PERSON>", "projectData": {"title": "Projektinformationen (optional)", "projectName": "Projektname", "customerName": "Kundenname", "startDate": "<PERSON><PERSON><PERSON><PERSON>"}, "addressData": {"title": "Standortadresse (optional)", "country": "Land", "line1": "Straße und Hausnummer", "postalCode": "<PERSON><PERSON><PERSON><PERSON>", "city": "Stadt"}}}}, "checkout": {"header": "<PERSON><PERSON><PERSON> Checkout", "pageTitle": "Check out – ", "finalReview": "Abschließende Überprüfung", "cardHolder": "<PERSON><PERSON><PERSON><PERSON>", "cardName": "Name auf der <PERSON>", "cardNumber": "Kartennummer", "cardExpiry": "Verfallsdatum", "cardCvc": "Sicherheitscode", "saveCard": "Zahlungsinformationen speichern", "wrongValue": "Der Wert ist ungültig", "wrongCardName": "Die Länge des Karteninhabernamens muss 2 bis 26 Zeichen einschließlich Vorname, Nachname und Leerzeichen betragen.", "invoiceNote": {"title": "Interne Notizen zur Bestelldokument hinzufügen", "description": "Geben Sie zusätzliche Informationen wie interne Bestellnummer und Projektname an. Die Notizen werden auf der Bestelldokument angezeigt.", "noteLine": "Notizzeile {line} (optional)"}, "ownApp": {"feeNote": "Es wird eine Plattformgebühr erhoben"}, "orderEntries": {"futurePrice": "danach {futurePrice} pro 12 Monate"}, "miniSummary": {"headerAddress": "Re<PERSON>nungsadress<PERSON>", "ownPurchaseHeader": "Rechnungs E-Mail", "totalPrice": "Gesamtnettopreis", "taxNote": "Die Steuer ist auf dem Bestelldokument angegeben", "taxNoteInvoiceBySeller": "Die Rechnungsstellung einschließlich Steuerberechnung und Zahlungsvorgang wird vom Verkäufer verwaltet.", "taxHint": "Auf Ihrer Kreditkarte wird ein zusätzlicher Betrag zur Deckung der anfallenden Steuern reserviert. Nach der Steuerberechnung wird der in Rechnung gestellte Bruttobetrag von Ihrer Kreditkarte abgebucht."}, "backToCart": "Zurück zum Einkaufswagen", "newCard": "Neue Kreditkarte", "paymentMethod": "Zahlungsmethode", "orderSummary": "Bestellungszusammenfassung", "placeOrder": "<PERSON><PERSON><PERSON>", "placeOrderAndPay": "Bestellen und Bezahlen", "paymentMethodNotSavedHint": "<PERSON><PERSON> Bezahlmethode wird erst nach dem Kauf in Ihrem Account gespeichert.", "paymentMethodOverwriteHint": "Hinzufügen einer weiteren Bezahlmethode ersetzt Ihre zuvor temporär gespeicherte Bezahlmethode.", "placeOrderConfirmation": "Mit Abschicken der Bestellung bestätige ich die allgemeinen Geschäftsbedingungen der gekauften Apps.", "deletePaymentMethod": "Delete payment method?", "error": "Bei Ihrer Bestellung ist ein Fehler aufgetreten.", "byCompany": "<PERSON>", "confirmation": {"confirmationHeader": "Bestätigung", "confirmationLine1": "<PERSON><PERSON> Kauf wurde erfolgreich bearbeitet.", "confirmationLine2": "Es kann einige Minuten dauern, bis es in Ihrer Apps-Liste angezeigt wird.", "confirmationLineInvoice": "Das Bestelldokument mit den Zahlungsdetails wird in Kürze verfügbar sein.", "confirmationLine3": "Bestellnummer", "confirmationLine3plural": "Bestellnummern", "followAppNotification": "Sie werden über zukünftige App-Updates oder Änderungen der Lizenzbedingungen per E-Mail benachrichtigt. Um dies zu deaktivieren, folgen Sie der/den App(s) auf der App-Detailseite nicht mehr:", "continueShopping": "Einkauf fortsetzen", "installPrompt": "Wie beginne ich mit der Installation einer App?", "toolInstallInstructions": "<PERSON><PERSON> Ihre Kameras nur mit einem lokalen Netzwerk verbunden sind, müssen Si<PERSON> unser Geräteverwaltungstool herunterladen, um Apps zu installieren.", "installInstructions": "<PERSON>n Ihre Kameras bereits zu unserem {linkToPortal} hinz<PERSON><PERSON><PERSON><PERSON> wurden, können Sie die Installation direkt von dort aus remote starten", "deviceManagementPortal": "Geräteverwaltungsportal", "learnMore": "<PERSON><PERSON> er<PERSON>"}}, "cart": {"cart": "Einkaufswagen", "pageTitle": "Einkaufswagen – ", "emptyCartMessage": "<PERSON><PERSON> Einkaufswagen ist leer", "checkout": "<PERSON><PERSON> Ka<PERSON> gehen", "continueShopping": "Einkauf fortsetzen", "orderFrom": "Bestellung von ", "subscriptionTitle": "Abonnement"}, "cartTotal": {"total": "Nettopreis", "taxmessage": "* Ihre Bestellung enthält keine Steuern von", "tax": "Mwst."}, "bundleInfo": {"description": "{bundleName} ({bundleSize})", "size": "1 Lizenz | {n} Liz<PERSON><PERSON>"}, "cartItem": {"itemRemovedSuccessMessage": "Die App wurde aus dem Warenkorb entfernt.", "itemRemovedErrorMessage": "Die App konnte nicht entfernt werden. Bitte laden Sie die Seite neu und versuchen Si<PERSON> es erneut.", "messageOnUpdateCart": "Ihr Warenkorb wurde erfolgreich aktualisiert.", "messageOnUpdateCartWithDiscount": "Ihr Warenkorb wurde erfolgreich aktualisiert. {percent}% Mengenrabatt wurde angewendet", "updateCartGeneralError": "Es ist ein Fehler aufgetreten. Die Produktmenge konnte im Warenkorb nicht aktualisiert werden.", "messageOnUpdateCartFailed": "Das Produkt wurde bereits aus dem Warenkorb entfernt.", "volumeDiscount": "Mengenrabatt", "hint": "<PERSON><PERSON><PERSON> Si<PERSON> {min}+ und erhalten Sie jeweils {percent}% <PERSON><PERSON><PERSON>", "per12Months": "/ 12 Monate", "for12Months": "für 12 Monate", "ownApp": {"title": "<PERSON><PERSON>pp", "info": "Die Plattformgebühr wird separat mit Ihrer Rechnungsmail verrechnet."}}, "exportInformation": {"title": "Global trade compliance", "pageTitle": "Global trade compliance – ", "appName": "App-Name", "companyName": "Entwicklerfirma", "eccn": "ECCN"}, "orderStatus": {"progress": "In Bearbeitung", "completed": "Abgeschlossen", "pending": "Zahlung ausstehend", "overdue": "Zahlung überfällig", "refunded": "Zahlung erstattet", "partially_refunded": "Zahlung teilweise erstattet"}, "orderHistory": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageTitle": "Bestellverlau<PERSON> – ", "order": "Bestellung", "table": {"orderNumber": "Bestellnummer", "apps": "Apps", "placedBy": "Aufge<PERSON><PERSON> von", "orderDate": "Bestellung datum", "lastUpdated": "Letzte Aktualisierung", "status": "<PERSON><PERSON><PERSON><PERSON>"}, "noOrders": {"title": "<PERSON><PERSON>", "text": "Aufgegebene Bestellungen werden hier aufgelistet."}, "creditCardPaymentSuccess": "Die Kreditkartenzahlungsmethode wurde für die folgenden Bestellungen erfolgreich aktualisiert: "}, "orderDetails": {"items": {"by": "<PERSON>", "quantity": {"label": "<PERSON><PERSON>", "tooltipText": "Menge bei Auftragserteilung"}}, "orderInfo": {"label": "Bestelldetails", "placedDate": "Datum", "placedBy": "Aufge<PERSON><PERSON> von", "payment": {"method": {"label": "Zahlungsmethode", "invoiceBySeller": "Verwaltet vom Verkäufer", "sepaCredit": "SEPA-Überweisung", "achInternational": "ACH-Überweisung", "creditCard": "Kreditkarte", "sepaDirectdebit": "SEPA-Lastschrift"}}, "paymentFailed": "Es gibt ein Problem mit Ihrer Karte, welche auf {ccEnding} endet.", "update": "Aktualisieren"}, "invoices": {"label": {"default": "Rechnngen", "documents": "Dokumente"}, "table": {"headers": {"invoiceId": {"label": {"default": "Rechnung-ID", "document": "Dokument-ID"}}, "invoiceDate": {"label": {"default": "Rechnungsdatum", "document": "Datum"}}, "invoiceStatus": {"label": "Status"}, "totalAmount": {"label": {"default": "Gesamtsumme", "document": "Gesamtnettosumme"}, "tooltipText": "Inklusive Steuern"}}, "data": {"invoiceStatus": {"paid": "Be<PERSON>hlt", "exempt": "Befreit", "pending": "Zahlung ausstehend", "overdue": "Zahlung überfällig", "notPaid": "<PERSON><PERSON> bezahlt", "reversed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "Zahlung erstattet", "partiallyRefunded": "Zahlung teilweise erstattet", "paymentFailed": "Zahlung gescheitert"}, "documentIssuedDate": {"label": "{documentType} Dokument ausgestellt"}, "documentType": {"invoice": "<PERSON><PERSON><PERSON><PERSON>", "reversal": "Storno", "refund": "Rückerstattung", "partialRefund": "Teilerstattung", "transactionReport": "Transaction report"}}}}}, "payment": {"tooltip": {"bankTransferDisabled": "Zahlung per Überweisung ist für ihr Unternehmen nicht aktiv. Bitte kontaktieren Sie den Support.", "generalInfo": "Nach der Bestellung steht Ihnen ein Bestelldokument zur Verfügung.", "overValue": "Diese Bestellung überschreitet Ihr Unternehmenskreditlimit für SEPA-Überweisung. Für weitere Informationen wenden Sie sich bitte an unseren Kundensupport.", "invoicebyseller": "Aufgrund der lokalen koreanischen Steuervorschriften verwaltet der Verkäufer den Rechnungsstellungs- und Zahlungsvorgang.", "companyScope": "Company-wide payment method. Any user with purchase permission in your company account can view and use this payment method."}, "notEnabled": "Nicht aktiv", "removed": "Die Zahlungsmethode wurde erfolgreich entfernt.", "paymentDetailsHeader": "", "pageTitle": "Payment details – ", "savedPaymentMethods": "Gespeicherte Zahlungsmethoden", "creditCard": "Kreditkarte", "bankTransfer": "Banküberweisung", "directDebit": "Lastschrift", "noPaymentDetails": "", "noPaymentDetailsInfo": "Sie haben noch keine Zahlungsmethoden gespeichert. Sie können beim <PERSON><PERSON> von <PERSON> neue Zahlungsmethoden hinzufügen und speichern.", "sepaTransfer": "SEPA-Überweisung", "sepaDirectDebit": "SEPA-Lastschrift", "sepaMandateForAccount": "SEPA-Lastschrift für Konto", "createDirectDebitPaymentInfo": "Neues SEPA-Lastschriftmandat hinzufügen", "sepaMandateNew": "Neues SEPA-Lastschriftmandat", "mandateReference": "Mandatsreferenz", "dateOfSignature": "Datum der Unterschrift", "achInternational": "ACH-Überweisung", "transferTo": "Überweisung an", "accountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account": "Ko<PERSON>", "bankName": "Bank Name", "iban": "IBAN", "bicSwift": "BIC/Swift Nr.", "routing": "Routing-Nr.", "accountNumber": "Konto-Nr.", "invoicebyseller": "Verwaltet vom Verkäufer", "setPreference": "Als Präferenz festlegen", "removePreference": "Als Präferenz deaktivieren", "delete": "Löschen", "setPreferenceSuccess": "Die Zahlungsmethode wurde erfolgreich als Präferenz festgelegt.", "removePreferenceSuccess": "Die Zahlungsmethode wurde erfolgreich als Präferenz entfernt.", "paymentRemoved": "Die Zahlungsmethode wurde erfolgreich entfernt.", "deletePaymentMethod": "Zahlungsmethode löschen", "deletePaymentMethodInfo": "Diese Zahlungsmethode wird gelöscht. Si<PERSON> können beim <PERSON> von <PERSON> neue Zahlungsmethoden hinzufügen.", "settingPreferenceFailed": "Die Zahlungsmethode konnte nicht als Präferenz festgelegt werden. Bitte versuchen Si<PERSON> es später noch einmal oder wenden Si<PERSON> sich an den Kundensupport.", "billingAddress": "Re<PERSON>nungsadress<PERSON>", "billingAddressInfo": "Rechnungsadresse Ihres Unternehmens. Um dies zu ändern, {url}.", "contactCustomerSupport": "wenden Si<PERSON> sich an den Kundendienst"}, "updatePayment": {"header": "Zahlungsmethode aktualisieren", "subtitle": "Bitte wählen Sie eine andere Zahlungsmethode oder fügen Si<PERSON> eine neue Karte hinzu.", "newCard": "Neue Kreditkarte", "finalReview": "Abschließende Prüfung", "cardHolder": "<PERSON><PERSON><PERSON><PERSON>", "cardName": "Name auf der <PERSON>", "cardNumber": "Kartennummer", "cardExpiry": "Verfallsdatum", "cardCvc": "Sicherheitscode", "saveCard": "Als bevorzugte Karte speichern und für alle überfälligen/zukünftigen Bestellungen aktualisieren", "wrongValue": "Der Wert ist ungültig", "wrongCardName": "Der Name des Karteninhabers muss 2 bis 26 <PERSON><PERSON><PERSON> lang sein, e<PERSON><PERSON><PERSON><PERSON><PERSON> Vorname, Nachname und Leerzeichen.", "problemWithPayment": "Es gab ein Problem mit Ihrer Karte, welche auf {ccEnding} endet.", "placeOrder": "Bestellung aufgeben", "placeOrderAndPay": "Bestellen und Bezahlen", "paymentMethodNotSavedHint": "<PERSON><PERSON>ung<PERSON>t wird erst nach dem Kauf in Ihrem Konto gespeichert.", "paymentMethodOverwriteHint": "Das Hinzufügen einer weiteren Zahlungsmethode ersetzt Ihre zuvor vorübergehend hinzugefügte Zahlungsmethode.", "placeOrderConfirmation": "<PERSON>t dieser Bestellung stimme ich den Nutzungsbedingungen für die gekauften Apps zu.", "deletePaymentMethod": "Zahlungsmethode löschen?"}, "tools": {"pageTitle": "Tools – ", "header": {"title": "<PERSON><PERSON><PERSON>", "description": "Finden Sie kostenlose <PERSON>ls, um Ihre Kamera zu erweitern"}, "container": {"title": "<PERSON><PERSON><PERSON>"}}, "companyProfile": {"companyInfo": {"founded": "Gründungsjahr", "size": "Firmengröße", "headquarters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visitWebsite": "Unternehmenswebsite", "visitLinkedinProfile": "Unternehmens-Linkedin-Profils", "about": "<PERSON><PERSON>"}, "contactInfo": {"sales": "Verkaufsabteilung", "support": "Supportabteilung", "phone": "Telefon", "email": "Email", "supportPage": "Support-Seite"}, "apps": {"title": "Apps", "noAppsFound": "<PERSON><PERSON> gefunden."}}, "error": {"backend": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.", "validation": "Einige Ihrer Eingaben sind ungültig.", "generic": {"serverError": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.", "notFound": "Die angeforderte Ressource konnte nicht gefunden werden.", "badRequest": "Die Anfrage war ungültig und konnte nicht verarbeitet werden.", "forbidden": "Sie verfügen nicht über die Berechtigung für den Zugriff auf diese Ressource."}, "review": {"exists": "Ein Mitarbeiter Ihrer Firma hat dieses Produkt bereits rezensiert.", "appNotOwned": "<PERSON><PERSON> müssen dieses Produkt erst erwerben, um es rezensieren zu können."}, "checkout": {"cartModified": "Ihr Warenkorb wurde geändert. Bitte verifizieren Sie die Änderungen und gehen Sie erneut zur Kasse.", "placeOrderFailed": "Die Bestellung konnte nicht aufgegeben werden.", "paymentFailed": "Bei Ihrer Zahlung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut oder wählen eine andere Zahlmethode.", "invalidCartState": "Ein technischer Fehler ist aufgetreten, bitte versuchen Sie es erneut. Falls der Fehler weiterhin besteht, setzen <PERSON> sich bitte mit unserem Support in Verbindung."}, "cart": {"unpayable": "<PERSON><PERSON><PERSON> <PERSON>hren Warenkorb konnte keine gültige Bezahlmethode gefunden werden. Bitte reduzieren Sie den Gesamtwert des Warenkorbes.", "empty": "<PERSON><PERSON> können nicht mit einem leeren Warenkorb zur Kasse gehen.", "invalidQuantity": "Die maximale Bestellmenge eines einzelnen Produktes beträgt {0} für Lizenzen mit einmaliger Zahlung, und {1} für Abonnements.", "noSelfPurchases": "<PERSON>e können keine Produkte von sich selbst erwerben.", "addFailed": "<PERSON><PERSON> zum Warenkorb ist ein Fehler aufgetreten.", "unsupportedLicense": "Produkt kann nicht zum Warenkorb hinzugefügt werden.", "preparationFailure": "Bei der Vorbereitung zum Kauf Ihres Warenkorbes ist ein technischer Fehler aufgetreten, bitte versuchen Si<PERSON> es später noch einmal. Falls der Fehler weiterhin besteht, setzen Sie sich bitte mit unserem Support in Verbindung."}, "licenseactivation": {"generic": "Lizenzaktivierung fehlgeschlagen.", "notPurchasable": "Lizenzaktivierung fehlgeschlagen. Liz<PERSON>z kann nicht erworben werden.", "notSupported": "Lizenzaktivierung fehlgeschlagen. Lizenztyp wird nicht unterstützt."}, "companyprofile": {"notFound": "Firmenprofil konnte nicht gefunden werden."}, "product": {"notFound": "Das Produkt konnte nicht gefunden werden."}}, "storeEditSuccessResponseMessage": "Die Daten wurden erfolgreich gespeichert", "contactCustomerSupport": "Kontaktieren Sie den Support"}}