import {wrapperComponentFactory, resizeWidth} from 'common/testtools/unit-test-utils';
import BreakpointTester from '../BreakpointTester.vue';

const mountBreakpointTester = () => wrapperComponentFactory(BreakpointTester);

describe('BreakpointTester', () => {

    it('default window width is 1024', () => {
        expect(window.innerWidth).toBe(1024);
    });

    it('sizes up to xl', () => {
        resizeWidth(1600);
        const wrapper = mountBreakpointTester();
        expect(wrapper.vm.$vuetify.breakpoint.lgAndUp).toBe(true);
    });

    it('sizes down to mobile', () => {
        resizeWidth(400);
        const wrapper = mountBreakpointTester();
        expect(wrapper.vm.$vuetify.breakpoint.smAndDown).toBe(true);
    });

});