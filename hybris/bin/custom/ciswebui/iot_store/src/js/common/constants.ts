export namespace Global {
    export const CUSTOMER_NAME_LENGTH = 25;
}

export namespace ShopRoute {
    export const HOME: string = '/shop';
    export const HOME_NAME: string = 'HomePage';

    export const LOGIN_REDIRECT = (redirectUrl: string): string => `${HOME}/login?redirect=${redirectUrl}`;

    export const CART: string = `${HOME}/cart`;
    export const CART_NAME: string = 'CartPage';

    export const CART_CHECKOUT = (cartCode:string):string => `${HOME}/cart/${cartCode}/checkout`;

    export const PRODUCTS: string = `${HOME}`;
    export const PRODUCTS_NAME: string = 'ProductsPage';

    export const TOOLS: string = `${HOME}/tools`;

    export const PRODUCT_DETAIL: string = `${HOME}/p/*`;
    export const PRODUCT_DETAIL_NAME: string = `ProductDetailsPage`;

    export const PRODUCTS_OVERVIEW: string = `${HOME}/products-overview`;
    export const PRODUCTS_OVERVIEW_NAME: string = 'ProductsOverviewPage';

    export const PRODUCTS_SEARCH: string = `${HOME}/products-search`;
    export const PRODUCTS_SEARCH_NAME: string = 'ProductsSearchPage';

    export const PRODUCT_SELECTION = (productCode: string = ':productCode'): string => `${HOME}/product-selection/${productCode}`;
    export const PRODUCT_SELECTION_NAME: string = 'ProductSelectionPage';

    export const CHECKOUT_SUMMARY: string = `${HOME}/checkout/multi/summary/view`;
    export const CHECKOUT_SUMMARY_BACK: string = `${HOME}/checkout/multi/summary/back`;
    export const CHECKOUT_SUMMARY_PLACE_ORDER: string = `${HOME}/checkout/multi/summary/placeOrder`;
    export const CHECKOUT_SUMMARY_CONFIRM_ORDER: string = `${HOME}/checkout/multi/summary/confirmOrder`;
    export const CHECKOUT_SUMMARY_ERROR: string = `${HOME}/checkout/multi/summary/error`;

    export const CHECKOUT_CONFIRMATION: string = `${HOME}/checkout/order-confirmation/*`;
    export const CHECKOUT_CONFIRMATION_NAME: string = 'CheckoutConfirmationPage';

    export const PENDING_CHECKOUT_CONFIRMATION: string = `${HOME}/checkout/order-pending-confirmation/*`;
    export const DELAYED_CHECKOUT_CONFIRMATION: string = `${HOME}/checkout/order-delayed-confirmation/*`;
    export const PENDING_CHECKOUT_CONFIRMATION_NAME: string = 'PendingCheckoutConfirmationPage';
    export const DELAYED_CHECKOUT_CONFIRMATION_NAME: string = 'DelayedCheckoutConfirmationPage';

    export const CHECKOUT_PAYMENT_METHOD: string = `${HOME}/checkout/multi/payment-method/add`;

    export const CHECKOUT_PAYMENT: string = `${HOME}/checkout/multi/payment`;
    export const CHECKOUT_PAYMENT_BACK: string = `${HOME}/checkout/multi/payment/back`;
    export const CHECKOUT_PAYMENT_PLACE_ORDER: string = `${HOME}/checkout/multi/payment/placeOrder`;
    export const CHECKOUT_PAYMENT_ERROR: string = `${HOME}/checkout/multi/payment/error`;
    export const CHECKOUT_PAYMENT_ADD: string = `${HOME}/checkout/multi/payment/add`;
    export const CHECKOUT_PAYMENT_CONFIRM: string = `${HOME}/checkout/multi/payment/confirmOrder`;

    export const CHECKOUT_PAYMENT_METHOD_CHOOSE: string = `${HOME}/checkout/multi/payment-method/choose`;
    export const CHECKOUT_PAYMENT_METHOD_BACK: string = `${HOME}/checkout/multi/payment-method/back`;

    export const EXPORT_INFORMATION_OVERVIEW: string = `${HOME}/export-information`;

    export const ORDER_HISTORY: string = `${HOME}/my-account/orders`;
    export const ORDER_HISTORY_NAME: string = 'OrderHistoryPage';

    export const ORDER_DETAILS: string = `${HOME}/my-account/orders/*`;
    export const ORDER_DETAILS_NAME: string = 'OrderDetailsPage';

    export const PAYMENT_DETAILS: string = `${HOME}/my-account/payment-details`;
    export const PAYMENT_DETAILS_NAME: string = 'PaymentDetailsPage';

    export const UPDATE_PAYMENT_METHOD = `${HOME}/my-account/orders/:orderCode/update-payment`;
    export const UPDATE_PAYMENT_METHOD_NAME: string = 'UpdatePaymentMethodPage';

    export const COMPANY_PROFILE: string = `${HOME}/company-profile/*`;

    export const HELP_AND_RESOURCES: string = `${HOME}/help-resources`;

    export const COUNTRY_STORE_API_RESOURCE: string = `${HOME}/api/session/country-store`;

    export const RETURN_TO_COMPANY_STORE_API_RESOURCE: string = `${COUNTRY_STORE_API_RESOURCE}/default`;
}

export namespace ShopDomains {
    export const LOCAL: string = 'store.dev.local';
    export const OLD_DEV: string = 'store.securityandsafetydev';
    export const DEV: string = 'store.camdroid';
    export const DEMO: string = 'store.securityandsafetydemo';
    export const OLD_LIVE: string = 'store.securityandsafetythings';
    export const LIVE: string = 'store.azena';
}

export namespace ScreenSize {
    export const EXTRA_LARGE_MIN_WIDTH = 1200;
}

export enum AlertType {
    SUCCESS = 'success',
    INFO = 'info',
    WARNING = 'warning',
    ERROR = 'error',
}

export namespace SupportUrlPaths {
    export const INSTALL_APPS_ONLINE: string = '/en-us/articles/360043594293-2-Installing-applications-in-an-online-environment';
    export const INSTALL_APPS_OFFLINE: string = '/en-us/articles/360043080614-3-Installing-applications-in-an-offline-environment';
    export const PROJECT_REGISTRATION: string ='/en-us/articles/4406311021587-Purchasing-application-Request-custom-offer';
}

export enum BaseStores {
    AZENA = 'iotstore',
    AA = 'aastore'
}

export namespace ProductsOverview {
    export const MAX_HARDWARE_REQUIREMENTS_TO_DISPLAY_ON_TAB = 2;
}

