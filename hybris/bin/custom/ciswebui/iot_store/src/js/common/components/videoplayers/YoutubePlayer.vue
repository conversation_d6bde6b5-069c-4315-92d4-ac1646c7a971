<template>
  <vue-plyr v-bind:options="videoOptions" class="player">
    <div v-if="videoUrl" data-plyr-provider="youtube" v-bind:data-plyr-embed-id="videoUrl"></div>
    <div v-else data-plyr-provider="youtube" v-bind:data-plyr-embed-id="video.source"></div>
  </vue-plyr>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { AppVideoData } from 'common/generated-types/types';
import { Options } from 'plyr';

@Component
export default class YoutubePlayer extends Vue {
  @Prop() videoUrl!: string;
  @Prop() video!: AppVideoData;

  get videoOptions(): Options {
    return {
      enabled: true,
      debug: false
    };
  }
}
</script>

<style lang="scss">
@import 'common/vuetify';

.player {
    .plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::before,
    .plyr__control--overlaid,
    .plyr--video .plyr__control:hover {
        background: var(--v-primary-base);
    }
    .plyr--full-ui input[type=range] {
        color: var(--v-primary-base);
    }
    .plyr {
        @media #{map-get($display-breakpoints, 'sm-and-up')} {
            border-radius: map-get($rounded, 'lg');
        }
    }
}
</style>
