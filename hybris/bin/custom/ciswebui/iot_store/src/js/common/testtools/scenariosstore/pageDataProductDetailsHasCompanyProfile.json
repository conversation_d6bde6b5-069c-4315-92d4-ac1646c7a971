{"company": {"companyUid": "b64cf5ad-3ef8-4b56-9984-00b6ad5bf923", "name": "smart iot", "companyCountry": "DE", "hasPublishedProfile": true, "profileUrl": "/company-profile/b64cf5ad-3ef8-4b56-9984-00b6ad5bf923"}, "appContactInfo": {"emailAddress": "<EMAIL>", "supportPhoneNumber": "1234", "supportPageUrl": "http://www.google.com", "productWebsiteUrl": "https://www.azena.com/products/bbq-watch", "privacyPolicyUrl": "https://www.azena.com/privacy-policy", "eula": {"type": "CUSTOM", "customUrl": "some test url", "standardEulaAppendix": null}}, "name": "BBQ Watch", "shortDescription": "Check the temperature of your BBQ.", "description": "<h1>BBQ Checking</h1><p>Check the temperature of your BBQ.</p><ul><li>Lets <a href='https://www.youtube.com/watch?v=SVyLlFezj2E'>smoke them meats</a></li><li>Lets <a href='https://store.dev.local:9002/shop'>link internally</a></li></ul><h2>All the things that are good</h2><h3>Best things</h3><ol><li>It's excellent</li><li>It works</li></ol>", "acquisitionCount": 0, "versionName": "1.0.0", "postUrl": "/shop/ajax/cart/add", "reviewStatus": "NOT_BOUGHT_APP", "isReviewAllowed": true, "postReviewUrl": "/shop/A_00000017_full/review", "descriptionHeight": 400, "changelogHeight": 400, "code": "A_00000017", "readOnly": false, "enabledCountries": [{"currencyIsoCode": "DE", "isocode": "DE", "name": "Germany", "canBuy": true, "sepaEnabled": true}, {"currencyIsoCode": "GB", "isocode": "GB", "name": "United Kingdom", "canBuy": true, "sepaEnabled": true}], "logoUrl": "/sample-data/logo.svg", "licenses": [{"code": "A_00000017_evaluation", "name": "Trial", "summary": null, "price": 0, "currencyIsocode": "USD", "currencySymbol": "USD", "licenseType": {"code": "EVALUATION", "type": "LicenseType"}, "purchasability": "PURCHASABLE", "quantity": 0}, {"code": "A_00000017_full", "name": "Purchase", "summary": null, "price": 1999, "currencyIsocode": "USD", "currencySymbol": "USD", "licenseType": {"code": "FULL", "type": "LicenseType"}, "purchasability": "PURCHASABLE", "quantity": 0}], "supportedLicenses": [{"code": "EVALUATION", "name": "Trial", "licenseType": {"code": "EVALUATION", "type": "LicenseType"}}, {"code": "FULL", "name": "Purchase", "licenseType": {"code": "FULL", "type": "LicenseType"}}], "pdfDocuments": [{"displayName": "hybris Commerce Developer Training 1.02 - Installing the Accelerator", "url": "/shop/medias/file-name.pdf?context=someContext", "size": 2972664, "code": "pc_00001003_documentation-files_3fc60bb0-2186-406e-a011-cd2de7e5d11f"}, {"displayName": "hybris Commerce Developer Training 1.01 - Overview", "url": "/shop/medias/another-file-name.pdf?context=anotherContext", "size": 2431990, "code": "pc_00001003_documentation-files_a1ff2cdb-b5dc-4e9a-b51c-1a9499e7f54e"}], "galleryItems": [{"url": "/sample-data/sample-screenshot.jpeg", "key": 0}, {"url": "/sample-data/sample-screenshot-2.jpeg", "key": 1}, {"url": "/sample-data/sample-screenshot.jpeg", "key": 2}], "reviews": [{"id": "8796158885937", "headline": "", "comment": "Absolut geniale <PERSON>, hält was sie verspricht! Habe mir direkt 20 weitere Lizenzen geholt.", "rating": 5, "date": "2020-04-01T15:14:13.664Z", "alias": "<PERSON>", "principal": {"uid": "", "name": ""}, "showCompany": true, "showName": false, "company": "De<PERSON> Apps"}, {"id": "8796158820401", "headline": "", "comment": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> stürzt regelmäßig ab und kann nur nach einem Neustart wieder genutzt werden. Kann jedem nur vom Kauf abraten. Sehr ärgerlich.", "rating": 1, "date": "2020-04-01T15:14:13.664Z", "alias": "<PERSON><PERSON>", "principal": {"uid": "", "name": ""}, "showCompany": true, "showName": false, "company": "ABC Company"}], "changelogs": [{"version": "1.0.0", "description": "", "creationDate": "2019-11-19T18:37:22.331Z"}], "permissions": [], "averageRating": 2, "numberOfReviews": 0, "osCompatibility": {"minOsVersion": "1.0", "maxOsVersion": ""}, "dualUse": true, "dualUseInfoText": "Sample dual use info text. This comes from the backend.", "followAppData": {"subscribed": false}, "ownCompanyAppAndCanBuy": false}