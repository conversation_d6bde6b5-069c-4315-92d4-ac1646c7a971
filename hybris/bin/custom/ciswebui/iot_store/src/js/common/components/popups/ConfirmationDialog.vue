<template>
  <popup-dialog class="confirmation-dialog"
                v-bind="$attrs"
                v-on="$listeners"
                v-bind:close-button="{ text: closeButton }"
                v-bind:submit-button="{ text: submitButton }"
                v-on:submit="confirm"
                v-on:close="close">
    <template v-if="confirmation" #header>
      {{ confirmation.header }}
    </template>
    <div v-if="confirmation">
      <p v-for="(line, index) in confirmation.bodyLines" :key="index">
        {{ line }}
      </p>
    </div>
  </popup-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import PopupDialog from './PopupDialog.vue';
import { ConfirmationDialogType } from 'common/types';

@Component({
  components: { PopupDialog },
})
export default class ConfirmationDialog extends Vue {
  @Prop() confirmation?: ConfirmationDialogType;
  @Prop() saveButtonName?: string;

  get submitButton(): string {
    return this.confirmation?.buttonLabel || (this.$t('ok') as string);
  }

  get closeButton(): string {
    return this.confirmation?.cancelButtonLabel || (this.$t('cancel') as string);
  }

  confirm(): void {
    this.$emit('confirm');
  }

  close(): void {
    this.$emit('cancel');
  }

}
</script>