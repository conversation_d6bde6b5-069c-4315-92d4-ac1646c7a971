describe('Default Store Dialog', () => {

    it('looks as expected', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/common-components-__ui_tests__-DefaultStoreDialog');
        expect(await page.title()).toBe('UI test - common-components-__ui_tests__-DefaultStoreDialog');

        await page.setViewport({width: 2000, height: 1200});
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot("desktop");

        await page.setViewport({width: 450, height: 800});
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot("mobile");
    });

})