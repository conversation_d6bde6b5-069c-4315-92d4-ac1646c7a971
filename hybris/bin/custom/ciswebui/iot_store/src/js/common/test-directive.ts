import Vue from 'vue';
import {DirectiveBinding} from "vue/types/options";

Vue.directive('test', {
    bind(el: HTMLElement, binding: DirectiveBinding) {
        let testClass = '';

        if(binding.arg) {
            testClass = binding.arg;
        }
        if(binding.value !== undefined) {
            testClass = `${testClass}-${binding.value}`;
        }
        if(testClass !== '' && !el.classList.contains(testClass)) {
            const hasClass = Object.keys(binding.modifiers)[0];
            const childTag = Object.keys(binding.modifiers)[1] as string;
            if(el.classList.contains(hasClass)) {
                el.querySelector(childTag)?.classList.add(testClass);
            } else {
                el.classList.add(testClass);
            }
        }
    }
});
