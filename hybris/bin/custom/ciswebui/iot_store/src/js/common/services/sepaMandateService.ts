const sepaMandateService = {
  isValidIban(ibanStr: string): boolean {
    // return true if iban is empty or null
    if (!ibanStr || ibanStr.trim() === '') {
      return true;
    }

    const cleaned = ibanStr.replace(/\s+/g, '');
    if (!/^[A-Z0-9]+$/.test(cleaned)) {
      return false;
    }
    const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$/;
    return ibanRegex.test(cleaned);
  },

  isValidMandateReference(mandateReference: string): boolean {
    const mandateReferenceRegex = /^[A-Z0-9+\?\/\-\:\(\)\.,']{1,35}$/;
    return mandateReferenceRegex.test(mandateReference);
  },

  isValidAccountHolderName(accountHolderName: string): boolean {
    // return true if account holder name is empty or null
    if (!accountHolderName || accountHolderName.trim() === '') {
      return true;
    }

    const trimmed = accountHolderName.trim();
    // Check length: 2-50 characters
    if (trimmed.length < 2 || trimmed.length > 50) {
      return false;
    }

    // Check allowed characters: letters, spaces, hyphens, and apostrophes
    const accountHolderNameRegex = /^[A-Za-z\s\-']+$/;
    return accountHolderNameRegex.test(trimmed);
  },


};

export { sepaMandateService };