import ConfirmationDialog from 'common/components/popups/ConfirmationDialog';
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';

describe('ConfirmationDialog component', () =>{

    it('simple cancel', async () => {

        const wrapper = wrapperComponentFactory(ConfirmationDialog, {
           props: {
               confirmation: {
                   header: 'someHeader',
                   bodyLines: ['someBody']
               }
           },
           shallow: false
        });
        expect(wrapper.html()).not.toBeUndefined();
        expect(wrapper.find('.modal-header').text()).toEqual('someHeader');
        expect(wrapper.find('.modal-body').text()).toEqual('someBody');

        await wrapper.find('[data-id="button-close"]').trigger('click');
        expect(wrapper.emitted('confirm')).toBeFalsy();
        expect(wrapper.emitted('cancel')).toBeTruthy();
    });

    it('simple confirm', async () => {
        const wrapper = wrapperComponentFactory(ConfirmationDialog, {
            props: {
                confirmation: {
                    header: 'someHeader',
                    bodyLines: ['someBody']
                }
            },
            shallow: false
        });
        await wrapper.find('[data-id="button-submit"]').trigger('click');
        expect(wrapper.emitted('confirm')).toBeTruthy();
        expect(wrapper.emitted('cancel')).toBeFalsy();
    });
});