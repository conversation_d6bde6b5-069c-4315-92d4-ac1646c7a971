{"company": {"name": "De<PERSON> Apps"}, "appContactInfo": {"emailAddress": "<EMAIL>", "supportPhoneNumber": null, "supportPageUrl": null, "productWebsiteUrl": null, "privacyPolicyUrl": "https://www.azena.com/privacy-policy", "eula": {"type": "CUSTOM", "customUrl": "some test url", "standardEulaAppendix": null}}, "name": "Heatmapping", "shortDescription": "Heatmapping helps retailers to understand customers' behavior through heatmaps. It’s a software that is installed on the cameras where you can see heatmaps in real time. You can also access from", "description": "<p>Heatmapping helps retailers to understand customers' behavior through heatmaps. It’s a software that is installed on the SAST cameras where you can see heatmaps in real time. You can also access from a cloud platform for a larger analysis. Heatmapping is very easy to install and it starts to extract data in minutes. It’s the most agile way to deploy heatmaps in your business.</p>", "acquisitionCount": 0, "versionName": "42.0.0", "postUrl": "/shop/ajax/cart/add", "isReviewAllowed": false, "readOnly": false, "contactUrl": "https://www.azena.com/contact", "postReviewUrl": "/shop/A_00001003_full/review", "code": "A_00001003", "logoUrl": "/sample-data/logo.svg", "licenses": [{"code": "A_00001003_full", "name": "Purchase", "price": 199, "currencySymbol": "$", "currencyIsocode": "USD", "licenseType": {"code": "FULL", "type": "LicenseType"}, "purchasability": "PURCHASABLE"}], "pdfDocuments": [], "galleryItems": [{"url": "/sample-data/sample-screenshot.jpeg", "key": 0}], "reviewStatus": "NOT_BOUGHT_APP", "reviews": [], "changelogs": [{"version": "42.0.0", "description": null, "creationDate": "2019-11-19T18:37:22.331Z"}], "permissions": [], "enabledCountries": [{"currencyIsoCode": "DE", "isocode": "DE", "name": "Germany", "canBuy": true, "sepaEnabled": true}, {"currencyIsoCode": "GB", "isocode": "GB", "name": "United Kingdom", "canBuy": true, "sepaEnabled": true}], "osCompatibility": {"minOsVersion": "1.0", "maxOsVersion": ""}, "dualUse": false, "dualUseInfoText": "", "followAppData": {"subscribed": false}}