<template>
  <v-dialog v-bind="$attrs"
            v-on="$listeners"
            :width="width"
            :fullscreen="fullscreen"
            :scrollable="scrollable"
            :value="true"
            @keydown.esc="close">
    <v-card flat>
      <v-card-title class="d-flex justify-space-between align-baseline">
        <h2 class="modal-header">
          <slot name="header"></slot>
        </h2>
        <CDButtonIcon
            v-if="headerClose"
            icon="$close"
            @click="close"
            class="mt-n3"
            data-id="button-header-dialog-close"></CDButtonIcon>
      </v-card-title>

      <v-card-text class="modal-body">
        <slot></slot>
      </v-card-text>

      <v-card-actions class="modal-footer justify-end">
        <slot name="footer">
          <CDButton v-if="closeButton" 
                    @click="close"
                    :disabled="closeButton.isDisabled"
                    :name="closeButtonName"
                    data-id="button-close"
                    :color="!submitButton ? 'primary' : undefined"
                    class="ma-1 text-capitalize">
            {{ closeButton.text }}
          </CDButton>
          <CDButton v-if="submitButton" 
                    @click="submit"
                    :disabled="submitButton.isDisabled"
                    :name="saveButtonName"
                    data-id="button-submit"
                    color="primary"
                    class="ma-1 text-capitalize">
            {{ submitButton.text }}
          </CDButton>
        </slot>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

export type Button = {
  text: string;
  isDisabled?: boolean;
};

@Component
export default class PopupDialog extends Vue {
  @Prop() submitButton?: Button;
  @Prop() closeButton?: Button;
  @Prop() saveButtonName?: string;
  @Prop() closeButtonName?: string;
  @Prop({ default: 600 }) width!: number;
  @Prop({ default: false }) fullscreen!: boolean;
  @Prop({ default: false }) scrollable!: boolean;
  @Prop({ default: false }) headerClose!: boolean;

  close(): void {
    this.$emit('close');
  }

  submit(): void {
    this.$emit('submit');
  }
}
</script>

<style scoped lang="scss">
.modal-header {
  flex: 1;
}
</style>