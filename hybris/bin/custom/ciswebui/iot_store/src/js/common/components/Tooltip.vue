<template>
    <div class="tooltip-container" data-id="tooltip-container" ref="container">
        <div v-on:mouseover="showTooltip" v-on:mouseleave="hideTooltip" class="key" ref="key">
            <slot></slot>
        </div>
        <div class="tooltip" ref="tooltip">
            <slot name="text">{{text}}</slot>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from 'vue-property-decorator';
    import anime from 'animejs';
    import def from 'common/defaults';

    @Component
    export default class Tooltip extends Vue {
        @Prop({default: ''}) text!: string;
        @Prop({default: false}) displayOnlyIfTruncated!: boolean;

        $refs!: {
            key: HTMLElement;
            tooltip: HTMLElement;
            container: HTMLElement;
        };

        showTooltip(): void {
            if (!this.displayOnlyIfTruncated || this.fieldIsTruncated()) {
                anime({
                    targets: this.$refs.tooltip,
                    opacity: 1,
                    duration: 150,
                    easing: def.animation.easeout,
                    begin: () => {
                        this.$refs.tooltip.style.display = 'block';
                    }
                });
            }
        }

        fieldIsTruncated(): boolean {
            const slotItem = this.$refs.key.children.item(0);
            if (slotItem && slotItem instanceof HTMLElement) {
                return slotItem.scrollWidth > slotItem.offsetWidth;
            }
            return false;
        }

        hideTooltip(): void {
            anime({
                targets: this.$refs.tooltip,
                opacity: 0,
                duration: 150,
                easing: def.animation.easeout,
                complete: () => {
                    this.$refs.tooltip.style.display = 'none';
                }
            });
        }

        mounted(): void {
            this.adaptPosition();
        }

        adaptPosition(): void {
            const tooltip = this.$refs.tooltip;
            tooltip.style.top = (this.$refs.key.clientHeight + 5) + 'px';

            const boundingRect = tooltip.getBoundingClientRect();
            const windowWidth = window.innerWidth;

            if (boundingRect.width > windowWidth - 10) {
                tooltip.style.width = (windowWidth - 10) + 'px';
                this.adaptPosition();
                return;
            }

            if (boundingRect.right > windowWidth) {
                if (boundingRect.left < boundingRect.width) {
                    tooltip.style.left = (windowWidth - boundingRect.right - 5) + 'px';
                } else {
                    tooltip.style.right = '0';
                }
            } else {
                tooltip.style.left = '0';
            }

            this.resetOverflow();
        }

        resetOverflow(): void {
            this.$refs.tooltip.style.display = 'none';
            this.$refs.container.style.overflow = 'inherit';
        }
    }
</script>

<style scoped lang="scss">
    @import "common/design";

    .tooltip-container {
        position: relative;
        overflow: hidden;
        div {
            cursor: pointer;
        }
    }

    .key {
        width: 100%;
        height: inherit;
        display: inline-block;
    }

    .tooltip {
        overflow-wrap: break-word;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        border-radius: 6px;
        box-shadow: 1px 8px 16px 0 rgba(176, 192, 237, 0.16), 0 2px 6px 0 rgba(115, 125, 155, 0.16);
        border: solid 1px #f6f5f5;
        background-color:  var(--v-grey-lighten5);
        padding: 10px;
        width: max-content;
        width: -moz-max-content;
        width: -webkit-max-content;
        max-width: 384px;
        z-index: 2;
        position: absolute;
        text-align: left;
        opacity: 0;
    }
</style>
