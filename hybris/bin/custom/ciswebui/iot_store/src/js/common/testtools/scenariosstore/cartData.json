[{"totalPrice": {"symbol": "EUR", "value": "1,198.00"}, "totalPriceWithTax": {"symbol": "EUR", "value": "1,425.62"}, "totalTax": {"symbol": "EUR", "value": "227.62"}, "cartItems": [{"appCode": "A_00000402", "bundleInfo": null, "productName": "Bookmark 4", "productCode": "A_00000402_full", "productUrl": "/p/A_00000402", "companyName": "ABC Company", "logoUrl": "/sample-data/app/app-icon-medium-1.jpeg", "licenseType": "FULL", "itemPrice": {"symbol": "EUR", "value": "199.00"}, "totalPrice": {"symbol": "EUR", "value": "199.00"}, "quantity": 1, "entryNumber": 0, "specialOffer": false}, {"appCode": "A_00000406", "bundleInfo": null, "productName": "CrossStreets 4", "productCode": "A_00000406_full", "licenseType": "FULL", "productUrl": "/p/A_00000406", "companyName": "ABC Company", "logoUrl": "/sample-data/app/app-icon-medium-2.jpeg", "itemPrice": {"symbol": "EUR", "value": "999.00"}, "totalPrice": {"symbol": "EUR", "value": "999.00"}, "quantity": 1, "entryNumber": 1, "specialOffer": false}], "cartCode": "cart_00001003", "developerCompanyName": "ABC Company"}, {"totalPrice": {"symbol": "EUR", "value": "299.00"}, "totalPriceWithTax": {"symbol": "EUR", "value": "355.81"}, "totalTax": {"symbol": "EUR", "value": "56.81"}, "cartItems": [{"appCode": "A_00000403", "productName": "Cars 4", "productCode": "A_00000403_full", "licenseType": "FULL", "productUrl": "/p/A_00000403", "companyName": "De<PERSON> Apps", "logoUrl": "/sample-data/app/app-icon-medium-3.jpeg", "itemPrice": {"symbol": "EUR", "value": "299.00"}, "totalPrice": {"symbol": "EUR", "value": "299.00"}, "quantity": 1, "entryNumber": 0, "bundleInfo": null}], "cartCode": "cart_00001004", "developerCompanyName": "De<PERSON> Apps"}]