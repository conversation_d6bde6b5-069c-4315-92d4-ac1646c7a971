import {times, constant} from 'lodash';

let numberRounderService = {

    purchases: function(installNumber: number) {
        let numberString: string = installNumber.toString();
        let firstDigit: string = numberString.slice(0, 1);
        let trailingDigits = (times(numberString.length - 1, constant(0))).join(',').replace(/,/g, '');
        let roundCustom = (x: number, increment: number) => Math.floor(x / increment) * increment;
        let customThreshold = 5000;
        return numberString.length === 5 ? roundCustom(installNumber, customThreshold) : parseInt(firstDigit + trailingDigits);
    }

};

export {numberRounderService}