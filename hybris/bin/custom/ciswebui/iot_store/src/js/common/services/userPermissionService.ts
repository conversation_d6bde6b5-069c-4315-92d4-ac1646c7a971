import {shopCoreDataProvider} from 'common/provider';
import {LicenseType} from 'common/types';
import {BaseStores} from 'common/constants';
import {i18nService} from 'common/services/i18nService';
import {LicensePurchasability, type ProductLicenseData} from 'common/generated-types/types';

const userPermissionService = {
    isAnonymous() {
        return shopCoreDataProvider.data.userName === null;
    },
    canPurchaseLicense(purchasableLicenseData: ProductLicenseData) {
        return this.isAllowedToBuyLicense(purchasableLicenseData.licenseType.code as LicenseType)
            && purchasableLicenseData.purchasability === LicensePurchasability.PURCHASABLE;
    },
    isAllowedToBuyLicense(licenseType: LicenseType) {
        const allowedToBuyLicenseType = (licenseType === LicenseType.EVALUATION) || (licenseType === LicenseType.TOOL) ||
            this.isAllowedToBuy();

        return !this.isAnonymous() && allowedToBuyLicenseType;
    },
    isAllowedToBuy() {
        return !this.isAnonymous() && shopCoreDataProvider.data.allowedToBuy && shopCoreDataProvider.data.currentCompany.companyApproved;
    },
    isUserOfManagedAccount() {
        return !this.isAnonymous() && shopCoreDataProvider.data.currentCompany.managedAccount;
    },
    hasAccessToCart() {
        return !this.isAnonymous()
            && !shopCoreDataProvider.data.currentCompany.managedAccount
            && !(shopCoreDataProvider.data.moduleConfig?.LANGUAGE_SWITCHER
                && shopCoreDataProvider.data.basestore === BaseStores.AA
                && !i18nService.isUserInCompanyCountry());
    }
};

export {userPermissionService};
