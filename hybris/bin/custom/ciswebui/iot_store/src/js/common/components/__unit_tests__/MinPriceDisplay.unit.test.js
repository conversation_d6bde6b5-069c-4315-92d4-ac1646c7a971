import {wrapperComponentFactory, resizeWidth} from 'common/testtools/unit-test-utils';
import {MinPriceDisplay} from 'common/components/index';
import {commercefacades, PriceFrequency} from "common/generated-types/types";

const $t = jest.fn().mockImplementation((key, params) => {
    if (key === 'shop.products.perYear') {
        return 'p.a.';
    }
    if (key === 'shop.products.price') {
        return 'from EUR 100.00';
    }

    if (key === 'shop.products.priceWithFrequency') {
        return 'from EUR 100.00 p.a.';
    }
});
const mountMinPriceDisplay = (minPrice) => wrapperComponentFactory(MinPriceDisplay,{
    props: {
        minPrice: minPrice,
    },
    mocks: {
        $t
    },
    shallow: false
});

describe('MinPriceDisplay Tester', () => {

    it('minimum price formatted with p.a. for yearly frequency ', () => {
        const minPrice =  {
            currencyIso: "EUR",
            value: 100,
            priceFrequency: PriceFrequency.YEARLY
        };
        const wrapper = mountMinPriceDisplay(minPrice);
        expect(wrapper.vm.formattedPrice).toBe('from EUR 100.00 p.a.');
    });
    it('minimum price format does not use p.a. for non yearly frequency ', () => {
        const minPrice =  {
            currencyIso: "EUR",
            value: 100,
            priceFrequency: PriceFrequency.ONE_TIME
        };
        const wrapper = mountMinPriceDisplay(minPrice);
        expect(wrapper.vm.formattedPrice).toBe('from EUR 100.00');
    });

    it('minimum price empty priceFrequency does not use p.a. for any frequency ', () => {
        const minPrice =  {
            currencyIso: "EUR",
            value: 100,
        };
        const wrapper = mountMinPriceDisplay(minPrice);
        expect(wrapper.vm.formattedPrice).toBe('from EUR 100.00');
    });

});