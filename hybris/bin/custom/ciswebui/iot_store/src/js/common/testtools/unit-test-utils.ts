import {config, createLocalVue, mount, shallowMount} from '@vue/test-utils';
import {i18n} from '../i18n';
import Vue, {type VueConstructor} from 'vue';
import vuetify from '../plugins/brands/azena/vuetify';
import {PiniaVuePlugin} from 'pinia';

// as side effects for all tests..?
import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {createTestingPinia} from '@pinia/testing';

const localVue = createLocalVue();
localVue.use(PiniaVuePlugin);
localVue.use(i18n);

createTestingPinia();

// stop Vue complaining about vuetify and cd-system components in full mount (!shallowMount) tests
Vue.config.ignoredElements = [/^v-/, /^cd-/];

config.stubs.transition = false;

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
const wrapperComponentFactory = (
    Component: VueConstructor,
    // store: Store<State>,
    {
        data = {},
        props = {},
        computed = {},
        slots = {},
        listeners = {},
        shallow = true,
        attachToBody = false,
        mocks = {
            $t: () => {}
        }
    } = {}
    ) => {
    // const localRouter = new VueRouter();
    // localRouter.push = jest.fn();
    // if(store) {
    //     Object.assign(store._vm, mocks);
    // }
    const setup = {
        data() {
            return data;
        },
        propsData: props,
        computed: computed,
        slots,
        listeners,
        // router: localRouter,
        // store,
        i18n,
        localVue,
        vuetify,
        ...{mocks, $keycloak: { token: 'mockToken' }},
        ...(attachToBody && { attachTo: document.body })
    };
    if (shallow) {
        return shallowMount(Component, setup);
    } else {
        return mount(Component, setup);
    }
};

const resizeWidth = (width: number): void => {
    Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: width
    });
    window.dispatchEvent(new Event('resize'));
};

const resizeHeight = (height: number): void => {
    Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: height
    });
    window.dispatchEvent(new Event('resize'));
};

const resizeScreen = (width: number, height: number): void => {
    resizeWidth(width);
    resizeHeight(height);
};

const resetScreenSize = (): void => {
    resizeWidth(1024);
    resizeHeight(768);
};

export {
    localVue,
    wrapperComponentFactory,
    resizeWidth,
    resizeHeight,
    resizeScreen,
    resetScreenSize
};
