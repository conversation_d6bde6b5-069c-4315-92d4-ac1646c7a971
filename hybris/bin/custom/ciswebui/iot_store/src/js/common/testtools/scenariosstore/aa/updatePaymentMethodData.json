{"order": {"paymentAddress": {"id": "8796715679767", "title": null, "titleCode": null, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "companyName": "austria buyer 1", "line1": "Main Street", "line2": "101", "town": "Money-City", "region": null, "district": null, "postalCode": "12345", "phone": "+43 6807541981", "cellphone": null, "email": "<EMAIL>", "country": {"isocode": "AT", "name": "Österreich", "canBuy": false, "blockedCountriesCommercial": null}, "shippingAddress": false, "billingAddress": true, "defaultAddress": false, "visibleInAddressBook": true, "formattedAddress": "Main Street, 101, Money-City, 12345"}, "totalPrice": {"symbol": "EUR", "value": "1.00"}, "totalTax": {"symbol": "EUR", "value": "0.19"}, "totalPriceWithTax": {"symbol": "EUR", "value": "1.19"}, "entries": [{"appCode": "AA_04012050", "productName": "Infoart A Einmalkauf", "productCode": "AA_04012050_full", "productUrl": "/p/AA_04012050", "companyName": "<PERSON>", "versionName": null, "licenseName": "<PERSON><PERSON>", "licenseType": "FULL", "logoUrl": "/shop/images/pcaa_04012050_icon_01_90Wx90H", "smallLogoUrl": "/shop/images/pcaa_04012050_icon_01_44Wx44H", "itemPrice": {"symbol": "EUR", "value": "1.00"}, "totalPrice": {"symbol": "EUR", "value": "1.00"}, "productFuturePrices": [], "quantity": 1, "entryNumber": 0, "scalePrices": []}], "paymentMethod": "CREDIT_CARD", "cartHash": null, "showInvoiceNotes": false, "invoiceNoteSizeLimit": null, "ownAppsPurchase": false, "placedBy": "<PERSON>", "code": "********", "status": "PENDING", "date": *************, "invoices": [], "paymentInfo": {"id": "*************", "paymentMethod": "CREDIT_CARD", "saved": false, "reusable": false, "defaultPaymentInfo": false, "paymentProvider": "DPG", "accountHolderName": "<PERSON>", "cardType": null, "cardTypeData": null, "cardNumber": "424242******4242", "startMonth": null, "startYear": null, "expiryMonth": "12", "expiryYear": "25", "issueNumber": null, "subscriptionId": "createTokenResponseToken", "billingAddress": null, "token": null, "paymentInstrumentId": null}}, "checkoutInfo": [{"paymentInfos": [{"id": "*************", "paymentMethod": "CREDIT_CARD", "saved": false, "reusable": false, "defaultPaymentInfo": false, "paymentProvider": "DPG", "accountHolderName": "<PERSON>", "cardType": null, "cardTypeData": null, "cardNumber": "424242******4242", "startMonth": null, "startYear": null, "expiryMonth": "12", "expiryYear": "25", "issueNumber": null, "subscriptionId": "createTokenResponseToken", "billingAddress": null, "token": null, "paymentInstrumentId": null}], "paymentProvider": "DPG", "paymentMethod": "CREDIT_CARD", "userActionParameters": {"sessionConfig": "some client configuration", "paymentId": "1234567", "sessionId": "zK|wf$sq_n.9/jO+)XzS|fO", "dpgjsUrl": "http://127.0.0.1:8091/dpgjs/js"}, "userCreatable": true, "savableForReuse": true}]}