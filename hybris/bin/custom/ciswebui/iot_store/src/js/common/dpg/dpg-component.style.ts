import {Styles} from "common/dpg/dpg.model";

const COMPONENT_STYLE: Styles = {
    "input": {
        "font-size": "16px",
        "color": "#444444",
        "font-family": "monospace"
    },
    ".ifs-valid": {
        "color": "Green"
    },
    ".ifs-invalid": {
        "color": "Crimson"
    },
    ".ifs-not-accepted": {
        "color": "DarkGoldenRod"
    }
}

export const COMPONENT_OPTIONS = {
    style: COMPONENT_STYLE
}