[{"id": "aa_globalHelpSupport", "url": "https://support.securityandsafetythings.com/hc", "type": "GLOBAL", "group": "HELP", "text": "Besuchen Sie Hilfe und Support", "description": "", "target": "_blank", "icon": "", "index": 0, "entryPage": "", "itemCode": "globalHelpSupport"}, {"id": "aa_globalImprint", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/imprint", "type": "GLOBAL", "group": "FOOTER", "text": "Impressum", "description": "", "target": "_blank", "icon": "", "index": 1, "entryPage": "", "itemCode": "globalImprint"}, {"id": "aa_globalHelpContact", "url": "https://support.securityandsafetythings.com/hc/requests/new", "type": "GLOBAL", "group": "HELP", "text": "Kontaktieren Sie Uns", "description": "", "target": "_blank", "icon": "", "index": 1, "entryPage": "", "itemCode": "globalHelpContact"}, {"id": "aa_storePrivacyPolicy", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/privacy-policy", "type": "STORE", "group": "FOOTER", "text": "Datenschutz", "description": "", "target": "_blank", "icon": "", "index": 2, "entryPage": "", "itemCode": "storePrivacyPolicy"}, {"id": "aa_globalLegal", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/legal-notice", "type": "GLOBAL", "group": "FOOTER", "text": "Rechtliche Grundlagen", "description": "", "target": "_blank", "icon": "", "index": 3, "entryPage": "", "itemCode": "globalLegal"}, {"id": "aa_storeTermsAndConditions", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/terms", "type": "STORE", "group": "FOOTER", "text": "Nutzungsbedingungen", "description": "", "target": "_blank", "icon": "", "index": 4, "entryPage": "", "itemCode": "storeTermsAndConditions"}, {"id": "aa_storeInfringement", "url": "https://legal.boschmarketplace.com/boschaftermarket/at/de/report-infringement", "type": "STORE", "group": "FOOTER", "text": "<PERSON>ers<PERSON>ß <PERSON>", "description": "", "target": "_blank", "icon": "", "index": 5, "entryPage": "", "itemCode": "storeInfrigment"}, {"id": "aa_globalMyProfile", "url": "http://localhost:8091/page/myprofile", "type": "GLOBAL", "group": "HEADER", "text": "<PERSON><PERSON>", "description": "", "target": "_blank", "icon": "user", "index": 1, "entryPage": "", "itemCode": "globalMyProfile"}, {"id": "aa_globalSupport", "url": "https://support.securityandsafetythings.com/hc", "type": "GLOBAL", "group": "HEADER", "text": "Unterstützung", "description": "", "target": "_blank", "icon": "support", "index": 5, "entryPage": "", "itemCode": "globalSupport"}, {"id": "aa_storeRegionAndLanguage", "url": "/shop", "type": "STORE", "group": "HEADER", "text": "Region & Sprache", "description": "", "target": "", "icon": "$world", "index": 6, "entryPage": "", "itemCode": "regionAndLanguage"}, {"id": "aa_storeSignOut", "url": "/shop/logout", "type": "STORE", "group": "HEADER", "text": "Ausloggen", "description": "", "target": "", "icon": "$logout", "index": 7, "entryPage": "", "itemCode": "storeSignOut"}, {"id": "aa_getInTouch", "url": "https://www.boschaftermarket.com/de/de/esitronic-kontaktformular/", "type": "GLOBAL", "group": "HELP", "text": "Kontakt", "description": "", "target": "_blank", "icon": "", "index": 8, "entryPage": "", "itemCode": "getInTouch"}, {"id": "aa_deviceManagement", "url": "http://localhost:8091/page/cameras", "type": "STORE", "group": "HOME_SWITCHER", "text": "Lizenzmanagement", "description": "", "target": "_blank", "icon": "", "index": 10, "entryPage": "", "itemCode": "deviceManagement"}, {"id": "aa_marketplaceComponent", "url": "/shop", "type": "STORE", "group": "COMPONENTS", "text": null, "description": null, "target": null, "icon": "$store", "index": 1, "entryPage": null, "itemCode": "marketplaceComponent"}, {"id": "aa_licenseManagementComponent", "url": "http://localhost:8091/page/cameras", "type": "STORE", "group": "COMPONENTS", "text": null, "description": null, "target": null, "icon": "$licenses", "index": 2, "entryPage": null, "itemCode": "licenseManagementComponent"}, {"id": "aa_accountComponent", "url": "http://localhost:8091/page/myprofile", "type": "STORE", "group": "COMPONENTS", "text": null, "description": null, "target": null, "icon": "$user", "index": 3, "entryPage": null, "itemCode": "accountComponent"}, {"id": "aa_storeOrderHistory", "url": "/shop/my-account/orders", "type": "STORE", "group": "HEADER", "text": "Order History", "description": null, "target": null, "icon": "$sheet", "index": 3, "entryPage": null, "itemCode": "storeOrderHistory"}, {"id": "aa_storePaymentDetails", "url": "/shop/my-account/payment-details", "type": "STORE", "group": "HEADER", "text": "Payment Details", "description": null, "target": null, "icon": "$payment", "index": 4, "entryPage": null, "itemCode": "storePaymentDetails"}]