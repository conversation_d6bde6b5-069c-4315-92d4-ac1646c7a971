import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import ConfirmationDialog from 'common/components/popups/ConfirmationDialog';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

new Vue({
    el: '#vue-app',
    data: {
        result: '',
        showConfirm: false,
    },
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        ConfirmationDialog
    },
    methods: {
        onClose() {
            this.showConfirm = false;
            this.result = 'cancel';
        },
        onConfirm() {
            this.result = 'ok';
            this.showConfirm = false;
        }
    },
    template: `
      <test-page-root>
        <div class="open-popup" v-on:click="showConfirm = true">open popup</div>
        <h2>Result: {{result}}</h2>

        <confirmation-dialog v-if="showConfirm"
                             v-bind:confirmation="{
                                                    header: 'myHeaderString',
                                                    bodyLines: ['myBodyString', 'myBodyString second line']
                                                  }"
                             v-on:cancel="onClose"
                             v-on:confirm="onConfirm">
        </confirmation-dialog>
      </test-page-root>
    `
});
