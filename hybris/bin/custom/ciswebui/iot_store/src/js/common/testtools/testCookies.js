import coreData from "./scenariosstore/aa/coreDataFromRest.json";

export const languageCookie = function(language) {
    return {
        'domain': new URL(global.__DEV_ADDRESS__).hostname,
        'expirationDate': 3000000000,
        'hostOnly': false,
        'httpOnly': false,
        'name': 'st_selected_lang',
        'path': '/',
        'sameSite': 'no_restriction',
        'secure': false,
        'session': false,
        'storeId': '0',
        'value': language
    };
};

export const countryCookie = function(country) {
    return {
        'domain': new URL(global.__DEV_ADDRESS__).hostname,
        'expirationDate': 3000000000,
        'hostOnly': false,
        'httpOnly': false,
        'name': 'st_selected_country',
        'path': '/',
        'sameSite': 'no_restriction',
        'secure': false,
        'session': false,
        'storeId': '0',
        'value': country
    };
};

export const coreDataRestHandler = (request) => {
    if (request.url().endsWith('/shop/api/coredata')) {
        request.respond({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(coreData)
        });
    } else {
        request.continue();
    }
};

export const cookies_en_AT = [languageCookie('en'), countryCookie('AT')];
export const cookies_de_DE = [languageCookie('de'), countryCookie('DE')];
