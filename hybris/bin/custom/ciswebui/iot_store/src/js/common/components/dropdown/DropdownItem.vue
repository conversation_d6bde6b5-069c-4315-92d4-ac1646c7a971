<template>
    <span class="item-container" v-on:click="dispatchSelected">
        <div>{{item.label}}</div>
        <div class="icon" v-if="selected && selected.id === item.id">
          <CDIcon dense color="primary">$complete</CDIcon>
        </div>
    </span>

</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {Item} from 'common/components/dropdown/Dropdown';

    @Component
    export default class DropdownItem extends Vue {
        @Prop() readonly item!: Item;

        dispatchSelected(): void {
            this.parent.$emit( 'option-selected', this.item);
        }

        get selected(): Item|undefined {
            return this.parent.selected && !!this.parent.selected.id ? this.parent.selected : undefined;
        }

        get parent(): Vue & {selected: Item | undefined} {
            // TSC gives an errors: property 'selected' does not exist on type Vue,
            //                      property '$emit' does not exist on type 'never'.
            // A workaround would be to make an extension type for this property.
            type VueExt = Vue & {selected: Item | undefined};
            return this.$parent as VueExt;
        }
    }
</script>

<style lang="scss" scoped>
    .item-container {
        margin-bottom: 15px;
        white-space: nowrap;

        display: flex;
        justify-content: space-between;
        align-content: center;
    }

    .icon {
        margin-left: 20px;
        svg {
            margin-top: 2px;
        }
    }

    .item-container:hover {
        cursor: pointer;
        color: var(--v-primary-base);
    }
</style>
