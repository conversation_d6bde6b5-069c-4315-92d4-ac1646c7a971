<template>
    <div class="permission-info">
        <slot name="icon">
            <info-icon></info-icon>
        </slot>
        <slot name="content">
        </slot>
    </div>
</template>

<script lang="ts">
    import { Component, Vue } from 'vue-property-decorator';
    import InfoIcon from 'common/images/boxed/icon-24-info-d.svg';
    @Component({
        components: {
            InfoIcon
        }
    })
    export default class PermissionText extends Vue {}
</script>

<style lang="scss" scoped>
    @import "shop/core/constants";

    .permission-info {
        font-size: 12px;
        display: flex;
        margin-bottom: $spacing-s;
        svg {
            margin-right: $spacing-s;
        }
        p {
            font-weight: 600;
            color: var(--v-info-base);
        }
    }
</style>
