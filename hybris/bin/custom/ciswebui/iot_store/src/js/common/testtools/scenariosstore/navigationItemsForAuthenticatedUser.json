[{"id": "globalHelpSupport", "url": "https://support.azena.com/hc", "type": "GLOBAL", "group": "HELP", "text": "Visit Help & Support", "description": null, "target": "_blank", "icon": null, "index": 0, "entryPage": null, "itemCode": "globalHelpSupport"}, {"id": "globalMyProfile", "url": "http://localhost:8091/page/myprofile", "type": "GLOBAL", "group": "HEADER", "text": "My Profile", "description": null, "target": "_blank", "icon": "user", "index": 1, "entryPage": null, "itemCode": "globalMyProfile"}, {"id": "storeTermsAndConditions", "url": "https://www.azena.com/terms", "type": "STORE", "group": "FOOTER", "text": "Terms and Conditions", "description": null, "target": "_blank", "icon": null, "index": 1, "entryPage": null, "itemCode": "storeTermsAndConditions"}, {"id": "globalHelpContact", "url": "https://support.azena.com/hc/requests/new", "type": "GLOBAL", "group": "HELP", "text": "Contact Us", "description": null, "target": "_blank", "icon": null, "index": 1, "entryPage": null, "itemCode": "globalHelpContact"}, {"id": "appProductCategory", "url": "/shop", "type": "STORE", "group": "PRODUCT_CATEGORIES", "text": "Apps", "description": null, "target": null, "icon": null, "index": 1, "entryPage": null, "itemCode": "appProductCategory"}, {"id": "storeHelpStoreDocs", "url": "https://support.azena.com/hc/sections/360011381714-Application-Store", "type": "STORE", "group": "HELP", "text": "Application Store help", "description": "Find help articles about Application Store.", "target": "_blank", "icon": "/sample-data/help-and-resources/store-docs.svg", "index": 1, "entryPage": null, "itemCode": "storeHelpStoreDocs"}, {"id": "globalMyCompany", "url": "http://localhost:8091/page/mycompany", "type": "GLOBAL", "group": "HEADER", "text": "My Company", "description": null, "target": "_blank", "icon": "company", "index": 2, "entryPage": null, "itemCode": "globalMyCompany"}, {"id": "storePrivacyPolicy", "url": "https://www.azena.com/privacy-policy", "type": "STORE", "group": "FOOTER", "text": "Privacy Policy", "description": null, "target": "_blank", "icon": null, "index": 2, "entryPage": null, "itemCode": "storePrivacyPolicy"}, {"id": "toolProductCategory", "url": "/shop/tools", "type": "STORE", "group": "PRODUCT_CATEGORIES", "text": "<PERSON><PERSON><PERSON>", "description": null, "target": null, "icon": null, "index": 2, "entryPage": null, "itemCode": "toolProductCategory"}, {"id": "storeHelpAzenaHome", "url": "https://www.azena.com", "type": "STORE", "group": "HELP", "text": "Azena home", "description": "Visit our website to learn more about <PERSON>zen<PERSON> and our offering.", "target": "_blank", "icon": "/sample-data/help-and-resources/azena-home.svg", "index": 2, "entryPage": null, "itemCode": "storeHelpAzenaHome"}, {"id": "storeOrderHistory", "url": "/shop/my-account/orders", "type": "STORE", "group": "HEADER", "text": "Order History", "description": null, "target": null, "icon": "$sheet", "index": 3, "entryPage": null, "itemCode": "storeOrderHistory"}, {"id": "globalLegal", "url": "https://www.azena.com/legal-notice", "type": "GLOBAL", "group": "FOOTER", "text": "Legal Note", "description": null, "target": "_blank", "icon": null, "index": 3, "entryPage": null, "itemCode": "globalLegal"}, {"id": "storeHelpPlatformOverview", "url": "https://www.azena.com/our-platform", "type": "STORE", "group": "HELP", "text": "Our platform", "description": "Get an overview of available tools and products.", "target": "_blank", "icon": "/sample-data/help-and-resources/our-platform.svg", "index": 3, "entryPage": null, "itemCode": "storeHelpPlatformOverview"}, {"id": "storePaymentDetails", "url": "/shop/my-account/payment-details", "type": "STORE", "group": "HEADER", "text": "Payment Details", "description": null, "target": null, "icon": "$payment", "index": 4, "entryPage": null, "itemCode": "storePaymentDetails"}, {"id": "storeHelpCompatibleCameras", "url": "https://www.azena.com/cameras", "type": "STORE", "group": "HELP", "text": "Compatible cameras", "description": "Browse and buy cameras with Azena OS.", "target": "_blank", "icon": "/sample-data/help-and-resources/compatible-cameras.svg", "index": 4, "entryPage": null, "itemCode": "storeHelpCompatibleCameras"}, {"id": "globalSupport", "url": "https://support.azena.com/hc", "type": "GLOBAL", "group": "HEADER", "text": "Support", "description": null, "target": "_blank", "icon": "support", "index": 5, "entryPage": null, "itemCode": "globalSupport"}, {"id": "globalImprint", "url": "https://www.azena.com/imprint", "type": "GLOBAL", "group": "FOOTER", "text": "Corporate Information", "description": null, "target": "_blank", "icon": null, "index": 5, "entryPage": null, "itemCode": "globalImprint"}, {"id": "storeHelpAndResources", "url": "/shop/help-resources", "type": "STORE", "group": "HEADER", "text": "Help & Resources", "description": null, "target": null, "icon": "$help", "index": 5, "entryPage": null, "itemCode": "storeHelpAndResources"}, {"id": "storeSignOut", "url": "/shop/logout", "type": "STORE", "group": "HEADER", "text": "Sign Out", "description": null, "target": null, "icon": "$logout", "index": 6, "entryPage": null, "itemCode": "storeSignOut"}, {"id": "storeInfrigment", "url": "https://www.azena.com/contact", "type": "STORE", "group": "FOOTER", "text": "Report Infringement", "description": null, "target": "_blank", "icon": null, "index": 7, "entryPage": null, "itemCode": "storeInfrigment"}, {"id": "deviceManagement", "url": "http://localhost:8091/page/cameras", "type": "STORE", "group": "HOME_SWITCHER", "text": "Device Management Portal", "description": null, "target": "_blank", "icon": null, "index": 10, "entryPage": null, "itemCode": "deviceManagement"}]