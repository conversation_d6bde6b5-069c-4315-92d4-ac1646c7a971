import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';
import {useStoreHeaderStore} from 'aa/shop/store/storeHeader';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import coreDataDefault from 'common/testtools/scenariosstore/aa/coreDataDefault.json';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import CategoriesGroups from 'aa/shop/productsoverview/components/CategoriesGroups.vue';
import categories from 'common/testtools/scenariosstore/aa/categories.json';
import {useCoreDataStore} from 'shop/store/coreData';
import coreData from 'common/testtools/scenariosstore/aa/coreDataFromRest.json';

Vue.use(VueI18n);
Vue.use(pinia);

window.frontendData.coreData = coreDataDefault;

const storeHeaderStore = useStoreHeaderStore();
storeHeaderStore.isActive = false;

const storeRootStore = useStoreRootStore();
storeRootStore.coreData = coreDataDefault;

const coreDataStore = useCoreDataStore();
coreDataStore.coreData = coreData;

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    pinia,
    components: {
        CategoriesGroups,
        TestPageRoot
    },
    data: {
        rootCategoryHierarchy: categories
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <categories-groups
              :root-category-hierarchy="rootCategoryHierarchy">
          </categories-groups>
        </div>
      </test-page-root>
    `
});
