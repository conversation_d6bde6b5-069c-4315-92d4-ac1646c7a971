<template>
  <StoreBanner data-id="container-purchase-disabled-banner">
    {{ $t('shop.purchaseDisabledBanner.text', { date: formattedDate }) }}
  </StoreBanner>
</template>

<script setup lang="ts">
    import StoreBanner from 'aa/shop/core/components/StoreBanner.vue';
    import {computed} from 'vue';
    import {shopCoreDataProvider} from 'common/provider';
    import {useI18n} from 'vue-i18n-bridge';
    import {i18nService} from 'common/services';

    const { d } = useI18n(); // Use the i18n instance to $d

    // Computed property to format the date
    const formattedDate = computed(() => {
    	const date = shopCoreDataProvider.data?.purchaseDisabledBannerInfo?.date ?? null;
    	if (!date) return '';
    	return d(new Date(date), {dateStyle: 'long'}, i18nService.getLocaleForFormats());
    });

</script>
