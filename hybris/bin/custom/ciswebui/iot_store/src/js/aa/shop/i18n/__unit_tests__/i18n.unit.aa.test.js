import 'common/testtools/unit_tests_mock'
import 'common/test-directive'
import { i18n, loadLanguageAsync } from 'aa/shop/i18n'
import { i18nService } from 'common/services'
import { cloneDeep } from 'lodash'

jest.mock('common/services/i18nService')

const defaultCoreData = {
  translationConfig: {
    baseUrl: 'remote_url',
    projectId: 'project_id',
    cdsProjectId: 'cds_project_id',
    remoteTranslationsEnabled: true,
  }
}

describe('AA i18n', () => {

  beforeEach(() => {
    i18n.locale = 'de'
  })

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('remote translation files', () => {
    let coreData = {};

    beforeEach(() => {
      coreData = cloneDeep(defaultCoreData);
      window.frontendData.coreData = coreData
    })

    it('are fetched when remote translation is enabled', async () => {
      await loadLanguageAsync()

      expect(i18nService.fetchTranslation).toHaveBeenCalledWith('de', 'remote_url', 'project_id')
      expect(i18nService.fetchTranslation).toHaveBeenCalledWith('de', 'remote_url', 'cds_project_id')
    })

    it('are not fetched when remote translation is disabled', async () => {
      coreData.translationConfig.remoteTranslationsEnabled = false

      await loadLanguageAsync()

      expect(i18nService.fetchTranslation).not.toHaveBeenCalled()
    })

    it('are merged to the i18n locale messages ', async () => {
      const storeTranslations = { 'store_key': 'store_value' }
      const cdsTranslations = { 'cds_key': 'cds_value' }
      i18nService.fetchTranslation.mockImplementation(async (locale, baseUrl, projectId) => {
        if (projectId === 'project_id') {
          return storeTranslations
        } else if (projectId === 'cds_project_id') {
          return cdsTranslations
        }
      })

      await loadLanguageAsync()

      expect(i18n.t('navigation.storeTitle')).toBe('Marketplace')
      // eslint-disable-next-line @intlify/vue-i18n/no-missing-keys -- translation can't be found by ESLint as it is defined in this test
      expect(i18n.t('store_key')).toBe('store_value')
      // eslint-disable-next-line @intlify/vue-i18n/no-missing-keys -- translation can't be found by ESLint as it is defined in this test
      expect(i18n.t('$vuetify.cds_key')).toBe('cds_value')
    })

  })
})
