import {categoriesService} from '../categoriesService';
import categories from 'common/testtools/scenariosstore/aa/categories.json';
import {cloneDeep} from 'lodash';

describe('Categories Service', () => {
    describe('sortCategories', () => {
        let categoriesData = {};
        beforeEach(() => {
            categoriesData = cloneDeep(categories.subcategories);
        });

        it('given categories with order then sort by order', () => {
            expect(categoriesService.sortCategories(categoriesData)).toEqual(
                expect.arrayContaining([
                    expect.objectContaining(
                        {
                            code: 'cat_1',
                            order: 1
                        }
                    ),
                    expect.objectContaining(
                        {
                            code: 'cat_2',
                            order: 2
                        }
                    ),
                    expect.objectContaining(
                        {
                            code: 'cat_3',
                            order: 3
                        }
                    ),
                    expect.objectContaining(
                        {
                            code: 'cat_4',
                            order: 4
                        }
                    )
                ])
            );
        });
    });

    describe('containsProducts', () => {

        const emptyCategory = {
            products: [],
            subcategories: []
        };

        const emptySubcategory = {
            products: [],
            subcategories: [{
                products: [],
                subcategories: []
            }]
        };

        const productInCategory = {
            products: [{}],
            subcategories: []
        };

        const productInSubcategory = {
            products: [],
            subcategories: [{
                products: [{}]
            }]
        };

        it.each`
            name                        | category                | result
            ${'empty object'}           | ${{}}                   | ${false}
            ${'empty category'}         | ${emptyCategory}        | ${false}
            ${'empty subcategory'}      | ${emptySubcategory}     | ${false}
            ${'product in category'}    | ${productInCategory}    | ${true}
            ${'product in subcategory'} | ${productInSubcategory} | ${true}
        `('returns \'$result\' for scenario \'$name\'', async ({name, category, result}) => {
            expect(categoriesService.containsProducts(category)).toBe(result);
        });
    });

    describe('filterEmptyCategories', () => {

        const emptyCategory = [{
            products: [],
            subcategories: []
        }];

        const productInCategory = [{
            products: [{}],
            subcategories: []
        }];

        const productInSubcategory = [{
            products: [],
            subcategories: [{
                products: [{}]
            }]
        }];

        it.each`
            name                        | category                | result
            ${'empty array'}            | ${[]}                   | ${[]}
            ${'empty category'}         | ${emptyCategory}        | ${[]}
            ${'product in category'}    | ${productInCategory}    | ${productInCategory}
            ${'product in subcategory'} | ${productInSubcategory} | ${productInSubcategory}
        `('returns expected result for scenario \'$name\'', async ({name, category, result}) => {
            expect(categoriesService.filterEmptyCategories(category)).toEqual(result);
        });

        it('returns expected result for scenario \'test data\'', async () => {
            const categoryData = cloneDeep(categories.subcategories);
            expect(categoriesService.filterEmptyCategories(categoryData)).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({ code: 'cat_1' }),
                    expect.objectContaining({ code: 'cat_2' }),
                    expect.objectContaining({ code: 'cat_3' }),
                    expect.objectContaining({ code: 'cat_4' })
                ])
            );
        });
    });
});
