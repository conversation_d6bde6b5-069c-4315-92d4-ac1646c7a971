<template>
  <div>
    <template v-if="showComparisonDialog">
      <CDButton large
                block
                @click="showDialog = true"
                color="primary"
                class="my-8 my-md-16"
                data-id="button-comparison-dialog-activator">
        <CDIcon class="mr-2" icon="$doublesquares"></CDIcon>
        {{ $t('shop.productsOverview.category.packetComparisonDialog.activator', { name: categoryName }) }}
      </CDButton>
    </template>
    <div class="my-n4" :data-id="`container-product-list-${category.code}`">
      <div v-for="(product, index) in category.products" :key="`container-product-${product.code}`">
        <category-product-details :product="product"
                                  :hide-chips="hideChips"
                                  :thl-app-code-in-cart="storeRootStore.thlAppCodeInCart"
                                  :has-new-info-on-app="storeRootStore.isNewInfoForAppEnabled"
                                  :hide-prices="storeRootStore.isManagedAccount">
        </category-product-details>
        <v-divider v-if="index < category.products.length - 1">
        </v-divider>
      </div>
      <div v-if="showComparisonDialog">
        <PacketComparisonDialog
            v-model="showDialog"
            @close="showDialog = false"
            :bills-of-materials="billsOfMaterials"
            :products="orderedProducts">
        </PacketComparisonDialog>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryHierarchyDTO, MaterialDTO, ProductDTO} from 'common/generated-types/types';
import CategoryProductDetails from 'aa/shop/productsoverview/components/categorydetails/CategoryProductDetails.vue';
import PacketComparisonDialog from 'aa/shop/productsoverview/components/PacketComparisonDialog.vue';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import {sortBy, uniqBy} from 'lodash';

@Component({
  components: {PacketComparisonDialog, CategoryProductDetails}
})
export default class CategoryProductList extends Vue {
  @Prop() category!: CategoryHierarchyDTO;
  @Prop({default: false}) hideChips?: boolean;

  storeRootStore = useStoreRootStore();

  get showComparisonDialog(): boolean {
    return this.billsOfMaterials.length > 0;
  }

  showDialog = false;

  get billsOfMaterials(): MaterialDTO[] {
    const materials = this.category.products.flatMap(
        product => product.billsOfMaterials.flatMap(material => material));

    return uniqBy(materials, material => material.code);
  }

  get orderedProducts(): ProductDTO[] {
    return sortBy(this.category.products, 'order');
  }

  get categoryName(): string {
    return this.category.name;
  }

}
</script>
