<template>
  <v-sheet class="product-selection-summary" data-id="container-product-selection-summary">
    <v-container :fluid="$vuetify.breakpoint.lgAndDown"
                 class="pa-4">
      <v-row>
        <v-col cols="12"
               md>
          <v-expansion-panels flat>
            <v-expansion-panel>
              <v-expansion-panel-header class="pa-0"
                                        data-id="product-selection-summary-header">
                <span class="cd-text-large font-weight-bold flex-grow-0">
                  {{ $t('shop.productSelection.summary.summary') }}
                </span>

                <template v-slot:actions>
                  <CDIcon color="grey darken-3">$expand</CDIcon>
                </template>

                <div v-if="productSelectionStore.sum"
                     class="flex-grow-0"
                     style="order: 1">
                  <span class="cd-text-large font-weight-bold">
                    <v-tooltip top
                               v-if="productSelectionStore.consent"
                               max-width="400">
                      <template v-slot:activator="{ on, attrs }">
                        <CDIcon icon="$statusinfo"
                                v-bind="attrs"
                                v-on="on"
                                color="primary"
                                data-id="product-selection-summary-price"
                                v-on:click.stop>
                        </CDIcon>
                      </template>
                      <span>
                        {{
                          $t(
                              'shop.productSelection.summary.preliminarySums',
                              {
                                productName: productSelectionStore.appName,
                                bundleProductName: productSelectionStore.product?.addonProductName,
                                discountPercentage: productSelectionStore.addonDiscount
                              }
                            )
                        }}
                      </span>
                    </v-tooltip>
                    <span style="vertical-align: middle" :class="productSelectionStore.consent ? ['original-price', 'mr-4'] : ''">
                      {{
                        $t(
                            'shop.productSelection.summary.price',
                            {
                              currency: productSelectionStore.sum.currencyIsocode,
                              price: $n(originalPrice, 'price', localeForFormats)
                            }
                        )
                      }}
                    </span>
                    <span v-if="productSelectionStore.consent" style="vertical-align: middle">
                      {{
                        $t(
                            'shop.productSelection.summary.price',
                            {
                              currency: discountedPrice?.currencyIsocode,
                              price: $n(discountedPrice?.amount + discountedThlPrice, 'price', localeForFormats)
                            }
                        )
                      }}
                    </span>
                  </span>
                </div>
              </v-expansion-panel-header>

              <v-expansion-panel-content>
                <CDNotificationPersistent v-if="nothingSelected"
                                          type="info"
                                          class="mt-4 mb-0">
                  {{ $t('shop.productSelection.summary.nothingSelectedInfo') }}
                </CDNotificationPersistent>

                <product-selection-summary-item v-for="item in productSelectionStore.selection"
                                                :key="item.code"
                                                :product="productSelectionStore.product"
                                                :variant="item"
                                                :quantity="item.quantity"
                                                class="mt-4">
                </product-selection-summary-item>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-col>

        <v-col cols="12"
               md="auto"
               class="d-flex">
          <CDButton large
                    color="primary"
                    class="flex-grow-1"
                    :disabled="nothingSelected"
                    v-on:click="addToCart"
                    data-id="product-selection-summary-addtocart">
            {{ $t('shop.productSelection.summary.addToCart') }}
          </CDButton>
        </v-col>
      </v-row>
    </v-container>
  </v-sheet>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {useProductSelectionStore} from 'shop/store/productSelection';
import ProductSelectionSummaryItem from 'aa/shop/productselection/components/ProductSelectionSummaryItem.vue';
import {pageSpinner} from 'common/components/spinner';
import {ShopRoute} from 'common/constants';
import {i18nService, messageService} from 'common/services';
import ThlNotification from 'aa/shop/thl/components/ThlNotification.vue';
import {DiscountProductType} from 'common/types';

@Component({components: {ThlNotification, ProductSelectionSummaryItem}})
export default class ProductSelectionSummary extends Vue {

  productSelectionStore = useProductSelectionStore();
  localeForFormats = '';

  mounted(): void {
    this.localeForFormats = i18nService.getLocaleForFormats();
  }
  get nothingSelected(): boolean {
    return this.productSelectionStore.selection.length <= 0;
  }
  get i18nService() {
    return i18nService;
  }
  get originalPrice(): number {
    const amount = this.productSelectionStore?.sum?.amount ?? 0;
    if (this.productSelectionStore?.consent) {
      return amount + this.productSelectionStore.getOriginalThlPrice(DiscountProductType.THL);
    } else {
      return amount;
    }
  }
  get discountedPrice(): { currencyIsocode: string; amount: number } | undefined {
    return this.productSelectionStore.getDiscountedSum();
  }
  get discountedThlPrice(): number {
    return this.productSelectionStore.getDiscountedPrice(DiscountProductType.THL);
  }
  addToCart(): void {
    pageSpinner.start(0);
    this.productSelectionStore.addToCart()
        .then(() => {
          messageService.success(this.$t('shop.productDetails.pricingSection.addToCartSuccess') as string, true, true);
          window.location.assign(ShopRoute.CART);
        })
        .catch((error) => {
          messageService.clearAll();

          const statusCode = error.response.status;

          if (statusCode === 400) {
            messageService.errorResponse(error.response.data, this.$i18n, true);
            window.location.assign(ShopRoute.CART);
          } else if (statusCode === 422) {
            messageService.errorResponse(error.response.data, this.$i18n);
          } else {
            messageService.error(this.$t('shop.productDetails.pricingSection.addToCartGeneralError') as string);
          }
        })
        .finally(() => {
          pageSpinner.stop();
        });
  }
}
</script>

<style lang="scss" scoped>
.product-selection-summary {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid var(--v-grey-lighten1);
  z-index: 1;

  .original-price {
    color: var(--v-grey-darken2);
    text-decoration: line-through;
  }
}

.v-expansion-panel {
  background-color: var(--v-transparent) !important;
}

.v-expansion-panel-header {
  min-height: 48px;
  flex-wrap: wrap;
  gap: 8px;
}

::v-deep .v-expansion-panel-header__icon {
  flex-grow: 1;
}

::v-deep .v-expansion-panel-content__wrap {
  padding: 0;
}
</style>
