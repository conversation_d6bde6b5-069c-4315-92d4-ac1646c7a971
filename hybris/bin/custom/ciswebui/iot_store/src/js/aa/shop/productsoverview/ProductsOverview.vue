<template>
  <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="mb-16 mt-8">
    <account-notification :userCompanyUnderReview="coreData.userCompanyUnderReview"></account-notification>
    <products-overview-header></products-overview-header>
    <categories-groups v-if="dataLoaded" :root-category-hierarchy="rootCategoryHierarchy"></categories-groups>
  </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryHierarchyDTO, ShopCoreData} from 'common/generated-types/types';
import {categoryHierarchyResource} from 'shop/resources';
import {messageService} from 'common/services';
import ProductsOverviewHeader from 'aa/shop/productsoverview/components/ProductsOverviewHeader.vue';
import CategoryDetails from 'aa/shop/productsoverview/components/categorydetails/CategoryDetails.vue';
import CategoriesGroups from 'aa/shop/productsoverview/components/CategoriesGroups.vue';
import AccountNotification from 'aa/shop/account/AccountNotification.vue';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';

/**
 * The Categories hierarchy is represented as follows:
 *
 * Root category: By convention the Root category is always named "main" and is not displayed
 * -> root : "main"
 *       Category groups: The Group does not have any products, and has at most one sub category.
 * ----> cat_1: ECU diagnosis
 *           Categories: Categories at this level can have products and subcategories
 * --------> cat_101: ESI[tronic] 2.0
 * ----> cat_2: Vehicle system analysis
 * --------> cat_201: CompacSoft[plus]
 * ----> cat_3: Emission Analysis
 * -------> cat_301: CompacSoft AU
 * ----> cat_4: Diesel system test
 * -------> cat_401: CRI/CRIN
 */
@Component({
  components: {AccountNotification, CategoriesGroups, CategoryDetails, ProductsOverviewHeader}
})
export default class ProductsOverview extends Vue {
  @Prop() coreData!: ShopCoreData;

  storeRootStore = useStoreRootStore();
  rootCategoryHierarchy = {} as CategoryHierarchyDTO;
  dataLoaded = false;

  created(): void {
    this.setPageTitle();
  }

  async mounted(): Promise<void> {
    this.storeRootStore.setCoreData<ShopCoreData>(this.coreData);
    await this.loadRootCategoryHierarchy();
  }

  loadRootCategoryHierarchy(): Promise<void> {
    return categoryHierarchyResource.getRootCategoryHierarchy().then(response => {
      this.rootCategoryHierarchy = response.data;
      this.dataLoaded = true;
    }).catch(error => {
      messageService.errorResponse(error.response?.data, this.$i18n);
    });
  }

  private setPageTitle(): void {
    document.title = this.$t('shop.productsOverview.pageTitle') as string + ' – ' + this.$t('navigation.storePageTitle') as string;
  }
};
</script>
