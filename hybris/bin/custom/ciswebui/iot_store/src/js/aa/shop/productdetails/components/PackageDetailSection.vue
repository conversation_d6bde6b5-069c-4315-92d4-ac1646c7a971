<template>
  <div class="package-detail">
    <v-container :fluid="true" class="pa-0">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <v-row>
          <v-col cols="12">
            <h2 class="mb-1 mt-0">{{$t('shop.productDetails.packageDetailsSection.title')}}</h2>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" lg="8">
            <CDAccordion :accordion-items="packageDetails"></CDAccordion>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <h2 class="mb-1 mt-4">{{$t('shop.productDetails.packageDetailsSection.addons')}}</h2>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" lg="8">
            <CDAccordion :accordion-items="relatedAddons"></CDAccordion>
          </v-col>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AccordionData} from 'common/types';
import {BoltonData, MaterialData} from 'common/generated-types/types';
import {isNil} from 'lodash';

@Component
export default class PackageDetailSection extends Vue {
  @Prop() billOfMaterials!: MaterialData[];
  @Prop() boltons!: BoltonData[];

  get packageDetails(): AccordionData[] {
    return this.billOfMaterials.map((material: MaterialData) => {
      const packageDetail: AccordionData = {
        title: material.name,
        content: material.description,
      };
      if (!isNil(material.videoUrl)) {
        packageDetail.contentUrl = {
          label: this.$t('shop.productDetails.packageDetailsSection.video') as string,
          url: material.videoUrl
        };
      }
      return packageDetail;
    });
  }

  get relatedAddons(): AccordionData[] {
    return this.boltons.map((bolton: BoltonData) => {
      return {
        title: bolton.name,
        content: bolton.description
      } as AccordionData;
    });
  }

}
</script>
