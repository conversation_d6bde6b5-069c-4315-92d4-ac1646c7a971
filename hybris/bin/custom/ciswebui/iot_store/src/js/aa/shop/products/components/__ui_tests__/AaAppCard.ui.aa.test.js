import * as Sizes from 'common/testtools/testScreenSizes';
import {cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";

describe('AaAppCard', () => {

    beforeEach(async () => {
        await page.setRequestInterception(true);
        page.on('request', coreDataRestHandler);
        await page.setCookie(...cookies_en_AT);
    });

    it('component looks as expected', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-products-components-__ui_tests__-AaAppCard');

        await Sizes.testAllScreenSizes(1000);
    });
});
