import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import coreDataDefault from 'common/testtools/scenariosstore/aa/coreDataDefault.json';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import CategoriesExpansionPanels from 'aa/shop/productsoverview/components/categorydetails/CategoriesExpansionPanels.vue';
import cat101Category from 'common/testtools/scenariosstore/aa/cat101Category.json';

Vue.use(VueI18n);
Vue.use(pinia);

window.frontendData.coreData = coreDataDefault;

const storeRootStore = useStoreRootStore();
storeRootStore.coreData = coreDataDefault;

new Vue({
    el: '#vue-app',
    i18n,
    pinia,
    vuetify,
    components: {
        TestPageRoot,
        CategoriesExpansionPanels
    },
    data: {
        categories: cat101Category.subcategories
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <categories-expansion-panels
              :categories="categories">
          </categories-expansion-panels>
        </div>
      </test-page-root>
    `
});
