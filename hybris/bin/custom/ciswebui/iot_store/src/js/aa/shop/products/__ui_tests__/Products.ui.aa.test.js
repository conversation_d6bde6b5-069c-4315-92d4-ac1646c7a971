import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords, scrollDown} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import productsData from 'common/testtools/scenariosstore/aa/productsData';
import pricesData from 'common/testtools/scenariosstore/aa/pricesData.json';
import {cloneDeep} from 'lodash';
import * as Sizes from 'common/testtools/testScreenSizes';
import {countryCookie, languageCookie} from 'common/testtools/testCookies';
import coreDataFromRest from 'common/testtools/scenariosstore/aa/coreDataFromRest.json';
import {breakpoints} from 'common/breakpoints';
import navigationItemsForAuthenticatedUser from 'common/testtools/scenariosstore/aa/navigationItemsForAuthenticatedUser.json';

const PRODUCTS_URL = '/shop/api/products?page=0&sortBy=acquisitionCount';
const PRICES_URL = '/shop/api/products/prices';
const CORE_DATA_URL = '/shop/api/coredata';
const productsRestHandler = (request, productsDataResult, pricesDataResult, responseCoreData = coreDataFromRest) => {
    if (request.url().endsWith(PRODUCTS_URL)) {
        request.respond({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(productsDataResult)
        });
    } else if (request.url().endsWith(PRICES_URL)) {
        request.respond({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(pricesDataResult)
        });
    } else if (request.url().endsWith(CORE_DATA_URL)) {
        request.respond({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(responseCoreData)
        });
    } else {
        request.continue();
    }
};

describe("aa products page with cookie banner", () => {
    it("looks as expected", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        await createInlineUiTestPage(testMap.ProductsAA, [{url: PRODUCTS_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });
});

describe("aa products page with direct sales enabled", () => {
    it("looks as expected", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        const coreData = cloneDeep(testMap.ProductsAA.coreData);
        coreData.moduleConfig.DIRECT_SALES = true;

        await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2500);
    });

    it("has a hardware compatibility dialog and looks as expected", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        const coreData = cloneDeep(testMap.ProductsAA.coreData);
        coreData.moduleConfig.DIRECT_SALES = true;

        await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(1000);
        await page.click('[data-id="link-show-more-hardwareRequirements"]');
        await page.waitFor(1000);

        await Sizes.testLargeScreensize(1000);
        await Sizes.testSmallScreensize(1000);
    });

    it("contains login cta banner when user not authenticated", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        const coreData = cloneDeep(testMap.ProductsAA.coreData);
        coreData.moduleConfig.DIRECT_SALES = true;
        coreData.userName = null;

        await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2500);
    });

    it("contains managed account banner when user company is managed account", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        const coreData = cloneDeep(testMap.ProductsAA.coreData);
        coreData.moduleConfig.DIRECT_SALES = true;
        coreData.currentCompany.managedAccount = true;

        await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2500);
    });

    it('contains managed account banner when user company is managed account and user is in another country store', async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        const coreData = cloneDeep(testMap.ProductsAA.coreData);
        coreData.moduleConfig.DIRECT_SALES = true;
        coreData.currentCompany.managedAccount = true;
        coreData.currentCountry.isocode = "PT";

        await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1000);
    });
});

describe("aa products page", () => {
    it("looks as expected", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        await createInlineUiTestPage(testMap.ProductsAA, [{url: PRODUCTS_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });

    it('shows mobile filters trigger on mobile', async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));
        await createInlineUiTestPage(testMap.ProductsAA, [{url: PRODUCTS_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 380, height: 1000});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot("mobile trigger");
    });

    it('shows mobile filters using portal', async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));
        await createInlineUiTestPage(testMap.ProductsAA, [{url: PRODUCTS_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 380, height: 1000});
        await page.waitFor(1000);
        await page.click('[data-id="button-toggle-filters"]');
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot("mobile filters");
    });

    it("has a footer and looks as expected", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        await createInlineUiTestPage(testMap.ProductsAA, [{url: PRODUCTS_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 1200, height: 1809});
        await page.waitFor(3000);
        expect(await page.screenshot()).toMatchImageSnapshot("extra large size");

        await page.setViewport({width: 992, height: 1906});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("large size");

        await page.setViewport({width: 720, height: 2514});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("medium size");

        await page.setViewport({width: 576, height: 2495});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("small size");
    });

    it('Looks as expected for anonymous user', async () => {
        let data = cloneDeep(testMap.ProductsAA);
        data.coreData.userName = null;

        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, pricesData));

        await createInlineUiTestPage(data, [{url: PRODUCTS_URL, data: data.coreData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 1220, height: 1906});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("anonymous large size");
    });

    it("app cards with company profile links looks as expected", async () => {
        const coreData = cloneDeep(testMap.ProductsAA.coreData);
        coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        const dataWithProfilesLinks = cloneDeep(productsData);
        dataWithProfilesLinks.products.map(product => {
            product.company.hasPublishedProfile = true;
            product.company.profileUrl = '/profile-url/uid';
        });
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, dataWithProfilesLinks));

        await createInlineUiTestPage({...testMap.ProductsAA, pageData: dataWithProfilesLinks, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });

    it("load more button", async () => {
        const coreData = cloneDeep(testMap.ProductsAA.coreData);
        coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        const dataWithProfilesLinks = cloneDeep(productsData);
        dataWithProfilesLinks.totalNumberOfResults=4;
        dataWithProfilesLinks.totalNumberOfPages=2;
        dataWithProfilesLinks.pageSize=2;
        dataWithProfilesLinks.products.length=2;

        dataWithProfilesLinks.products.map(product => {
            product.company.hasPublishedProfile = true;
            product.company.profileUrl = '/profile-url/uid';
        });
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, dataWithProfilesLinks));

        await createInlineUiTestPage({...testMap.ProductsAA, pageData: dataWithProfilesLinks, coreData: coreData});
        await acceptCookieBanner();
        await scrollDown();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });

    it("app cards with special product price looks as expected", async () => {
        let data = cloneDeep(testMap.ProductsAA);
        data.coreData.moduleConfig.DIRECT_SALES = true;
        data.coreData.userName = 'Tom Repairer';

        const specialPricesData = cloneDeep(pricesData);
        specialPricesData.map(price => {
            price.specialOffer = true;
        });
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData, specialPricesData));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2500);
    });

    describe('aa products page header', () => {
        describe('language switcher', () => {
            let coreData;
            const coreDataRestHandler = (request, productsDataResult) => {
                if (request.url().endsWith('/shop/api/coredata')) {
                    request.respond({
                        status: 200,
                        contentType: 'application/json',
                        body: JSON.stringify(coreDataFromRest)
                    });
                } else if (request.url().endsWith(PRODUCTS_URL)) {
                    request.respond({
                        status: 200,
                        contentType: 'application/json',
                        body: JSON.stringify(productsDataResult)
                    });
                } else {
                    request.continue();
                }
            };

            const cookies = [languageCookie('en'), countryCookie('AT')];

            beforeEach(async () => {
                coreData = cloneDeep(testMap.ProductsAA.coreData);
                coreData.userName = null;
                coreData.moduleConfig.LANGUAGE_SWITCHER = true;

                await page.setRequestInterception(true);
                page.on('request', (request) => coreDataRestHandler(request, productsData));

                await page.setCookie(...cookies);
            });

            afterEach(async () => {
                await page.deleteCookie(...cookies);
            });

            it('activator', async () => {
                await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
                await acceptCookieBanner();
                await resetMouseCoords();

                await Sizes.testExtraLargeScreensize(1000);
                await Sizes.testLargeScreensize(1000);
                await Sizes.testMediumScreensize(1000);
                await Sizes.testSmallScreensize(1000);
                await Sizes.testExtraSmallScreensize(1000);
            });

            it('dialog', async () => {
                await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
                await acceptCookieBanner();
                await resetMouseCoords();

                const activatorSelector = '[data-id="button-language-switcher-dialog-activator"]';
                await page.waitForSelector(activatorSelector);
                await page.click(activatorSelector);

                await Sizes.testExtraLargeScreensize(1000);
                await Sizes.testLargeScreensize(1000);
            });

            it('dialog (mobile slot in CDNavbar)', async () => {
                await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
                await acceptCookieBanner();
                await resetMouseCoords();

                await page.setViewport({width: breakpoints.M, height: 1000});
                await page.waitFor(1500);

                const activatorSelector = '[data-id="button-language-switcher-dialog-activator"]';
                await page.waitForSelector(activatorSelector);
                await page.click(activatorSelector);

                await Sizes.testMediumScreensize(1000);
                await Sizes.testSmallScreensize(1000);
                await Sizes.testExtraSmallScreensize(1000);
            });

            it('mandatory selection', async () => {
                await page.deleteCookie(...cookies);

                await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
                await acceptCookieBanner();
                await resetMouseCoords();

                await Sizes.testExtraLargeScreensize(1000);
                await Sizes.testLargeScreensize(1000);
                await Sizes.testMediumScreensize(1000);
                await Sizes.testSmallScreensize(1000);
                await Sizes.testExtraSmallScreensize(1000);
            });

            it('shows switch back banner when user is in another country store', async () => {
                coreData.currentCountry = {
                    "isocode": "PT",
                    "name": "Portugal",
                    "canBuy": true,
                    "blockedCountriesCommercial": []
                };
                coreData.navigationItems = navigationItemsForAuthenticatedUser;
                coreData.userName = 'Tom Repairer';

                await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});

                await acceptCookieBanner();
                await resetMouseCoords();

                await Sizes.testExtraLargeScreensize(1000);
                await Sizes.testLargeScreensize(1000);
                await Sizes.testMediumScreensize(1000);
                await Sizes.testSmallScreensize(1000);
                await Sizes.testExtraSmallScreensize(1000);
            });

            it('dialog (in CDFooter)', async () => {
                await createInlineUiTestPage({...testMap.ProductsAA, coreData: coreData});
                await acceptCookieBanner();
                await resetMouseCoords();

                await page.waitFor(1500);
                const locationFooterElement = '[data-id="footer"] div > a';
                await page.waitForSelector(locationFooterElement);
                await page.click(locationFooterElement);

                await Sizes.testExtraLargeScreensize(1000);
                await Sizes.testLargeScreensize(1000);
                await Sizes.testMediumScreensize(1000);
                await Sizes.testSmallScreensize(1000);
                await Sizes.testExtraSmallScreensize(1000);
            });
        });
    });
});
