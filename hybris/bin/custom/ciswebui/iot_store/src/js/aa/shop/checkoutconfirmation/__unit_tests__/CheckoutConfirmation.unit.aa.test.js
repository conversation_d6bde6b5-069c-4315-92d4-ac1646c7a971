import 'common/testtools/unit_tests_mock'
import 'common/test-directive'
import {localVue, wrapperComponentFactory} from 'common/testtools/unit-test-utils'
import CheckoutConfirmation from '../CheckoutConfirmation.vue'
import {cloneDeep} from 'lodash'

jest.mock('common/util')

const mountCheckoutConfirmation = (props = {}) => wrapperComponentFactory(CheckoutConfirmation, {
    props: {
        coreData: {
            userCompanyUnderReview: false,
            numberOfCartItems: 0,
            moduleConfig: {
                ORDER_CONFIRMATION_INFOBOX: true
            },
            supportUrl: 'https://support.example.com'
        },
        pageData: {
            buyerEmail: '<EMAIL>',
            licensingEmail: '<EMAIL>',
            licensePortalUrl: 'https://license.example.com',
            urlData: [
                { code: 'ORDER1', url: 'https://order1.example.com', ownAppOrder: false },
                { code: 'ORDER2', url: 'https://order2.example.com', ownAppOrder: true }
            ],
            camerasUrl: 'https://cameras.example.com'
        },
        ...props
    },
    shallow: false
})

const defaultCoreData = {
    userCompanyUnderReview: false,
    numberOfCartItems: 0,
    moduleConfig: {
        ORDER_CONFIRMATION_INFOBOX: true
    },
    supportUrl: 'https://support.example.com',
    navigationItems: [
        {
            itemCode: 'globalSupport',
            url: 'https://support.example.com'
        }
    ]
}

describe('CheckoutConfirmation', () => {
    let coreData

    beforeEach(() => {
        coreData = cloneDeep(defaultCoreData)
        window.frontendData = { coreData }
    })

    describe('order summary', () => {
        it('displays correct number of orders', async () => {
            const wrapper = await mountCheckoutConfirmation()
            await new Promise(process.nextTick)

            expect(wrapper.findAll('a[target="_blank"]').length).toBe(2)
        })
    })

    describe('action buttons', () => {
        it('shows continue shopping button when cart is empty', async () => {
            const wrapper = await mountCheckoutConfirmation()
            await new Promise(process.nextTick)

            expect(wrapper.find('[data-id="button-continue-shopping"]').exists()).toBeTruthy()
            expect(wrapper.find('[data-id="button-back-to-cart"]').exists()).toBeFalsy()
        })

        it('shows back to cart button when cart has items', async () => {
            coreData.numberOfCartItems = 1
            const wrapper = await mountCheckoutConfirmation({ coreData })
            await new Promise(process.nextTick)

            expect(wrapper.find('[data-id="button-back-to-cart"]').exists()).toBeTruthy()
            expect(wrapper.find('[data-id="button-continue-shopping"]').exists()).toBeFalsy()
        })

        it('shows license management button when user company is not under review', async () => {
            const wrapper = await mountCheckoutConfirmation()
            await new Promise(process.nextTick)

            expect(wrapper.find('[data-id="button-licensemanagement"]').exists()).toBeTruthy()
        })

        it('hides license management button when user company is under review', async () => {
            coreData.userCompanyUnderReview = true
            const wrapper = await mountCheckoutConfirmation({ coreData })
            await new Promise(process.nextTick)

            expect(wrapper.find('[data-id="button-licensemanagement"]').exists()).toBeFalsy()
        })
    })

    describe('summary box', () => {
        it('is displayed when enabled', async () => {
            const wrapper = await mountCheckoutConfirmation()
            await new Promise(process.nextTick)

            expect(wrapper.find('.my-apps').exists()).toBeTruthy()
        })

        it('is not displayed when disabled', async () => {
            coreData.moduleConfig.ORDER_CONFIRMATION_INFOBOX = false
            const wrapper = await mountCheckoutConfirmation({ coreData })
            await new Promise(process.nextTick)

            expect(wrapper.find('.my-apps').exists()).toBeFalsy()
        })
    })

    describe('computes properties correctly', () => {
        it('camerasPurchasedUrl', async () => {
            const wrapper = await mountCheckoutConfirmation()
            await new Promise(process.nextTick)

            expect(wrapper.vm.camerasPurchasedUrl).toBe('https://cameras.example.com/systems?purchased-apps=true')
        })
    })
})