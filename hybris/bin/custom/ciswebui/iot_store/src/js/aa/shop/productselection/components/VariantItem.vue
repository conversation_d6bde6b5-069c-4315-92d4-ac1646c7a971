<template>
  <v-card outlined
          height="100%">
    <v-card-text class="pa-md-6">
      <p v-if="isBundle">
          <span class="cd-text-large black--text font-weight-bold"
                data-id="text-bundle-label"
                style="vertical-align: middle">
            {{ bundleLabel }}
          </span>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <CDIcon icon="$info"
                      v-bind="attrs"
                      v-on="on"
                      v-on:click.stop></CDIcon>
            </template>
            <span>{{ $tc('shop.productSelection.variantSelection.variant.bundle.toolTipText', bundleInfo.size) }}</span>
          </v-tooltip>
      </p>
      <p v-if="variant.price"
         class="text-h3 black--text mb-0"
         :data-id="`text-variant-price-${variant.code}`">
        {{
          $t(
              'shop.productSelection.variantSelection.variant.pricingSection.price',
              {
                currency: variant.currencyIsocode,
                price: $n(variant.price, 'price', localeForFormats)
              }
          )
        }}{{ isSpecialOffer ? '*' : '' }}
        {{ isSubscription ? $t('shop.productSelection.variantSelection.variant.pricingSection.perYear') : '' }}
      </p>

      <!-- In case the variant is not a special offer but another variant of the same runtime is then display a gap instead of the chip -->
      <div v-if="displaySpecialOfferChipContainer"
           :data-id="`container-special-price-chip-product-${variant.code}`">
        <special-offer-chip
            :class="{'hidden': !isSpecialOffer}"
            class="mt-4"
            disabled>
        </special-offer-chip>
      </div>
    </v-card-text>
    <v-card-actions class="px-4 pb-4 px-md-6 pb-md-6 pt-0">
      <v-tooltip bottom
                 :disabled="canBePurchased"
                 :data-id="`tooltip-variant-non-purchasable-${variant.code}`">
        <template v-slot:activator="{ on }">
          <div v-on="on" class="add-button">
            <CDQuantity :value="quantity"
                        v-on:input="onInput"
                        v-on="on"
                        :label="$t('shop.productSelection.variantSelection.variant.pricingSection.addButton')"
                        :data-id="`button-add-variant-${variant.code}`"
                        :disabled="!canBePurchased || isTHLProductAlreadyInCart">
            </CDQuantity>
          </div>
        </template>
        <span>{{ tooltipMessage }}</span>
      </v-tooltip>
    </v-card-actions>
  </v-card>
</template>


<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {BundleInfoData, LicensePurchasability, ProductLicenseData} from 'common/generated-types/types';
import {LicenseType} from 'common/types';
import {useProductSelectionStore} from 'shop/store/productSelection';
import {bundleInfoService, i18nService} from 'common/services';
import SpecialOfferChip from 'aa/shop/core/components/specialoffer/SpecialOfferChip.vue';
import ConfirmationDialog from 'common/components/popups/ConfirmationDialog.vue';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';


@Component({
  components: {ConfirmationDialog, SpecialOfferChip}
})
export default class VariantItem extends Vue {
  @Prop() variant!: ProductLicenseData;
  @Prop() discountAvailable!: boolean;

  productSelectionStore = useProductSelectionStore();
  storeRootStore = useStoreRootStore();

  showConfirmationDialog = false;
  localeForFormats = '';

  mounted(): void {
    this.localeForFormats = i18nService.getLocaleForFormats();
  }

  get i18nService() {
    return i18nService;
  }

  get isSubscription(): boolean {
    return this.variant.licenseType.code === LicenseType.SUBSCRIPTION;
  }

  get bundleInfo(): BundleInfoData {
    return this.variant.bundleInfo;
  }

  get isBundle(): boolean {
    return !!this.bundleInfo?.code;
  }

  get isTHLProductAlreadyInCart(): boolean {
    return this.productSelectionStore.product?.code === this.storeRootStore.thlAppCodeInCart;
  }

  get canBePurchased(): boolean {
    return LicensePurchasability.PURCHASABLE === this.variant.purchasability;
  }


  get isImportedCompanyEligibleToBuy(): boolean {
    return LicensePurchasability.IMPORTED_COMPANY_NOT_ELIGIBLE === this.variant.purchasability;
  }

  get tooltipMessage(): string {
    if (this.isImportedCompanyEligibleToBuy) {
      return this.$t('shop.productSelection.variantSelection.variant.pricingSection.importedCompanyTooltipText') as string;
    }
    return this.$t('shop.productSelection.variantSelection.variant.pricingSection.tooltipText') as string;
  }

  get bundleLabel(): string {
    return bundleInfoService.getBundleLabel(this.bundleInfo, this.$i18n);
  }

  get quantity(): number {
    return this.productSelectionStore.quantity(this.variant);
  }

  get displaySpecialOfferChipContainer(): boolean {
    return this.isSpecialOffer || this.anyVariantWithSameRuntimeIsSpecialOffer;
  }

  get isSpecialOffer(): boolean {
    return this.variant.specialOffer;
  }

  get anyVariantWithSameRuntimeIsSpecialOffer(): boolean {
    return !!this.productSelectionStore.product?.licenses
        .filter((license: ProductLicenseData) => this.variant.runtime.code === license.runtime.code)
        .some((license: ProductLicenseData) => license.specialOffer);
  }

  onInput(quantity: number): void {
    this.productSelectionStore.select(this.variant, quantity);
    if (this.discountAvailable && quantity === 1) {
      if (this.productSelectionStore.consent === null) {
        this.$emit('request-discount-confirmation');
      }
    }
    if (quantity === 0) {
      this.productSelectionStore.consent = null;
    }
  }
}
</script>
<style lang="scss" scoped>

.add-button {
  width: 100%
}

.hidden {
  opacity: 0;
}

</style>
