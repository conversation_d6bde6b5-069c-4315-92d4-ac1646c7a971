import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import VariantSelection from '../VariantSelection';
import productSelection from 'common/testtools/scenariosstore/aa/productSelection.json';

const mountVariantSelection = (variants) => wrapperComponentFactory(VariantSelection, {
    props: {
        variants: variants
    },
    mocks: {}
});

describe('VariantSelection', () => {
    let product;

    beforeEach(() => {
        product = productSelection;
    });

    describe('Section Title', () => {
        it('is title for bundles when at least one variant is a bundle', async () => {
            product.licenses[0].bundleInfo = {
                code: 'BI_M_3',
                name: 'M',
                size: 3
            };

            const wrapper = await mountVariantSelection(product.licenses);

            const sectionTitle = wrapper.find('[data-id="text-variant-selection-title"]');
            expect(sectionTitle.text()).toBe('shop.productSelection.variantSelection.variant.titleBundles');
        });

        it('is title for non bundles when no variants are bundles', async () => {
            product.licenses.forEach(license => license.bundleInfo = {
                code: null,
                name: null,
                size: 0
            });

            const wrapper = await mountVariantSelection(product.licenses);

            const sectionTitle = wrapper.find('[data-id="text-variant-selection-title"]');
            expect(sectionTitle.text()).toBe('shop.productSelection.variantSelection.variant.titleNonBundles');
        });
    });

    describe('bundleVariants', () => {
        it('returns true when at least one variant is a bundle', async () => {
            product.licenses[0].bundleInfo = {
                code: 'BI_M_3',
                name: 'M',
                size: 3
            };

            const wrapper = await mountVariantSelection(product.licenses);

            const bundleVariants = wrapper.vm.bundleVariants;
            expect(bundleVariants).toBeTruthy();
        });

        it('returns true when no variants are bundles', async () => {
            product.licenses.forEach(license => license.bundleInfo = {
                code: null,
                name: null,
                size: 0
            });

            const wrapper = await mountVariantSelection(product.licenses);

            const bundleVariants = wrapper.vm.bundleVariants;
            expect(bundleVariants).toBeFalsy();
        });
    });

});
