<template>
  <CDBreadcrumbs :items="items"></CDBreadcrumbs>
</template>

<script setup lang="ts">
import {ShopRoute} from 'common/constants';
import {computed} from 'vue';
import {useRoute} from 'vue-router/composables';
import {useI18n} from 'vue-i18n-bridge';
import {useProductSelectionStore} from 'shop/store/productSelection';
import {StoreDataProvider} from 'shop/resources';

const {t} = useI18n();
const route = useRoute();
const productSelection = useProductSelectionStore();

function pageItem(disabled: boolean, name: string, href?: string) {
  return {
    text: t(`navigation.breadcrumbs.${name}`),
    disabled,
    href
  };
}

function homeItem(disabled: boolean) {
  return pageItem(disabled,'HomePage', ShopRoute.HOME);
}

function productSelectionItem() {
  // While the product details are still loading no additional breadcrumb item is shown. Similar to the product selection page itself, which
  // stays empty while product details are loading.
  if (productSelection.product === undefined) return [];

  return [
    {
      text: productSelection.product.name,
      disabled: true
    }
  ];
}

function orderDetailsItems() {
  return [
    pageItem(false, ShopRoute.ORDER_HISTORY_NAME, ShopRoute.ORDER_HISTORY),
    {
      text: t(`navigation.breadcrumbs.OrderDetailsPage`, {code: StoreDataProvider.pageData.code}),
      disabled: true
    }
  ];
}

const items = computed(() => {
  switch (route.name) {
    case ShopRoute.HOME_NAME:
    case ShopRoute.PRODUCTS_OVERVIEW_NAME:
      return [homeItem(true)];
    case ShopRoute.PRODUCT_SELECTION_NAME:
      return [homeItem(false), ...productSelectionItem()];
    case ShopRoute.ORDER_DETAILS_NAME:
      return [homeItem(false), ...orderDetailsItems()];
    default:
      return [homeItem(false), pageItem(true, route.name ?? 'unknown')];
  }
});
</script>
