<template>
  <v-container fluid class="pa-0 mt-12 mb-10">
    <v-row class="ma-n2 ma-md-n3 pb-6 pb-md-5">
      <v-col class="pa-2 pa-md-3">
        <h2 data-id="text-runtime-selection-title">
          {{ $t('shop.productSelection.variantSelection.runtime.runtimeType') }}
        </h2>
      </v-col>
    </v-row>

    <v-row class="ma-n2 ma-md-n3">
      <v-col v-for="runtime in runtimes"
             :key="runtime.code"
             class="pa-2 pa-md-3"
             cols="12"
             md="4"
             lg="3">

        <v-card outlined
                height="100%"
                @click.native="select(runtime.code)"
                :class="{ 'selected-runtime' : selectedRuntime === runtime.code }"
                :data-id="`container-runtime-card-${runtime.code}`">
          <v-card-title class="cd-text-large black--text text-left font-weight-bold pa-md-6">
            {{ $t(`shop.productSelection.variantSelection.runtime.name.${runtime.code}`) }}
          </v-card-title>
          <v-card-text class="px-md-6 pb-md-6">
            <div class="text-body-2 black--text text-left">
              <ul>
                <li v-for="detail in tm(`shop.productSelection.variantSelection.runtime.descriptionDetailsMap.${runtime.code}`)"
                    :key="detail">
                  <span>{{ detail }}</span>
                </li>
              </ul>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

  </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {RuntimeData, SimpleProductData} from 'common/generated-types/types';
import {uniqBy} from 'lodash';
import VariantItem from 'aa/shop/productselection/components/VariantItem.vue';
import {useI18n} from 'vue-i18n-bridge';


@Component({
  components: {VariantItem}
})
export default class RuntimeSelection extends Vue {
  @Prop({ default: '' }) value!: string;
  @Prop() product!: SimpleProductData;

  selectedRuntime = this.value;

  mounted(): void {
    if (this.runtimes.length > 0 && !this.runtimes.find(r => r.code === this.selectedRuntime)) {
      this.select(this.runtimes[0].code);
    }
  }

  get runtimes(): RuntimeData[] {
    const runtimes = this.product?.licenses?.map(license => license.runtime);
    return this.customSortRuntimes(uniqBy(runtimes, runtime => runtime.code));
  }

  select(runtime: string): void {
    this.selectedRuntime = runtime;
    this.$emit('input', runtime);
  }

  tm(code: string) {
    const { tm } = useI18n();
    return Object.fromEntries(Object.entries(tm(code)).sort());
  }

  customSortRuntimes(arr: RuntimeData[]): RuntimeData[] {
    const desiredOrder = ['runtime_subs_unlimited', 'runtime_full_3y', 'runtime_full_unlimited'];

    arr.sort((a, b) => {
      const indexA = desiredOrder.indexOf(a.code);
      const indexB = desiredOrder.indexOf(b.code);

      if (indexA === -1 && indexB === -1) {
        return 0;
      } else if (indexA === -1) {
        return 1;
      } else if (indexB === -1) {
        return -1;
      }

      return indexA - indexB;
    });

    return arr;
  }
}
</script>
<style lang="scss" scoped>

.selected-runtime {
  border-color: var(--v-black-lighten1);
  border-width: 2px;
}

</style>
