<template>
  <div class="cart-page">
    <account-notification :userCompanyUnderReview="coreData.userCompanyUnderReview"></account-notification>
    <v-container>
      <v-row>
        <v-col cols="12">
          <div class="header-container">
            <h1 class="d-flex align-content-center mb-8">
              <CDIcon color="black" class="mr-4" large>$cart</CDIcon>
              <h1>{{ $t('shop.cart.cart') }}</h1>
            </h1>
          </div>
        </v-col>
      </v-row>
    </v-container>

    <template v-if="nonEmptyCompanies.length > 0">
      <cart class="mb-12" v-for="company in nonEmptyCompanies"
            v-bind:key="company.cartCode"
            v-bind:cart-code="company.cartCode"
            v-bind:cart-items="company.cartItems"
            v-bind:company-name="company.developerCompanyName"
            v-bind:total-price="company.totalPrice"
            v-bind:total-price-without-discount="company.totalPriceWithoutDiscount"
            v-bind:is-own-app-purchase="company.ownAppsPurchase"
            v-bind:country="coreData.currentCountry?.isocode"
            v-bind:user-group="coreData.currentCompany?.userGroup"
            v-on:cart-data-changed="onCartPageDataChanged">
      </cart>
      <v-container v-if="showSpecialOfferConditions" class="my-16">
        <v-row>
          <v-col cols="12">
            <special-offer-conditions>
            </special-offer-conditions>
          </v-col>
        </v-row>
      </v-container>
    </template>

    <v-container v-else>
      <div class="empty-content">
        <h2 class="empty-cart-message">
          {{ $t('shop.cart.emptyCartMessage') }}
        </h2>
      </div>
    </v-container>

    <v-container>
      <v-row>
        <v-col cols="12">
          <CDButton large class="mb-4" :href="shopHome">
            {{ $t('shop.cart.continueShopping') }}
          </CDButton>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CartPageData, ShopCoreData} from 'common/generated-types/types';
import CartIcon from 'common/images/boxed/icon-24-cart-b.svg';
import {Cart} from 'aa/shop/cartpage/components';
import {findIndex, isEmpty} from 'lodash';
import {ShopRoute} from 'common/constants';
import {StoreDataProvider} from 'shop/resources';
import SpecialOfferConditions from 'aa/shop/core/components/specialoffer/SpecialOfferConditions.vue';
import AccountNotification from 'aa/shop/account/AccountNotification.vue';

@Component({
        components: {
          AccountNotification,
	        SpecialOfferConditions,
          Cart,
          CartIcon
        }
    })
    export default class CartPage extends Vue {
        @Prop() pageData!: Array<CartPageData>;

        coreData: ShopCoreData = StoreDataProvider.coreData;
        shopHome: string = ShopRoute.HOME;
        companies: Array<CartPageData> = this.pageData;

        created(): void {
            this.setPageTitle();
        }

        private setPageTitle(): void {
            document.title = this.$t('shop.cart.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
        }

        get nonEmptyCompanies(): CartPageData[] {
            return this.companies.filter(company => !isEmpty(company.cartItems));
        }

        onCartPageDataChanged(data:CartPageData): void {
            const index = findIndex(this.companies, {'cartCode': data.cartCode});
            index !== -1 && this.companies.splice(index, 1, data);
        }

        get showSpecialOfferConditions(): boolean {
            // check if any of the nonEmptyCompanies cartItems has a specialOffer
            return this.nonEmptyCompanies.some((company) => company.cartItems.some((cartItem) => cartItem.specialOffer));
        }
    }
</script>
<style scoped lang="scss">
    @import "common/design";
    @import "shop/core/constants";
    @import "shop/pages/checkout";
</style>
