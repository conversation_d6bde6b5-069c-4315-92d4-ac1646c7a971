<template>
  <div v-if="rootCategoryHierarchy" class="mb-16 mt-8">

    <categories-groups-navigation :categories-groups="categoriesGroupsToDisplay"
                                  :category-group-in-focus="categoryGroupInFocus"
                                  @categoryGroupLinkClicked="goToCategoryGroupLink">
    </categories-groups-navigation>

    <category-group-details v-for="categoryGroup in categoriesGroupsToDisplay"
                            :key="categoryGroup.code"
                            :category-group="categoryGroup"
                            class="category-group-section"
                            ref="categoryGroups"
                            v-intersect.quiet="{
                                handler: onIntersect(categoryGroup),
                                options: {
                                  rootMargin: '-50% 0% -50% 0%'
                                }
                            }">
    </category-group-details>
    <special-offer-conditions v-if="showSpecialOfferConditions" class="mt-16">
    </special-offer-conditions>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryHierarchyDTO} from 'common/generated-types/types';
import ProductsOverviewHeader from 'aa/shop/productsoverview/components/ProductsOverviewHeader.vue';
import CategoryGroupDetails from 'aa/shop/productsoverview/components/categorydetails/CategoryGroupDetails.vue';
import CategoriesGroupsNavigation from 'aa/shop/productsoverview/components/CategoriesGroupsNavigation.vue';
import {categoriesService} from 'aa/shop/services/categoriesService';
import SpecialOfferConditions from 'aa/shop/core/components/specialoffer/SpecialOfferConditions.vue';

/**
 * Categories groups represent the first level sub categories of the Root.
 * e.g cat_1 and cat_2 in the following structure
 * -> main
 * ----> cat_1: Steuergeräte-Diagnose
 * ----> cat_2: Fahrzeugsystem-Analyse
 **/
@Component({
  components: {SpecialOfferConditions, CategoriesGroupsNavigation, CategoryGroupDetails, ProductsOverviewHeader}
})
export default class CategoriesGroups extends Vue {
  @Prop() rootCategoryHierarchy!: CategoryHierarchyDTO;

  categoryGroupInFocus = {} as CategoryHierarchyDTO;

  $refs!: {
    categoryGroups: CategoryGroupDetails[];
  };

  mounted() {
    const categoriesGroups = this.categoriesGroupsToDisplay;
    if (categoriesGroups && categoriesGroups.length) {
      this.categoryGroupInFocus = categoriesGroups[0];
    }
  }

  get categoriesGroupsToDisplay(): CategoryHierarchyDTO[] {
    const categoriesGroupsToDisplay = categoriesService.filterEmptyCategories(this.rootCategoryHierarchy.subcategories);
    return categoriesService.sortCategories(categoriesGroupsToDisplay);
  }

  goToCategoryGroupLink(categoryGroup: CategoryHierarchyDTO): void {
    const categoryGroupDetails = this.$refs.categoryGroups
        .find(categoryGroupRef => categoryGroupRef.categoryGroup.code === categoryGroup.code) as CategoryGroupDetails;
    if (categoryGroupDetails) {
      categoryGroupDetails.$el.scrollIntoView({block: 'start', behavior: 'smooth'});
    }
  }

  onIntersect(categoryGroup: CategoryHierarchyDTO) {
    return (observerEntries: IntersectionObserverEntry[]) => {
      const observerEntry = observerEntries.pop();
      if (!observerEntry?.isIntersecting) {
        return;
      }
      this.categoryGroupInFocus = categoryGroup;
    };
  }

  get showSpecialOfferConditions(): boolean {
    return categoriesService.containsSpecialOfferProducts(this.rootCategoryHierarchy);
  }
};
</script>
