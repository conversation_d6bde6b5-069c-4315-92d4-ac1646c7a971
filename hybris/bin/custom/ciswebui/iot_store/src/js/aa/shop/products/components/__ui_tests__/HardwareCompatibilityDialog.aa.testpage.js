import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import HardwareCompatibilityDialog from 'aa/shop/products/components/HardwareCompatibilityDialog.vue';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        HardwareCompatibilityDialog
    },
    data: {
        facet: {
            'group': 'hardwareRequirements',
            'orderIndex': 7,
            'items': [
                {
                    'name': 'KTS 560/590',
                    'facetIndex': 'hardwareRequirements',
                    'facetValue': 'KTS 560/590',
                    'count': 44
                },
                {
                    'name': 'DCI 700',
                    'facetIndex': 'hardwareRequirements',
                    'facetValue': 'DCI 700',
                    'count': 4
                },
                {
                    'name': 'FSA 500',
                    'facetIndex': 'hardwareRequirements',
                    'facetValue': 'FSA 500',
                    'count': 1
                },
                {
                    'name': 'FSA 7XX',
                    'facetIndex': 'hardwareRequirements',
                    'facetValue': 'FSA 7XX',
                    'count': 1
                }
            ]
        },
        value: true,
        selectedFacet: null
    },
    template: `
      <test-page-root class="aastore">
        <div ref="mainwrap">
          <hardware-compatibility-dialog
            v-model="value"  
            v-bind:facet="facet">
          </hardware-compatibility-dialog>
        </div>
      </test-page-root>
    `
});
