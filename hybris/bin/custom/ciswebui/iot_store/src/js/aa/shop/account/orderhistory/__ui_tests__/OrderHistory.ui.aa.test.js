import {acceptCookieBanner, resetMouseCoords, createInlineUiTestPage} from "common/testtools/inlineTestPageCreator";
import {testMap} from 'common/testtools/scenariosstore';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';

describe("order history page", () => {
    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.OrderHistoryAA);
    });

    it("with orders", async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();
        await Sizes.testSmallScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testLargeScreensize(1050);
        await Sizes.testExtraLargeScreensize(1050);

        await page.hover(".v-data-table__expanded__row");
        expect(await page.screenshot()).toMatchImageSnapshot("when hovering over an order");

        await page.hover(".link-number a");
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("when hovering over a page number");
    });

    it("with orders next page", async () => {
        data.pageData.pagination.hasPrevious = true;
        data.pageData.pagination.currentPage = 1;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();
        await Sizes.testSmallScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testLargeScreensize(1050);
        await Sizes.testExtraLargeScreensize(1050);

        await page.hover(".v-data-table__expanded__row");
        expect(await page.screenshot()).toMatchImageSnapshot("when hovering over an order next page");
    });

    it("with order in pending state", async () => {
        data.pageData.results[0].statusDisplay = 'overdue';
        data.pageData.results[0].statusDisplay = 'pending';
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testLargeScreensize(1050);
    });

    it("with order in overdue and pending state", async () => {
        data.pageData.results[0].statusDisplay = 'overdue';
        data.pageData.results[0].statusDisplay = 'pending';
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testLargeScreensize(1050);
    });

    it("with order in awaiting license activation state", async () => {
        data.pageData.results[0].statusDisplay = 'AWAITING_LICENSE_ACTIVATION';
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testSmallScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testLargeScreensize(1050);
        await Sizes.testExtraLargeScreensize(1050);
    });

    it("with order in in validation state", async () => {
        data.pageData.results[0].statusDisplay = 'ON_VALIDATION';
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testSmallScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testLargeScreensize(1050);
        await Sizes.testExtraLargeScreensize(1050);
    });

    it("with order in in completed state", async () => {
        data.pageData.results[0].statusDisplay = 'completed';
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testSmallScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testLargeScreensize(1050);
        await Sizes.testExtraLargeScreensize(1050);
    });

    it("without orders", async () => {
        data.pageData.results = [];
        data.pageData.pagination.totalNumberOfResults = 0;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await Sizes.testLargeScreensize(1050);
    });

});
