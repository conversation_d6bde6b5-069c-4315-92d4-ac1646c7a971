<template>
  <v-expansion-panels
      v-if="categories && categories.length"
      accordion
      flat
      class="mt-8">
    <v-expansion-panel v-for="category in sortedCategories" :key="category.code">
      <v-expansion-panel-header hide-actions class="px-0" :data-id="`container-expansion-panel-header-${category.code}`">
        <template v-slot:default>
          <div>
            <v-icon color="primary">
              $expand
            </v-icon>
            <span class="primary--text">
              {{ category.name }}
            </span>
          </div>
        </template>
      </v-expansion-panel-header>
      <v-expansion-panel-content>
        <category-product-list :category="category"></category-product-list>
      </v-expansion-panel-content>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryHierarchyDTO} from 'common/generated-types/types';
import CategoryProductList from 'aa/shop/productsoverview/components/categorydetails/CategoryProductList.vue';
import {categoriesService} from 'aa/shop/services/categoriesService';

/**
 * Displays categories in expansion panels. Each category's expansion panel contains the category's products.
 *
 */
@Component({
  components: {CategoryProductList}
})
export default class CategoriesExpansionPanels extends Vue {
  @Prop() categories!: CategoryHierarchyDTO[];

  get sortedCategories(): CategoryHierarchyDTO[] {
    return categoriesService.sortCategories(this.categories);
  }
}
</script>
