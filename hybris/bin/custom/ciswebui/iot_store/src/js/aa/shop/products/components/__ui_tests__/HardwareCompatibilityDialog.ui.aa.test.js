import * as Sizes from 'common/testtools/testScreenSizes';

describe('HardwareCompatibilityDialog', () => {

    it('component looks as expected', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-products-components-__ui_tests__-HardwareCompatibilityDialog');

        await Sizes.testMediumScreensize(1000);
        await Sizes.testLargeScreensize(1000);
        await Sizes.testExtraLargeScreensize(2000);
    });

    it('component looks as expected on mobile', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-products-components-__ui_tests__-HardwareCompatibilityDialog');

        await Sizes.testSmallScreensize(1000);
    });

});
