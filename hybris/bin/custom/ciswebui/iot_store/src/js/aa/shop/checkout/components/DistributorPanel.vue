<template>
  <div class="mt-12 mb-16" v-if="getOnlyValidDistributors.length > 0">
    <hr role="separator" aria-orientation="horizontal" class="v-divider theme--light"/>
    <h3 class="mt-12 mb-4">
      {{ $t('shop.checkout.distributor.title') }}
    </h3>
    <p class="mb-8">
      {{ $t('shop.checkout.distributor.description') }}
    </p>

    <v-row no-gutters>
      <h4 class="mr-4" data-id="current-distributor-name">{{ currentDistributor?.name }}</h4>

      <CDButtonText data-id="distributor-change-button"
                    v-on:click="distributorSwitcher"
                    class="link">
        {{ $t('shop.checkout.distributor.change') }}
      </CDButtonText>
    </v-row>

    <template v-if="distributorDialog">
      <distributor-dialog v-model="distributorDialog"
                          :current-distributor="currentDistributor"
                          :distributors-list="getOnlyValidDistributors"
                          v-on="$listeners">
      </distributor-dialog>
    </template>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import DistributorDialog from 'aa/shop/checkout/components/DistributorDialog.vue';
import {UmpDistributorData} from 'common/generated-types/types';

@Component({
  components: {DistributorDialog}
})
export default class DistributorPanel extends Vue {

  @Prop() listDistributors!: UmpDistributorData[];
  @Prop() currentDistributor!: UmpDistributorData;

  distributorDialog = false;

  distributorSwitcher(): void {
    this.distributorDialog = true;
  }

  get getOnlyValidDistributors(): UmpDistributorData[] {
    return (this.listDistributors?.filter(Boolean) || [])?.sort((a, b) => a.name.localeCompare(b.name));
  }
}

</script>

<style>
link {
  font-weight: bold;
}

.theme--light.v-btn.link {
  color: var(--v-primary-base);
}
</style>