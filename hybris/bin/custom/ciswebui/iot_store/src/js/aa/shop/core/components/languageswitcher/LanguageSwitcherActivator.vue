<template>
  <component
      :is="activatorComponent"
      color="black"
      icon="$world"
      v-bind="$attrs"
      v-on="$listeners"
      data-id="button-language-switcher-dialog-activator"
  >
    <template v-if="!$vuetify.breakpoint.mobile">
      {{ languageLabel(currentLanguage) }}
    </template>
  </component>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {i18nService} from 'common/services';
import {TranslateResult} from 'vue-i18n';
import {lowerCase} from 'lodash';

@Component
export default class LanguageSwitcherActivator extends Vue {
  currentLanguage = '';

  mounted() {
    this.currentLanguage = i18nService.getUserLanguage();
  }

  get activatorComponent() {
    return this.$vuetify.breakpoint.mobile ? 'CDButtonIcon' : 'CDButtonTextIcon';
  }

  languageLabel(isoCode: string): TranslateResult {
    if (!isoCode) {
      return '';
    }
    return this.$t(`language.${lowerCase(isoCode)}`);
  }
}
</script>
