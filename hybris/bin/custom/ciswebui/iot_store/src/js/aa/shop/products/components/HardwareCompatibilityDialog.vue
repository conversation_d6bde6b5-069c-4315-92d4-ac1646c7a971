<template>
  <v-dialog v-model="show"
            :fullscreen="mobile"
            width="850"
            scrollable
            content-class="white pa-md-8">

    <v-card flat data-id="container-hardware-compatibility-dialog">

      <v-card-title v-if="!mobile" class="mb-8 justify-space-between">
        <h2 class="text-capitalize" data-id="text-hardware-compatibility-dialog-header">
          {{ $t('shop.products.facets.compatibleHardwareDialog.title') }}
        </h2>
        <CDButtonIcon icon="$close"
                      @click="show = false"
                      data-id="button-hardware-compatibility-dialog-close">
        </CDButtonIcon>
      </v-card-title>

      <v-card-title v-else>
        <CDButtonTextIcon
            icon="$arrowleft"
            data-id="button-hardware-compatibility-mobile-dialog-close"
            @click.native="show = false">
          {{ $t('shop.products.facets.compatibleHardwareDialog.title') }}
        </CDButtonTextIcon>
      </v-card-title>

      <v-card-text data-id="container-hardware-compatibility-dialog-body">
        <v-row dense no-gutters>
          <v-col cols="10">
            <CDInput v-if="!mobile"
                     data-id="input-search-hardware-compatibility-dialog"
                     :clearable="true"
                     v-model="query"
                     background-color="white"
                     :placeholder="$t('shop.products.facets.compatibleHardwareDialog.searchHardware')"
                     prepend-inner-icon="$search">
            </CDInput>
          </v-col>
          <v-col cols="12">
            <v-radio-group v-if="!mobile"
                           data-id="radio-group-hardware"
                           v-model="selectedFacetValue" row>
              <v-col cols="4"
                     v-for="facetItem in filteredItems"
                     :key="facetItem.facetValue">
                <CDRadio
                    :label="facetItem.name"
                    :value="facetItem.facetValue"
                    @change="selectedFacetValue = facetItem.facetValue">
                </CDRadio>
              </v-col>
            </v-radio-group>
            <v-radio-group v-else data-id="radio-group-hardware-mobile-viewport">
              <v-col>
                <CDRadio
                    v-for="facetItem in filteredItems"
                    :key="facetItem.facetValue"
                    :label="facetItem.name"
                    :value="facetItem.facetValue"
                    @change="selectedFacetValue = facetItem.facetValue">
                </CDRadio>
              </v-col>
            </v-radio-group>
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-actions class="justify-end py-4" data-id="container-hardware-compatibility-dialog-footer">
        <CDButton class="mr-4"
                  data-id="button-hardware-compatibility-dialog-cancel"
                  @click="show = false">
          {{ $t('shop.products.facets.compatibleHardwareDialog.cancelButton') }}
        </CDButton>
        <CDButton class="mr-4"
                  color="primary"
                  data-id="button-hardware-compatibility-dialog-submit"
                  @click="addSelectedFacet"
                  :disabled="isFacetValueUnchanged">
          {{ $t('shop.products.facets.compatibleHardwareDialog.confirmButton') }}
        </CDButton>
      </v-card-actions>

    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {FacetData, FacetItem} from 'common/generated-types/types';

@Component
export default class HardwareCompatibilityDialog extends Vue {
  @Prop() selectedFacet!: FacetItem;
  @Prop() facet!: FacetData;
  @Prop() value!: boolean;

  query = '';
  selectedFacetValue = '';
  facetItems = [] as FacetItem[];

  mounted() {
    this.selectedFacetValue = this.selectedFacet?.facetValue;
    this.facetItems = this.facet?.items;
    this.query = '';
  }

  get filteredItems(): FacetItem[]{
    return this.facetItems.filter((item) => {
      return item.name.toLowerCase().includes(this.query.toLowerCase());
    });
  }

  get mobile(): boolean {
    return this.$vuetify.breakpoint.smAndDown;
  }

  addSelectedFacet() {
    if (!this.selectedFacetValue) return;

    const selectedFacetObject = this.facet.items.find(
        (facet) => facet.facetValue === this.selectedFacetValue
    );

    if (!selectedFacetObject) return;

    const facetItem = {
      name: selectedFacetObject.name,
      facetIndex: this.facet.group,
      facetValue: selectedFacetObject.facetValue,
      count: selectedFacetObject.count
    };

    this.$emit('facetSelection', facetItem);

    this.show = false;
  }

  get isFacetValueUnchanged(): boolean {
    return this.selectedFacetValue === this.selectedFacet?.facetValue;
  }

  get show(): boolean {
    return this.value;
  }

  set show(value) {
    this.$emit('input', value);
  }

}
</script>

<style scoped lang="scss">
@import "aa/shop/core/dialogs";
.header {
  &:after {
    content: "";
    width: 100%;
    height: 1px;
    background-color: var(--v-grey-darken2);
  }
}
</style>
