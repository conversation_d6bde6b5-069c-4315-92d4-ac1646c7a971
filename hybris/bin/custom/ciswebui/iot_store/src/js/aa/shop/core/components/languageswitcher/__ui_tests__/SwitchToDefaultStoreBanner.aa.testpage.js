import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import {SwitchToDefaultStoreBanner} from 'aa/shop/core/components/languageswitcher';
import {createApp} from 'vue-demi';

Vue.use(VueI18n, { bridge: true });

const options = {
    el: '#vue-app',
    i18n,
    vuetify,
    pinia,
    components: {
        TestPageRoot,
        SwitchToDefaultStoreBanner
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <SwitchToDefaultStoreBanner></SwitchToDefaultStoreBanner>
        </div>
      </test-page-root>
    `
};

const app = createApp(options);
app.use(i18n);
app.mount('#app');

Vue.use(VueI18n);
