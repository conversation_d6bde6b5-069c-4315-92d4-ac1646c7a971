<template>
  <div class="checkout-confirmation-container">
    <account-notification :userCompanyUnderReview="coreData.userCompanyUnderReview"></account-notification>

    <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="mt-4 mt-lg-16">
      <v-row>
        <v-col cols="12" lg="8">
          <div class="checkout-success-header">
            <h1 class="mb-10">{{ $t('shop.checkout.confirmation.confirmationHeader') }}</h1>
            <p class="checkout-success-text">
              <template>
                {{ $t('shop.checkout.confirmation.confirmationLine1') }}
                <br/>
                {{ $t('shop.checkout.confirmation.confirmationLine2') }}
                <br/>
                {{ $t('shop.checkout.confirmation.licenseActivation') }}
                <br/>
                <h2 class="mt-10 mb-1">{{ $t('shop.checkout.confirmation.paymentAndInvoice.header') }}</h2>
                {{ $t('shop.checkout.confirmation.paymentAndInvoice.text') }}
                <br/>
                <span v-if="orders.length === 1">{{ $t('shop.checkout.confirmation.confirmationLine3') }}:&nbsp;<a target="_blank" v-bind:href="orders[0].url">{{orders[0].code}}</a></span>
              </template>
              <span v-if="orders.length > 1">{{ $t('shop.checkout.confirmation.confirmationLine3plural') }}: <a target="_blank" v-bind:href="item.url" v-for="item in orders">{{item.code}}</a></span>
            </p>
          </div>
        </v-col>
        <v-col cols="12" lg="4">
          <div class="d-flex flex-column flex-md-row justify-start justify-lg-end">
            <CDButton v-if="coreData.numberOfCartItems > 0" :href="cartUrl" data-id="button-back-to-cart">
              {{ $t('shop.checkout.backToCart') }}
            </CDButton>
            <CDButton v-else
                      :href="licenseManagementPortalLink"
                      class="primary ml-md-4 ml-0 mt-md-0 mt-4"
                      data-id="button-licensemanagement"
            >
              {{$t('shop.checkout.confirmation.goToLicenseActivation')}}
            </CDButton>
          </div>
        </v-col>
      </v-row>

      <summary-box v-if="summaryBoxEnabled" :pageData="pageData"
                                            :summaryText="getSummaryText()"></summary-box>

    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CheckoutConfirmationData, ShopCoreData, UrlData} from 'common/generated-types/types';
import {ShopRoute} from 'common/constants';
import {ContentDivider} from 'common/components';
import {navigationService} from 'common/services';
import AccountNotification from 'aa/shop/account/AccountNotification.vue';
import {CheckoutResponsiveGridLayout} from 'shop/checkout/components';
import SummaryBox from 'aa/shop/checkoutconfirmation/SummaryBox.vue';

@Component({
  components: {
    SummaryBox,
    CheckoutResponsiveGridLayout,
    AccountNotification,
    ContentDivider
  }
})
export default class DelayedCheckoutConfirmation extends Vue {

  @Prop() coreData!: ShopCoreData;
  @Prop() pageData!: CheckoutConfirmationData;
  orders: UrlData[] = [];
  camerasUrl = '';
  shopHome: string = ShopRoute.HOME;
  ownAppOrder = false;
  licenseManagementPortalLink = this.pageData.licensePortalUrl;

  supportLink = this.coreData.supportUrl;
  readonly cartUrl = ShopRoute.CART;

  getSummaryText(): string {
    return this.$t('shop.checkout.summary.delayedConfirmationText', { licenseManagement:
          '<a href="'+ this.licenseManagementPortalLink + '" data-id="license-management-portal-link">' +
          this.$t('navigation.deviceManagementPortal') +
          '</a>'}) as string;
  }

  created(): void {
    this.setPageTitle();
  }

  private setPageTitle() {
    document.title = this.$t('shop.checkout.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
  }

  mounted(): void {
    this.orders = this.pageData.urlData;
    this.camerasUrl = this.camerasPurchasedUrl;
    this.ownAppOrder = this.orders.some(v => v.ownAppOrder);
    this.supportLink = navigationService.byItemCode('globalSupport').url;
  }

  get summaryBoxEnabled(): boolean {
    return this.coreData.moduleConfig?.ORDER_CONFIRMATION_INFOBOX ?? false;
  }

  get camerasPurchasedUrl(): string {
    const camerasRoute = '/systems';
    const url = new URL(this.pageData.camerasUrl + camerasRoute);
    const query = new URLSearchParams(url.search);
    query.append('purchased-apps', 'true');
    url.search = query.toString();
    return url.toString();
  }
}

</script>

<style scoped lang="scss">
.my-apps {
  border: solid 1px var(--v-grey-lighten1);
}

.checkout-success-text {
  font-family: BoschSansMedium, sans-serif !important;
  font-weight: 500 !important;
  line-height: 150% !important;
  font-size: 20px !important;
}
</style>
