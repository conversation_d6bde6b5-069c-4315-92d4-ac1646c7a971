import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import cat101Category from 'common/testtools/scenariosstore/aa/cat101Category.json';
import cat201Category from 'common/testtools/scenariosstore/aa/cat201Category.json';
import cat401Category from 'common/testtools/scenariosstore/aa/cat401Category.json';
import CategoryDetails from '../categorydetails/CategoryDetails';

const mountCategoryDetails = (category) => wrapperComponentFactory(CategoryDetails, {
    props: {
        category
    },
    shallow: false
});

describe('CategoryDetails', () => {

    it('given category with products when render then display its products and render its subcategories in expansion panels', async () => {
        const wrapper = mountCategoryDetails(cat401Category);

        expect(wrapper.find('[data-id="container-category-with-products-cat_401"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="container-category-products-list-cat_401"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="container-category-subcategories-expansion-panels-cat_401"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="container-category-tabs-cat_401"]').exists()).toBeFalsy();
    });

    it('given category with no products when render then display its subcategories in tabs', async () => {
        const wrapper = mountCategoryDetails(cat101Category);

        expect(wrapper.find('[data-id="container-category-tabs-cat_101"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="container-category-with-products-cat_101"]').exists()).toBeFalsy();
    });

    describe('hardwareRequirementsForCategoryProducts', () => {
        it('aggregates all products hardware requirements feature values', async () => {
            const wrapper = mountCategoryDetails(cat201Category);

            const categoriesGroupsToDisplay = wrapper.vm.hardwareRequirementsForCategoryProducts(cat201Category);

            expect(categoriesGroupsToDisplay.flatMap(group => group.value)).toEqual(['FSA 7XX', 'FSA 500']);
        });
    });

    describe('displayPicturesFromCategoryWithOrder', () => {
        it('finds the category by order and sets the category media as media to display', async () => {
            const wrapper = mountCategoryDetails(cat101Category);

            await wrapper.vm.displayPicturesFromCategoryWithOrder(1);

            expect(wrapper.vm.pictureToDisplay.url).toEqual('/sample-data/aa/categories/cat_10102-picture.jpeg');
            expect(wrapper.vm.thumbnailToDisplay.url).toEqual('/sample-data/aa/categories/cat_10102-thumbnail.jpeg');
        });
    });

});
