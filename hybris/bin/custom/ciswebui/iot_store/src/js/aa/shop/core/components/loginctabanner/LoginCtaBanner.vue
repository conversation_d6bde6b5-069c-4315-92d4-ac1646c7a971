<template>
  <StoreBanner closeable
               data-id="container-login-cta-banner">
    {{ $t('shop.loginCtaBanner.text') }}
    <template v-slot:actions>
      <CDButton :href="loginEntry.url"
                class="mr-2"
                data-id="button-login-cta-banner">
        {{ $t('navigation.items.storeLogin') }}
      </CDButton>
    </template>
  </StoreBanner>
</template>

<script setup lang="ts">
import StoreBanner from 'aa/shop/core/components/StoreBanner.vue';
import {computed} from 'vue';
import {navigationService} from 'common/services';

const loginEntry = computed(() => navigationService.byItemCode('storeLogin'));

</script>

