import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {LanguageSwitcherDialog} from 'aa/shop/core/components/languageswitcher';
import {i18nService} from 'common/services/i18nService';
import {useCoreDataStore} from 'shop/store/coreData';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';

jest.mock('common/services/i18nService');
jest.mock('common/util');

let languageSwitcherDialogModel = false;

const mountLanguageSwitcherDialog = () => wrapperComponentFactory(LanguageSwitcherDialog, {
    props: {
        value: languageSwitcherDialogModel,
        globalDefaultLanguage: 'en'
    },
    listeners: {
        input: e => { languageSwitcherDialogModel = e; }
    }
});

const coreDataStore = useCoreDataStore();
coreDataStore.coreData = {
    "activeCountries": [
        {
            "isocode": "AT",
            "name": "Austria",
            "defaultLanguage": "de",
            "supportedLanguages": [
                "en",
                "de"
            ]
        }
    ]
};

describe('LanguageSwitcherDialog', () => {

    beforeEach(() => {
        languageSwitcherDialogModel = false;
    });

    describe('languagesForCountry', () => {
        it('return languages for country', async () => {
            const wrapper = await mountLanguageSwitcherDialog();
            await new Promise(process.nextTick);

            const languagesForCountry = wrapper.vm.languagesForCountry('AT');
            expect(languagesForCountry).toEqual(expect.arrayContaining(['en', 'de']));
        });

        it('return empty for unknown country', async () => {
            const wrapper = await mountLanguageSwitcherDialog();
            await new Promise(process.nextTick);

            const languagesForCountry = wrapper.vm.languagesForCountry('CH');
            expect(languagesForCountry).toEqual([]);
        });
    });

    it('countries to return list of countries', async () => {
        const wrapper = await mountLanguageSwitcherDialog();
        await new Promise(process.nextTick);

        const countries = wrapper.vm.countries;
        expect(countries).toEqual(expect.arrayContaining(['AT']));
    });

    describe('onCountryChange', () => {
        it('resets language to default for country if selected is not compatible with country', async () => {
            const wrapper = await mountLanguageSwitcherDialog();
            wrapper.vm.selectedLanguage = 'fr';
            await new Promise(process.nextTick);

            await wrapper.vm.resetLanguageIfNotCompatibleWithCountry('AT');
            expect(wrapper.vm.selectedLanguage).toBe('de');
        });

        it('does not reset language if compatible', async () => {
            const wrapper = await mountLanguageSwitcherDialog();
            wrapper.vm.selectedLanguage = 'en';
            await new Promise(process.nextTick);

            await wrapper.vm.resetLanguageIfNotCompatibleWithCountry('AT');
            expect(wrapper.vm.selectedLanguage).toBe('en');
        });
    });

    it.each`
        country | language | dialog   | dismissible
        ${''}   | ${''}    | ${true}  | ${false}
        ${'AT'} | ${''}    | ${true}  | ${false}
        ${''}   | ${'de'}  | ${true}  | ${false}
        ${'AT'} | ${'de'}  | ${false} | ${true}
    `('mandatory selection is \'$dialog\' when country is \'$country\' and language is \'$language\'', async ({country, language, dialog, dismissible}) => {
        i18nService.getUserCountry.mockImplementation(() => country);
        i18nService.getUserLanguage.mockImplementation(() => language);

        const wrapper = await mountLanguageSwitcherDialog();
        await new Promise(process.nextTick);

        expect(languageSwitcherDialogModel).toBe(dialog);
        expect(wrapper.vm.dismissible).toBe(dismissible);
    });
});
