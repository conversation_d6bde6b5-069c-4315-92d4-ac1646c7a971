import * as Sizes from 'common/testtools/testScreenSizes';
import {cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";

describe('CategoryDetails', () => {

    beforeEach(async () => {
        await page.setRequestInterception(true);
        page.on('request', coreDataRestHandler);
        await page.setCookie(...cookies_en_AT);
    });

    it('component looks as expected for categories with multiple subcategories', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-CategoryDetails.multiple.subcategories');

        await Sizes.testLargeScreensize(2000);
        await Sizes.testSmallScreensize(2000);
    });

    it('component looks as expected for categories with one subcategory', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-CategoryDetails.one.subcategory');

        await Sizes.testLargeScreensize(2000);
        await Sizes.testSmallScreensize(2000);
    });

    it('component looks as expected for categories with multiple hardware compatibilities', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-CategoryDetails.multiple.hardwareCompatibilities');

        await Sizes.testLargeScreensize(2000);
        await Sizes.testSmallScreensize(2000);
    });


    it('component looks as expected for categories with multiple hardware compatibilities and new info for app', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-CategoryDetails.multiple.hardwareCompatibilities.newinfo');

        await Sizes.testLargeScreensize(2000);
        await Sizes.testSmallScreensize(2000);
    });
});
