<template>
    <v-dialog v-model="dialog"
              :fullscreen="$vuetify.breakpoint.smAndDown"
              width="650"
              content-class="white pa-8 supergraphic-stripe-dialog"
              :persistent="!dismissible">

      <div data-id="container-language-switcher-dialog">
        <v-row class="mb-8" justify="space-between">
          <h2 class="text-capitalize" data-id="text-language-switcher-dialog-header">
            {{ $t('navigation.languageSwitcher.dialog.title') }}
          </h2>
          <CDButtonIcon v-if="dismissible"
                        icon="$close"
                        @click="dialog = false"
                        class="mt-n3"
                        data-id="button-language-switcher-dialog-close"></CDButtonIcon>
        </v-row>
        <div class="mb-8" data-id="container-language-switcher-dialog-body">
          <v-row dense>
            <v-col cols="12">
              <span>
                {{ $t('navigation.languageSwitcher.dialog.selectCountryLabel') }}
              </span>
            </v-col>
            <v-col cols="12" lg="8">
              <CDSelect
                  :items="countries"
                  v-model="selectedCountry"
                  data-id="input-language-switcher-country">
                <template v-slot:selection="{ item }">
                  {{ countryLabel(item) }}
                </template>
                <template v-slot:item="{ item }">
                  {{ countryLabel(item) }}
                </template>
              </CDSelect>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col cols="12">
              <span>
                {{ $t('navigation.languageSwitcher.dialog.selectLanguageLabel') }}
              </span>
            </v-col>
            <v-col cols="12" lg="8">
              <CDSelect
                  :items="languagesForCountry(selectedCountry)"
                  v-model="selectedLanguage"
                  hide-details
                  data-id="input-language-switcher-language">
                <template v-slot:selection="{ item }">
                  {{ languageLabel(item) }}
                </template>
                <template v-slot:item="{ item }">
                  {{ languageLabel(item) }}
                </template>
              </CDSelect>
            </v-col>
            <v-col cols="12">
              <CDButtonText
                  color="primary"
                  @click="setLanguageToDefault">
                {{ $t('navigation.languageSwitcher.dialog.setLanguageToDefault', {default: languageLabel(globalDefaultLanguage)}) }}
              </CDButtonText>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <span v-if="selectedCountry && selectedLanguage">
                {{
                  $t(
                      'navigation.languageSwitcher.dialog.visitMarketPlaceInCountry',
                      {
                        country: countryLabel(selectedCountry),
                        language: languageLabel(selectedLanguage)
                      }
                  )
                }}
              </span>
            </v-col>
          </v-row>
        </div>

        <div data-id="container-language-switcher-dialog-footer">
          <v-row justify="end">
            <CDButton
                color="primary"
                data-id="button-language-switcher-dialog-submit"
                :disabled="!selectedCountry || !selectedLanguage"
                @click="goToMarketPlaceForCountry">
              {{ $t('navigation.languageSwitcher.dialog.confirm') }}
            </CDButton>
          </v-row>
        </div>
      </div>
    </v-dialog>
</template>

<script lang="ts">
import {Component, ModelSync, Prop, Vue, Watch} from 'vue-property-decorator';
import {TranslateResult} from 'vue-i18n';
import {lowerCase} from 'lodash';
import {useCoreDataStore} from 'shop/store/coreData';
import {ApiCountryData} from 'common/generated-types/types';
import {i18nService, messageService} from 'common/services';

@Component
export default class LanguageSwitcherDialog extends Vue {
  @Prop() globalFallbackCountry!: string;
  @Prop() globalDefaultLanguage!: string;

  @ModelSync('value', 'input', { type: Boolean, default: false })
  dialog!: boolean;

  coreDataStore = useCoreDataStore();

  currentCountry = '';
  currentLanguage = '';
  selectedCountry = '';
  selectedLanguage = '';

  async mounted() {
    this.currentCountry = i18nService.getUserCountry();
    this.currentLanguage = i18nService.getUserLanguage();

    this.selectedCountry = this.currentCountry || this.globalFallbackCountry;
    this.selectedLanguage = this.currentLanguage;

    if (!this.currentCountry || !this.currentLanguage) {
      this.dialog = true;
    }
  }

  @Watch('selectedCountry')
  onChildChanged(country: string) {
    this.resetLanguageIfNotCompatibleWithCountry(country);
  }

  languagesForCountry(countryIso : string) : string[] {
      const countryData = this.getCountry(countryIso);
      return countryData && countryData.supportedLanguages || [];
  }

  getCountry(countryIso : string) : ApiCountryData | undefined {
    return this.coreDataStore.coreData?.activeCountries?.find(country => country.isocode === countryIso);
  }

  get countries() : string[] {
    return this.coreDataStore.coreData?.activeCountries?.map(country => country.isocode);
  }

  languageLabel(isoCode: string): TranslateResult {
    if (!isoCode) {
      return '';
    }
    return this.$t(`language.${lowerCase(isoCode)}`);
  }

  countryLabel(isoCode: string): TranslateResult {
    if (!isoCode) {
      return '';
    }
    return this.$t(`country.${isoCode}`);
  }

  goToMarketPlaceForCountry() {
    if (!this.selectedLanguage || !this.selectedCountry) {
      return;
    }
    i18nService.goToSelectedMarketPlace(this.selectedCountry, this.selectedLanguage)
        .then(() => this.handleStoreSwitchSuccess())
        .catch(error => {
            if (error.response && error.response.status === 400) {
                const generalError = error.response.data;
                if (generalError) {
                    messageService.error(this.$t(generalError) as string);
                } else {
                    messageService.error(this.$t('shop.error.validation') as string);
                }
            } else {
                messageService.error(this.$t('shop.error.backend') as string);
            }
        });
  }

  resetLanguageIfNotCompatibleWithCountry(countryIso: string) : void {
    if (!this.selectedLanguage) {
      return;
    }
    const countryData = this.getCountry(countryIso);
    if (!countryData) {
      return;
    }

    if (!countryData.supportedLanguages.includes(this.selectedLanguage)) {
      this.selectedLanguage = countryData.defaultLanguage;
    }
  }

  setLanguageToDefault() {
    this.selectedLanguage = this.globalDefaultLanguage;
  }

  get dismissible() : boolean {
    return !!this.currentCountry && !!this.currentLanguage;
  }

  private handleStoreSwitchSuccess(): void {
      this.$router.go(0);
  }
}
</script>

<style scoped lang="scss">
.v-dialog__content:has(.v-dialog--persistent) {
  backdrop-filter: blur(6px);
}
</style>
