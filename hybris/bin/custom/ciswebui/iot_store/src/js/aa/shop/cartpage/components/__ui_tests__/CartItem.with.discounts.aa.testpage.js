import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import CartItem from 'aa/shop/cartpage/components/CartItem';
import pinia from 'shop/store';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

Vue.use(VueI18n);

const CART = {
    appCode: 'A_12345678',
    sellerProductId: "1 687 P15 100",
    quantity: 6,
    logoUrl: '/sample-data/square-16.png',
    entryNumber: 0,
    productCode: 'productCode',
    productUrl: 'productUrl',
    productName: 'productName',
    companyName: 'companyName',
    licenseName: 'licenseName',
    licenseType: 'FULL',
    runtime: {
        code: 'runtime_full_3y'
    },
    itemPrice: {
        value: '9.60',
        symbol: 'EUR'
    },
    totalPrice: {
        value: '12.00',
        symbol: 'EUR'
    },
    addOnThl: "thlTestUserGroup",
    scalePrices: [
        {
            currencyIso: 'EUR',
            value: 12,
            priceType: 'BUY',
            formattedValue: '12.00',
            minQuantity: 1,
            maxQuantity: 4
        }, {
            currencyIso: 'EUR',
            value: 9.6,
            priceType: 'BUY',
            formattedValue: '9.60',
            minQuantity: 5,
            maxQuantity: 9
        }, {
            currencyIso: 'EUR',
            value: 8.4,
            priceType: 'BUY',
            formattedValue: '8.40',
            minQuantity: 10,
            maxQuantity: null
        }],
    specialOffer: false
};

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        CartItem
    },
    pinia,
    data: {
        cartItems: [CART]
    },
    template: `
      <test-page-root>
        <div style="padding: 20px">
          <cart-item v-for="item in cartItems" v-bind:item="item"
                     :key="item.productCode"
                     cart-code="test-cart">
          </cart-item>
        </div>
      </test-page-root>
    `
});
