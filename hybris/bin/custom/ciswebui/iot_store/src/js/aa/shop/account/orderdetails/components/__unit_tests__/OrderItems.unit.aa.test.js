import {mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import OrderItems from 'aa/shop/account/orderdetails/components/OrderItems';
import 'common/test-directive';
import orderDetailsWithInvoicesData from 'common/testtools/scenariosstore/aa/orderDetailsWithInvoicesData.json';
import {cloneDeep} from 'lodash';
import {BaseStores} from 'common/constants';

const mountOrderItems = (props) => mount(OrderItems, {
    mocks: {
        $t: () => {
        },
    },
    propsData: {
        orderEntries: props.orderEntries,
    }
});

describe('OrderItems', () => {
    let propsData = {};
    beforeEach(() => {
        propsData = cloneDeep({
            orderEntries: orderDetailsWithInvoicesData.entries
        });
    });

    it('displays order items', async () => {
        const wrapper = mountOrderItems(propsData);
        await wrapper.vm.$nextTick();

        const entries = wrapper.findAll('[data-id^="container-order-entry-"]');
        expect(entries.length).toEqual(2);
        const entryProductUrl = wrapper.find('[data-id="link-order-entry-product-url-0"]');
        expect(entryProductUrl.attributes('href')).toEqual('/p/A_00001007');
        const entryProductName = wrapper.find('[data-id="text-order-entry-product-name-0"]');
        expect(entryProductName.text()).toEqual('Smoke App');
        const entryAppCode = wrapper.find('[data-id="text-order-entry-seller-product-id-0"]');
        expect(entryAppCode.text()).toEqual('00001007P12840');
    });

});
