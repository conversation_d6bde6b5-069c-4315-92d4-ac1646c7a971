<template>
  <v-dialog v-model="dialog"
            v-if="displayOtherCompatibleHardware"
            :fullscreen="$vuetify.breakpoint.smAndDown"
            width="640"
            content-class="white pa-6 pa-md-8">

    <template v-slot:activator="{ on, attrs }">
      <CDButtonText
          small
          color="primary"
          :class="activatorClass"
          v-bind="attrs"
          v-on="on"
          data-id="button-other-compatible-hardware-dialog-activator">
        <template>
          {{
            $tc('shop.productsOverview.category.otherCompatibleHardwareDialog.activator', otherCompatibleHardwareCount)
          }}
        </template>
      </CDButtonText>
    </template>

    <v-card flat data-id="container-other-compatible-hardware-dialog-content">

      <v-card-title class="pa-0 mb-4">
        <h2 class="text-capitalize" data-id="text-other-compatible-hardware-dialog-header">
          {{ $t('shop.productsOverview.category.otherCompatibleHardwareDialog.title') }}
        </h2>
        <v-spacer></v-spacer>
        <CDButtonIcon icon="$close"
                      @click="dialog = false"
                      class="mt-n10 mr-n6"
                      data-id="button-other-compatible-hardware-dialog-close"></CDButtonIcon>
      </v-card-title>

      <v-row dense no-gutters>
        <v-col cols="12">
          <p>
            {{ $t('shop.productsOverview.category.otherCompatibleHardwareDialog.listDescription') }}
          </p>
          <v-list class="py-0">
            <v-row>
              <v-col cols="12" md="6" v-for="(hardware, i) in hardwareRequirements" :key="i">
              <v-list-item
                :value="hardware"
                :data-id="`container-hardware-requirement-${hardware.value}`">
                <v-list-item-icon v-if="hardware.iconUrl"
                                  class="my-0 mr-4" style="min-width: 44px">
                  <v-img :src="hardware.iconUrl"
                         width="44"
                         height="44"
                         eager>
                  </v-img>
                </v-list-item-icon>
                <v-list-item-title
                    :data-id="`text-hardware-requirement-${hardware.value}`">
                  {{ hardware.value }}
                </v-list-item-title>
              </v-list-item>
              </v-col>
            </v-row>
          </v-list>
        </v-col>
      </v-row>

      <v-card-actions data-id="container-other-compatible-hardware-dialog-actions">
        <v-row justify="end">
          <CDButton data-id="button-other-compatible-hardware-dialog-cancel"
                    @click="dialog = false"
                    color="primary">
            {{ $t('shop.productsOverview.category.otherCompatibleHardwareDialog.closeButton') }}
          </CDButton>
        </v-row>
      </v-card-actions>
    </v-card>

  </v-dialog>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {FeatureValueDTO} from 'common/generated-types/types';
import {ProductsOverview} from 'common/constants';

@Component
export default class OtherHardwareCompatibilityDialog extends Vue {
  @Prop() hardwareRequirements!: FeatureValueDTO[];
  @Prop() activatorClass?: string;

  dialog = false;

  get otherCompatibleHardwareCount(): number {
    const hardwareRequirementsLength = this.hardwareRequirements?.length;
    return hardwareRequirementsLength - ProductsOverview.MAX_HARDWARE_REQUIREMENTS_TO_DISPLAY_ON_TAB;
  }

  get displayOtherCompatibleHardware(): boolean {
    return !!this.otherCompatibleHardwareCount && this.otherCompatibleHardwareCount > 0;
  }
}
</script>
