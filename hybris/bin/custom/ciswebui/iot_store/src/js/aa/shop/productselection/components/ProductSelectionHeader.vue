<template>
  <v-container fluid
               class="pa-0"
               data-id="container-product-selection-header-content">
    <v-row class="ma-n2">
      <v-col class="pa-2">
        <h5>
          {{ $t('shop.productSelection.header.subtitle') }}
        </h5>
      </v-col>
    </v-row>
    <v-row class="mt-2 mx-n2 mb-n2">
      <v-col cols="auto"
             class="pa-2">
        <v-img :src="product.logoUrl"
               :max-width="56"
               class="rounded-circle logo">
        </v-img>
      </v-col>
      <v-col class="pa-2 d-flex flex-column justify-center">
        <h2 data-id="text-product-name">
          {{ product.name }}
        </h2>
        <p v-if="product.countryEulas?.length > 0"
           class="text-body-2">
          <a v-for="eula in product.countryEulas"
             :href="eula.url"
             target="_blank"
             class="mr-4">{{ eula.label }}</a>
        </p>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {SimpleProductData} from 'common/generated-types/types';

@Component({
  components: {}
})
export default class ProductSelectionHeader extends Vue {
  @Prop() product!: SimpleProductData;
}
</script>

<style lang="scss" scoped>
.logo {
  border: 1px solid var(--v-grey-lighten3);
}
</style>
