import productSelection from 'common/testtools/scenariosstore/aa/productSelection.json';
import productSelectionWithoutPrices from 'common/testtools/scenariosstore/aa/productSelectionWithoutPrices.json';
import productSelectionWithBundles from 'common/testtools/scenariosstore/aa/productSelectionWithBundles.json';
import productSelectionWithEulas from 'common/testtools/scenariosstore/aa/productSelectionWithEulas.json';
import productSelectionWithSpecialOffers from 'common/testtools/scenariosstore/aa/productSelectionWithSpecialOffers.json';
import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from 'common/testtools/inlineTestPageCreator';
import {breakpoints} from 'common/breakpoints';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';
import {testMap} from 'common/testtools/scenariosstore';
import coreDataDefault from 'common/testtools/scenariosstore/aa/coreDataDefault.json';
import {cookies_en_AT} from "common/testtools/testCookies";
import coreData from 'common/testtools/scenariosstore/aa/coreDataFromRest.json';

describe('Product Selection', () => {
    let data;
    const productCode = 'AA_04012910';
    const REST_URL = `/shop/api/products/${productCode}`;
    const productSelectionRestHandler = (
        request,
        productSelectionData = productSelection,
        responseCoreData = coreData,
        promotionConsentData = null
    ) => {
        if (request.url().includes(REST_URL)) {
            request.respond({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify(productSelectionData),
            });
        } else if (request.url().endsWith('/shop/api/coredata')) {
            request.respond({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify(responseCoreData),
            });
        } else if (request.url().includes('/shop/api/promotion/consent/')) {
            const mockedPromotionConsentData = promotionConsentData || {
                master: {
                    licenseCode: 'AA_04012910_sub',
                    price: 2000,
                    discountPrice: 1000,
                },
                thl: {
                    licenseCode: 'some_thl_license_code',
                    price: 500,
                    discountPrice: 250,
                },
            };
            request.respond({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify(mockedPromotionConsentData),
            });
        } else {
            request.continue();
        }
    };

    beforeEach(async () => {
        data = cloneDeep(testMap.ProductSelectionAA(productCode));
        await page.setRequestInterception(true);
        await page.setCookie(...cookies_en_AT);
    });

    it('looks as expected', async () => {
        page.on('request', (request) => productSelectionRestHandler(request));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);
    });

    it('userGroup for user company and product are different', async () => {
        page.on('request', (request) => productSelectionRestHandler(request));
        data.coreData.currentCompany.userGroup = 'thlTestUserGroupNot';

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);
    });

    it('looks as expected with company under review note is visible', async () => {
        page.on('request', (request) => productSelectionRestHandler(request));
        data.coreData.userCompanyUnderReview=true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);
    });

    it('looks as expected with company under review note learn more is visible', async () => {
        page.on('request', (request) => productSelectionRestHandler(request));
        data.coreData.userCompanyUnderReview=true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        if (await page.$('[data-id="account-under-review-learn-more"]') !== null) {
            await page.click('[data-id="account-under-review-learn-more"]');
        }

        await Sizes.testAllScreenSizes(2000);
    });

    it('runtime selection as expected', async () => {
        page.on('request', (request) => productSelectionRestHandler(request));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-runtime-card-runtime_subs_unlimited"]');
        await page.waitFor(1000);

        await Sizes.testAllScreenSizes(2000);
    });

    it('empty summary as expected', async () => {
        page.on('request', (request) => productSelectionRestHandler(request));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();
        await page.waitFor(1000);

        await page.click('[data-id="product-selection-summary-header"]');

        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });

    it('looks as expected accept discount', async () => {
        page.on('request', (request) => productSelectionRestHandler(request));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-runtime-card-runtime_subs_unlimited"]');
        await page.click('[data-id="button-add-variant-AA_04012910_sub"]');
        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });

    it('looks as expected accept discount accepted', async () => {
        const mockedPromotionConsentData = {
            master: {
                licenseCode: 'AA_04012910_sub',
                price: 2000,
                discountPrice: 1000,
            },
            thl: {
                licenseCode: 'some_thl_license_code',
                price: 500,
                discountPrice: 250,
            },
        };
        page.on('request', (request) =>
            productSelectionRestHandler(
                request,
                productSelection,
                coreData,
                mockedPromotionConsentData
            )
        );

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-runtime-card-runtime_subs_unlimited"]');
        await page.click('[data-id="button-add-variant-AA_04012910_sub"]');
        await page.waitFor(1000);

        const confirmButton = '[data-id="button-submit"]';
        if (await page.$(confirmButton) !== null) {
            await page.click(confirmButton);
            await page.waitFor(1000);
        }

        await page.click('[data-id="product-selection-summary-header"]');
        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });

    it('looks as expected accept discount accepted with bundles', async () => {
        const mockedPromotionConsentData = {
            master: {
                licenseCode: 'AA_04012910_sub',
                price: 2000,
                discountPrice: 1000,
            },
            thl: {
                licenseCode: 'some_thl_license_code',
                price: 500,
                discountPrice: 250,
            },
        };
        page.on('request', (request) =>
            productSelectionRestHandler(
                request,
                productSelectionWithBundles,
                coreData,
                mockedPromotionConsentData
            )
        );

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-runtime-card-runtime_subs_unlimited"]');
        await page.click('[data-id="button-add-variant-AA_04012910_sub"]');
        await page.waitFor(1000);

        const confirmButton = '[data-id="button-submit"]';
        if (await page.$(confirmButton) !== null) {
            await page.click(confirmButton);
            await page.waitFor(1000);
        }

        await page.click('[data-id="product-selection-summary-header"]');
        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });

    it('non empty summary as expected', async () => {
        page.on('request', (request) => productSelectionRestHandler(request));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-runtime-card-runtime_subs_unlimited"]');
        await page.click('[data-id="button-add-variant-AA_04012910_sub"]');
        await page.waitFor(1000);
        await page.click('[data-id="button-submit"]');
        await page.waitFor(1000);
        await page.click('[data-id="product-selection-summary-header"]');
        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);

        await page.setViewport({width: breakpoints.M, height: 2000});
        await page.waitFor(1000);
        await page.hover('[data-id="product-selection-summary-price"]');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('when hovering over a info icon');
    });

    it('with bundles looks as expected', async () => {
        page.on('request', (request) => productSelectionRestHandler(request, productSelectionWithBundles));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-runtime-card-runtime_full_3y"]');

        await Sizes.testAllScreenSizes(2000);
    });

    it('summary with bundles looks as expected', async () => {
        page.on('request', (request) => productSelectionRestHandler(request, productSelectionWithBundles));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-runtime-card-runtime_full_3y"]');
        await page.click('[data-id="button-add-variant-AA_04012910_full_3"]');
        await page.click('[data-id="product-selection-summary-header"]');

        await Sizes.testAllScreenSizes(2000);
    });

    it('with EULAs looks as expected', async () => {
        page.on('request', (request) => productSelectionRestHandler(request, productSelectionWithEulas));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);
    });

    it('when license non purchasable add button is disabled', async () => {
        const productSelectionData = cloneDeep(productSelection);
        productSelectionData.licenses[1].purchasability = 'UNAVAILABLE';

        page.on('request', (request) => productSelectionRestHandler(request, productSelectionData));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);

        await page.hover('[data-id="button-add-variant-AA_04012910_sub"]');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('when hovering over a button');
    });

    it('when imported company non eligible to buy licenses add button is disabled', async () => {
        const productSelectionData = cloneDeep(productSelection);
        productSelectionData.licenses[1].purchasability = 'IMPORTED_COMPANY_NOT_ELIGIBLE';

        page.on('request', (request) => productSelectionRestHandler(request, productSelectionData));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);

        await page.hover('[data-id="button-add-variant-AA_04012910_sub"]');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('when hovering over a button');
    });

    it('when account is managed the variant section is hidden', async () => {
        let productSelectionData = cloneDeep(productSelectionWithoutPrices);

        let coreData = cloneDeep(coreDataDefault);
        coreData.moduleConfig.DIRECT_SALES = true;
        coreData.currentCompany.managedAccount = true;

        page.on('request', (request) => productSelectionRestHandler(request, productSelectionData));
        await createInlineUiTestPage({...data, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });

    it('when user is anonymous the variant section and summary are not displayed', async () => {
        let productSelectionData = cloneDeep(productSelectionWithoutPrices);

        let coreData = cloneDeep(coreDataDefault);
        coreData.moduleConfig.DIRECT_SALES = true;
        coreData.userName = null;

        page.on('request', (request) => productSelectionRestHandler(request, productSelectionData));
        await createInlineUiTestPage({...data, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });

    it('when variants have no prices the variants are displayed with no price labels', async () => {
        const productSelectionData = cloneDeep(productSelectionWithBundles);
        productSelectionData.licenses.map(license => {
            license.purchasability = 'UNAVAILABLE';
            license.price= null;
        });

        page.on('request', (request) => productSelectionRestHandler(request, productSelectionData));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });

    it('with special offer variants looks as expected', async () => {
        const productSelectionData = cloneDeep(productSelectionWithSpecialOffers);
        productSelectionData.licenses.map(license => {
            license.specialOffer = true;
        });

        page.on('request', (request) => productSelectionRestHandler(request, productSelectionData));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });

    it('with only one special offer variant in runtime looks as expected', async () => {
        const productSelectionData = cloneDeep(productSelectionWithSpecialOffers);
        productSelectionData.licenses
            .filter(license => license.licenseType.code === 'SUBSCRIPTION')[0].specialOffer = false;

        page.on('request', (request) => productSelectionRestHandler(request, productSelectionData));

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(1000);
        await Sizes.testAllScreenSizes(2000);
    });
});
