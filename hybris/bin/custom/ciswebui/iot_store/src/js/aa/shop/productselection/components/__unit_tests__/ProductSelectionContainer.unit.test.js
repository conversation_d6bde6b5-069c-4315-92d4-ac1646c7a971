import 'common/testtools/unit_tests_mock'
import 'common/test-directive'
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils'
import ProductSelectionContainer from '../ProductSelectionContainer'
import productSelection from 'common/testtools/scenariosstore/aa/productSelection.json'
import productSelectionWithoutPrices from 'common/testtools/scenariosstore/aa/productSelectionWithoutPrices.json'
import { useStoreRootStore } from 'aa/shop/store/storeRoot'
import { cloneDeep } from 'lodash'

jest.mock('common/util')

const mountProductSelectionContainer = (product) => wrapperComponentFactory(ProductSelectionContainer, {
  props: {
    product: product
  },
  shallow: false
})

const storeRootStore = useStoreRootStore()

const defaultCoreData = {
  moduleConfig: {
    DIRECT_SALES: true
  },
  currentCompany: {
    managedAccount: false
  },
  userName: 'auth_user',
}

describe('ProductSelectionContainer', () => {
  let product
  let coreData

  beforeEach(() => {
    product = productSelection
    coreData = cloneDeep(defaultCoreData)
    storeRootStore.coreData = coreData
    window.frontendData.coreData = coreData
  })

  describe('variants', () => {
    it('renders runtime selection and variant selection sections', async () => {
      const wrapper = await mountProductSelectionContainer(productSelectionWithoutPrices)
      await new Promise(process.nextTick)

      expect(wrapper.find('[data-id="text-runtime-selection-title"]').exists()).toBeTruthy()
      expect(wrapper.find('[data-id="text-variant-selection-title"]').exists()).toBeTruthy()
    })

    it('returns variants grouped by runtime', async () => {
      const wrapper = await mountProductSelectionContainer(product)

      const variantsPerRuntime = wrapper.vm.variantsPerRuntime

      expect(Object.keys(variantsPerRuntime)).toEqual(['runtime_full_unlimited', 'runtime_subs_unlimited'])
      expect(variantsPerRuntime['runtime_subs_unlimited']).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ code: 'AA_04012910_sub' })
        ])
      )
      expect(variantsPerRuntime['runtime_full_unlimited']).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ code: 'AA_04012910_full' })
        ])
      )
    })

    it('does not show the variant selection section if is managed account', async () => {
      coreData.currentCompany.managedAccount = true

      const wrapper = await mountProductSelectionContainer(productSelectionWithoutPrices)

      expect(wrapper.vm.showVariantSelection).toBeFalsy()
      expect(wrapper.find('[data-id="text-runtime-selection-title"]').exists()).toBeTruthy()
      expect(wrapper.find('[data-id="text-variant-selection-title"]').exists()).toBeFalsy()
    })

    it('does not show the variant selection section if is anonymous account', async () => {
      coreData.userName = null

      const wrapper = await mountProductSelectionContainer(productSelectionWithoutPrices)

      expect(wrapper.vm.showVariantSelection).toBeFalsy()
      expect(wrapper.find('[data-id="text-runtime-selection-title"]').exists()).toBeTruthy()
      expect(wrapper.find('[data-id="text-variant-selection-title"]').exists()).toBeFalsy()
    })

  })

})
