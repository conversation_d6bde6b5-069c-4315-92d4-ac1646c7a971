import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import coreDataDefault from 'common/testtools/scenariosstore/aa/coreDataDefault.json';

Vue.use(VueI18n);
Vue.use(pinia);

window.frontendData.coreData = coreDataDefault;
window.frontendData.coreData.currentCompany.managedAccount = true;

const storeRootStore = useStoreRootStore();
storeRootStore.coreData = coreDataDefault;
storeRootStore.coreData.moduleConfig.DIRECT_SALES = true;

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import CategoryProductList from 'aa/shop/productsoverview/components/categorydetails/CategoryProductList.vue';
import cat401Category from 'common/testtools/scenariosstore/aa/cat401Category.json';

new Vue({
    el: '#vue-app',
    pinia,
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        CategoryProductList
    },
    data: {
        category: cat401Category
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <category-product-list
              :category="category">
          </category-product-list>
        </div>
      </test-page-root>
    `
});
