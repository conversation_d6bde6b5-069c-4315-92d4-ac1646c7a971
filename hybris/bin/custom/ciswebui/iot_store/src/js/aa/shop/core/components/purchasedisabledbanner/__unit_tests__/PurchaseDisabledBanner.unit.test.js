import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import PurchaseDisabledBanner from 'aa/shop/core/components/purchasedisabledbanner/PurchaseDisabledBanner.vue';

jest.mock('common/util');

const mountComponent = () => {
    return wrapperComponentFactory(PurchaseDisabledBanner, {
        mocks: {},
        shallow: false
    });
};

describe('PurchaseDisabledBanner', () => {
    it('renders banner correctly', () => {
        const wrapper = mountComponent();

        expect(wrapper.find('[data-id="container-purchase-disabled-banner"]')).toBeTruthy();
        expect(wrapper.find('[data-id="text-banner-content"]').text()).toBe(('shop.purchaseDisabledBanner.text'));
    });
});

