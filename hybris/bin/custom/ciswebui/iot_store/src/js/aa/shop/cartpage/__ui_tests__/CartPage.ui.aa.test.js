import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';
import {cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";
import coreData from "common/testtools/scenariosstore/aa/coreDataFromRest.json";
import {breakpoints} from "common/breakpoints";

describe('cart page', () => {

  beforeEach(async () => {
    await page.setRequestInterception(true);
    page.on('request', coreDataRestHandler);
    await page.setCookie(...cookies_en_AT);
  });

  it('shows company cart', async () => {

    const data = cloneDeep(testMap.CartAA);
    await createInlineUiTestPage(data);
    await acceptCookieBanner();

    await Sizes.testExtraLargeScreensize(1472);
    await Sizes.testLargeScreensize(2054);
    await Sizes.testMediumScreensize(2095);
    await Sizes.testSmallScreensize(2307);
  });

  it('shows company cart and company under review note', async () => {

    const data = cloneDeep(testMap.CartAA);
    data.coreData.userCompanyUnderReview=true;

    await createInlineUiTestPage(data);
    await acceptCookieBanner();

    await Sizes.testExtraLargeScreensize(1472);
    await Sizes.testLargeScreensize(2054);
    await Sizes.testMediumScreensize(2095);
    await Sizes.testSmallScreensize(2307);
  });

  it('shows empty cart', async () => {

    const data = cloneDeep(testMap.CartEmptyAA);
    await createInlineUiTestPage(data);
    await acceptCookieBanner();

    await Sizes.testExtraLargeScreensize(1400);
    await Sizes.testLargeScreensize(1400);
    await Sizes.testMediumScreensize(1400);
    await Sizes.testSmallScreensize(1400);
  });

  describe('Cart items', () => {
    it('display bundle information when available', async () => {
      const data = cloneDeep(testMap.CartAA);
      data.pageData[0].cartItems[0].bundleInfo = {
        code: 'BI_M_3',
        name: 'M',
        size: 3
      }
      await createInlineUiTestPage(data);
      await acceptCookieBanner();
      await resetMouseCoords();

      await Sizes.testAllScreenSizes(2000);
    });

    it('with special offer chip look as expected', async () => {
      const data = cloneDeep(testMap.CartAA);
      data.pageData[0].cartItems.map(item => {
        item.specialOffer = true;
      });
      await createInlineUiTestPage(data);
      await acceptCookieBanner();
      await resetMouseCoords();

      await Sizes.testAllScreenSizes(2000);
    });
  });
});

describe('Cart interactions', () => {
    beforeEach(async () => {
      const cartResourcesRestHandler = request => {
        if (request.url().includes('/shop/api/cart')) {
            request.respond({
                status: 200,
                contentType: 'application/json',
                body: ''
            });
        } else if (request.url().endsWith('/shop/api/coredata')) {
          request.respond({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(coreData)
          });
        } else {
          request.continue();
        }
      };
      await page.setRequestInterception(true);
      page.on('request', cartResourcesRestHandler);
      await page.setCookie(...cookies_en_AT);
    });

    afterEach(async () => {
      await page.setRequestInterception(false);
    });

    it('notification after item deletion', async () => {
      const data = cloneDeep(testMap.CartAA);
      await createInlineUiTestPage(data);
      await acceptCookieBanner();

      await page.click('[data-id="icon-delete-action-0"]');
      await page.waitFor(500);

      await Sizes.testLargeScreensize(2000);
    });
})
