<template>
  <v-container fluid class="pa-0 mt-14 mb-10">
    <v-row class="ma-n2 ma-md-n3 pb-6 pb-md-5">
      <v-col class="pa-2 pa-md-3">
        <h2 data-id="text-variant-selection-title">
          {{
            bundleVariants
                ? $t('shop.productSelection.variantSelection.variant.titleBundles')
                : $t('shop.productSelection.variantSelection.variant.titleNonBundles')
          }}
        </h2>
      </v-col>
    </v-row>

    <v-row class="ma-n2 ma-md-n3">
      <v-col v-for="variant in sortedVariants"
             :key="variant.code"
             class="pa-2 pa-md-3"
             cols="12"
             md="4"
             lg="3">
        <variant-item :variant="variant"
                      v-on="$listeners"
                      :discount-available="discountAvailable"
                      :data-id="`container-variant-item-${variant.code}`">
        </variant-item>
      </v-col>
    </v-row>

  </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ProductLicenseData} from 'common/generated-types/types';
import VariantItem from 'aa/shop/productselection/components/VariantItem.vue';
import {sortBy} from 'lodash';

@Component({
  components: {VariantItem}
})
export default class VariantSelection extends Vue {
  @Prop() variants!: Array<ProductLicenseData>;
  @Prop({default: false}) discountAvailable!: boolean;

  get bundleVariants(): boolean {
    return this.variants?.some(variant => !!variant.bundleInfo?.code);
  }

  get sortedVariants(): ProductLicenseData[] {
    return sortBy(this.variants, (variant) => variant.bundleInfo.size);
  }
}
</script>
