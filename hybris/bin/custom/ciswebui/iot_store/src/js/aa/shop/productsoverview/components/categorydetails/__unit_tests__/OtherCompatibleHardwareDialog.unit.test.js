import 'common/test-directive';
import OtherCompatibleHardwareDialog from 'aa/shop/productsoverview/components/categorydetails/OtherCompatibleHardwareDialog.vue';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import {ProductsOverview} from 'common/constants';

jest.mock('common/util');

const defaultHardwareRequirements = [
    {
        value: "KTS 560",
        iconUrl: "sample-data/aa/products/hardware-features/kts-560.png"
    },
    {
        value: "KTS 590",
        iconUrl: "sample-data/aa/products/hardware-features/kts-590.png"
    },
    {
        value: "KTS 465",
        iconUrl: "sample-data/aa/products/hardware-features/kts-465.png"
    },
    {
        value: "KTS 960",
        iconUrl: "sample-data/aa/products/hardware-features/kts-960.png"
    }
];

const mountOtherCompatibleHardwareDialog = (hardwareRequirements = defaultHardwareRequirements) => wrapperComponentFactory(OtherCompatibleHardwareDialog, {
    props: {
        hardwareRequirements
    },
    shallow: false
});

describe('OtherCompatibleHardwareDialog', () => {

    describe('dialog activator', () => {
        it('displayed by default', async () => {
            const wrapper = mountOtherCompatibleHardwareDialog();
            await new Promise(process.nextTick);

            const activator = wrapper.find('[data-id="button-other-compatible-hardware-dialog-activator"]');
            expect(activator.exists()).toBeTruthy();
            const dialogContent = wrapper.find('[data-id="container-other-compatible-hardware-dialog-content"]');
            expect(dialogContent.exists()).toBeFalsy();
        })

        it('text contains the other compatible hardware count', async () => {
            const wrapper = mountOtherCompatibleHardwareDialog();
            await new Promise(process.nextTick);

            const activator = wrapper.find('[data-id="button-other-compatible-hardware-dialog-activator"]');
            expect(activator.exists()).toBeTruthy();
            expect(activator.text()).toBe("shop.productsOverview.category.otherCompatibleHardwareDialog.activator");
        })

        it('opens the dialog when clicked', async () => {
            const wrapper = mountOtherCompatibleHardwareDialog();
            await new Promise(process.nextTick);

            await wrapper.find('[data-id="button-other-compatible-hardware-dialog-activator"]').trigger('click');
            await new Promise(process.nextTick);

            const dialogContent = wrapper.find('[data-id="container-other-compatible-hardware-dialog-content"]');
            expect(dialogContent.exists()).toBeTruthy();
        })

        it('will not be displayed if no requirements to display', async () => {
            const wrapper = mountOtherCompatibleHardwareDialog([]);
            await new Promise(process.nextTick);

            const activator = wrapper.find('[data-id="button-other-compatible-hardware-dialog-activator"]');
            expect(activator.exists()).toBeFalsy();
        })

        it('will not be displayed if requirements count less or equal than max requirements to display on tab', async () => {
            const wrapper = mountOtherCompatibleHardwareDialog([{
                value: "KTS 560",
                iconUrl: ""
            }, {
                value: "KTS 590",
                iconUrl: ""
            }]);
            await new Promise(process.nextTick);

            const activator = wrapper.find('[data-id="button-other-compatible-hardware-dialog-activator"]');
            expect(activator.exists()).toBeFalsy();
        })
    })

    describe('dialog content', () => {
        it('contains the hardware requirements', async () => {
            const wrapper = mountOtherCompatibleHardwareDialog();
            await new Promise(process.nextTick);

            await wrapper.find('[data-id="button-other-compatible-hardware-dialog-activator"]').trigger('click');
            await new Promise(process.nextTick);

            const hardwareRequirements = wrapper.findAll('[data-id^="text-hardware-requirement-"]').wrappers;
            expect(hardwareRequirements.length).toBe(4);
            expect(hardwareRequirements.length).toBe(defaultHardwareRequirements.length);
            expect(hardwareRequirements.map(requirement => requirement.text())).toEqual(
                expect.arrayContaining(['KTS 560', 'KTS 590', 'KTS 465', 'KTS 960'])
            );
        })
    })

    describe('otherCompatibleHardwareCount', () => {
        it('returns the number of hardware requirements minus the max requirements to display on tab', async () => {
            const wrapper = mountOtherCompatibleHardwareDialog();
            await new Promise(process.nextTick);

            const otherCompatibleHardwareCount = wrapper.vm.otherCompatibleHardwareCount;
            expect(otherCompatibleHardwareCount).toEqual(defaultHardwareRequirements.length - ProductsOverview.MAX_HARDWARE_REQUIREMENTS_TO_DISPLAY_ON_TAB);
            expect(otherCompatibleHardwareCount).toEqual(2);
        })
    })
});
