import en from 'aa/shop/i18n/en.json';
import VueI18n, {
    type DateTimeFormats,
    type FallbackLocale,
    type LocaleMessages,
    type NumberFormatOptions,
    type NumberFormats
} from 'vue-i18n';
import {i18nService, messageService} from 'common/services';
import {castToVueI18n, createI18n, type I18nOptions} from 'vue-i18n-bridge';
import Vue from 'vue';

declare global {
    interface Window {
        frontendData: any;
    }
}

Vue.use(VueI18n, {bridge: true});

const messages: LocaleMessages = {
    en
};

const fallbackLocale: FallbackLocale = 'en';

const locales = ['en', 'de', 'da', 'el', 'es', 'et', 'fi', 'fr', 'hr', 'lt', 'lv', 'nl', 'no', 'pt', 'sl', 'sv'];

const createNumberFormat = (locale: string): NumberFormatOptions & { locale: string } => ({
    style: 'decimal',
    locale,
    maximumFractionDigits: 1,
});

const numberFormats: NumberFormats = {};
const datetimeFormats: DateTimeFormats = {};

locales.forEach((locale) => {
    numberFormats[locale] = {
        decimal: createNumberFormat(locale),
        price: {
            ...createNumberFormat(locale),
            maximumFractionDigits: 2,
            minimumFractionDigits: 2,
        },
    };

    datetimeFormats[locale] = {
        time: {
            hour: 'numeric',
            minute: 'numeric'
        },
        short: {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        },
        long: {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
        }
    }
});

const options = {
    legacy: false,
    locale: i18nService.getUserLanguage() || fallbackLocale,
    fallbackLocale,
    messages,
    datetimeFormats,
    numberFormats
} as I18nOptions;

const i18n = castToVueI18n(createI18n(options, VueI18n));

export async function loadLanguageAsync() {
    const locale = i18n.locale;
    const translationConfig = window.frontendData.coreData.translationConfig;
    if (locale === 'en' || !translationConfig.remoteTranslationsEnabled) {
        return Promise.resolve();
    }

    return Promise.all([
        i18nService.fetchTranslation(locale, translationConfig.baseUrl, translationConfig.projectId),
        i18nService.fetchTranslation(locale, translationConfig.baseUrl, translationConfig.cdsProjectId)
    ]).then(([storeTranslations, cdsTranslations]) => {
        i18n.mergeLocaleMessage(locale, {...storeTranslations, $vuetify: {...cdsTranslations}});
    }).catch(() => messageService.error('Could not fetch language data.'));
}

export {i18n};
