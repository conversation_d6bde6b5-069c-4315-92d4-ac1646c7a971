import * as Sizes from 'common/testtools/testScreenSizes';
import coreData from 'common/testtools/scenariosstore/aa/coreDataFromRest.json';

describe('switch to default store banner', () => {

    it('banner looks as expected', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-core-components-languageswitcher-__ui_tests__-SwitchToDefaultStoreBanner');

        await Sizes.testLargeScreensize(400);
        await Sizes.testSmallScreensize(400);
    });
});
