import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import LoginCtaBanner from 'aa/shop/core/components/loginctabanner/LoginCtaBanner.vue';
import {wrapperComponentFactory} from "common/testtools/unit-test-utils";

jest.mock('common/util');

const mountComponent = () => {
    return wrapperComponentFactory(LoginCtaBanner, {
        mocks: {},
        shallow: false
    });
};

window.frontendData = {
    coreData: {
        navigationItems: [
            {
                id: "storeLogin",
                url: "/shop/login",
                text: "Log in",
                itemCode: "storeLogin"
            }
        ],
    }
}

describe('LoginCtaBanner', () => {
    it('renders banner correctly', () => {
        const wrapper = mountComponent();

        expect(wrapper.find('[data-id="container-login-cta-banner"]')).toBeTruthy();
        expect(wrapper.find('[data-id="button-login-cta-banner"]')).toBeTruthy();
        expect(wrapper.find('[data-id="text-banner-content"]').text()).toBe(('shop.loginCtaBanner.text'));
    });
});

