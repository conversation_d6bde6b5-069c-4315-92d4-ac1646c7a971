<template>

  <CDNavbar2 v-model="storeHeaderStore.isActive"
             :title="storeHeaderStore.currentPageTitle"
             :home-link="homePageLink">
    <template #content-end v-if="!$route.meta?.useMinimalHeader">
      <template v-if="isUserAuthenticated">
        <authenticated-header-content></authenticated-header-content>
      </template>

      <template v-else>
        <unauthenticated-header-content></unauthenticated-header-content>
      </template>

    </template>

    <template #extension v-if="!$route.meta?.useMinimalHeader">
      <new-store-header-breadcrumbs v-if="!isMobile"></new-store-header-breadcrumbs>
      <v-spacer></v-spacer>
      <h4 class="text-h4">
        {{
          $t('navigation.headerExtensionTitle', {country: isMobile ? '' : currentCountyLabel})
        }}
      </h4>
    </template>

  </CDNavbar2>

</template>

<script setup lang="ts">
import {useStoreHeaderStore} from 'aa/shop/store/storeHeader';
import {ShopRoute} from 'common/constants';
import {computed} from 'vue';
import {i18nService, userPermissionService} from 'common/services';
import UnauthenticatedHeaderContent from 'aa/shop/core/components/header/UnauthenticatedHeaderContent.vue';
import AuthenticatedHeaderContent from 'aa/shop/core/components/header/AuthenticatedHeaderContent.vue';
import NewStoreHeaderBreadcrumbs from 'aa/shop/core/components/header/NewStoreHeaderBreadcrumbs.vue';
import {useI18n} from 'vue-i18n-bridge';
import {useVuetify} from 'aa/shop/composables/usevuetify';

const storeHeaderStore = useStoreHeaderStore();
const {t} = useI18n();
const vuetify = useVuetify();

const homePageLink = ShopRoute.HOME;

const isMobile = computed(() => vuetify?.breakpoint.mobile);

const isUserAuthenticated = computed(() => !userPermissionService.isAnonymous());

const currentCountyLabel = computed(() => {
  const countryIso = i18nService.getUserCountry();
  return t(`country.${countryIso}`);
});

</script>

<style scoped>

</style>
