<template>
    <div class="shop">
        <CDNavbar app
                  v-model="storeHeaderStore.isActive"
                  :title="pageLabel"
                  :home-link="baseUrl"
                  :has-supergraphic-stripe="true"
                  :hide-on-scroll="$route.meta?.navbarHideOnScroll"
                  id="store-header">

            <template v-slot:logo>
              <AaLogoSmall class="logo d-md-none logo--mobile"></AaLogoSmall>
              <AaLogo class="logo d-none d-md-flex logo--desktop"></AaLogo>
            </template>

            <template v-slot:content-center>
              <CDNavbarLink v-if="hasAppsLink" :href="appsLink.url" :isActive="appsActive" data-id="link-category-apps">
                  {{ translateNavigation(appsLink) }}
              </CDNavbarLink>
              <CDNavbarLink v-if="hasToolsLink" :href="toolsLink.url" :isActive="toolsActive" data-id="link-category-tools">
                  {{ translateNavigation(toolsLink) }}
              </CDNavbarLink>
            </template>

            <template v-slot:content-end>
                <template v-if="userPermissionService.isAnonymous()">
                    <div class="mr-4" v-if="isLanguageSwitcherFeatureOn">
                      <language-switcher-activator @click="languageSwitcherStore.dialogOpened = true"></language-switcher-activator>
                    </div>
                    <div v-if="loginEntry">
                        <CDButtonText id="login-header-link" :href="loginEntry.url" class="mr-2">
                            {{ translateNavigation(loginEntry) }}
                        </CDButtonText>
                        <CDButtonText :href="registerEntry.url" class="mr-4">{{
                                translateNavigation(registerEntry)
                            }}</CDButtonText>
                        <CDButton v-if="hasGetInTouchLink" :href="getInTouchLink.url" :target="getInTouchLink.target" color="primary">{{
                                translateNavigation(getInTouchLink)
                            }}</CDButton>
                    </div>
                </template>

                <template v-else>
                    <CDHomeSwitcher
                      :v-if="dmtNavigationItem"
                      :is-developer="false"
                      :is-integrator="true"
                      :dropdown-links="homeSwitcher"
                      :z-index="200">
                    </CDHomeSwitcher>
                    <CDSeparator></CDSeparator>
                    <a v-if="hasAccessToCart"
                       :href="cartPage"
                       class="mr-4 icon-cart"
                       data-id="link-cart-page">
                        <CDIcon icon="$cart"
                                color="black"
                                data-id="icon-cart"
                                :badge-content="totalQuantity > 0 ? totalQuantity : false"
                                :badge-value="totalQuantity > 0 ? true : false"
                                :badge-dot="totalQuantity > 0 ? false : true">
                        </CDIcon>
                    </a>
                    <CDFlyoutMenu data-id="flyout" class="icon-user" :header="CDFlyoutHeader" :z-index="200" nudge-bottom="8" nudge-right="38">
                        <template v-for="item in headerEntries">
                          <CDFlyoutMenuItem v-if="'onClick' in item"
                                            :key="item.id"
                                            :title="item.text"
                                            :icon="item.icon"
                                            :highlight="item.highlight"
                                            :has-divider="item.hasDivider"
                                            @cdClick="item.onClick">
                          </CDFlyoutMenuItem>
                          <CDFlyoutMenuItem v-else
                                            :key="item.id"
                                            :title="item.text"
                                            :href="item.href"
                                            :target="item.target"
                                            :icon="item.icon"
                                            :highlight="item.highlight"
                                            :has-divider="item.hasDivider">
                          </CDFlyoutMenuItem>
                        </template>
                    </CDFlyoutMenu>
                </template>
            </template>

            <template v-slot:content-end-mobile>
                <template v-if="userPermissionService.isAnonymous()">
                    <div class="mr-2" v-if="isLanguageSwitcherFeatureOn">
                      <language-switcher-activator @click="languageSwitcherStore.dialogOpened = true"></language-switcher-activator>
                    </div>
                    <CDButton v-if="hasGetInTouchLink" :href="getInTouchLink.url" :target="getInTouchLink.target" color="primary" class="mr-2">{{ navigationService.translate($i18n, getInTouchLink) }}</CDButton>
                    <CDFlyoutMenu v-bind="FlyoutMenuFormatting"
                                  :hasIcons="false"
                                  menuIcon="$user">
                        <template v-for="item in mobileMenu">
                            <CDFlyoutMenuItem v-if="'onClick' in item"
                                              :key="item.id"
                                              :title="item.text"
                                              :highlight="item.highlight"
                                              :has-divider="item.hasDivider"
                                              @cdClick="item.onClick">
                            </CDFlyoutMenuItem>
                            <CDFlyoutMenuItem v-else
                                              :key="item.id"
                                              :title="item.text"
                                              :href="item.href"
                                              :target="item.target"
                                              :highlight="item.highlight"
                                              :has-divider="item.hasDivider">
                            </CDFlyoutMenuItem>
                        </template>
                    </CDFlyoutMenu>
                </template>

                <template v-else>
                    <CDButtonIcon :v-if="dmtNavigationItem" icon="$home" :href="dmtNavigationItem.url" color="black"></CDButtonIcon>
                    <CDSeparator></CDSeparator>
                    <a v-if="hasAccessToCart"
                       :href="cartPage"
                       class="mr-4">
                        <CDIcon icon="$cart"
                                color="black"
                                :badge-content="totalQuantity > 0 ? totalQuantity : false"
                                :badge-value="totalQuantity > 0 ? true : false"
                                :badge-dot="totalQuantity > 0 ? false : true">
                        </CDIcon>
                    </a>
                    <CDFlyoutMenu v-bind="FlyoutMenuFormatting" :header="CDFlyoutHeader" :z-index="200">
                        <CDFlyoutMenuItem v-if="hasAppsLink" :href="appsLink.url" :title="translateNavigation(appsLink)"></CDFlyoutMenuItem>
                        <CDFlyoutMenuItem v-if="hasToolsLink" :href="toolsLink.url" :title="translateNavigation(toolsLink)"></CDFlyoutMenuItem>
                        <template v-for="item in headerEntries">
                            <CDFlyoutMenuItem v-if="'onClick' in item"
                                              :key="item.id"
                                              :title="item.text"
                                              :icon="item.icon"
                                              :highlight="item.highlight"
                                              :has-divider="item.hasDivider"
                                              @cdClick="item.onClick">
                            </CDFlyoutMenuItem>
                            <CDFlyoutMenuItem v-else
                                              :key="item.id"
                                              :title="item.text"
                                              :href="item.href"
                                              :target="item.target"
                                              :icon="item.icon"
                                              :highlight="item.highlight"
                                              :has-divider="item.hasDivider">
                            </CDFlyoutMenuItem>
                        </template>
                    </CDFlyoutMenu>
                </template>
            </template>

        </CDNavbar>
    </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {FadeTransition} from 'common/components';
import {NavigationItemData, NavigationItemGroup, NavigationItemType} from 'common/generated-types/types';
import {CDNavigationItem, EventNavigationItem, HomeSwitcherItems} from 'common/types';
import {navigationService, userPermissionService} from 'common/services';
import {isNil, sortBy} from 'lodash';
import {CategoriesHeader} from 'shop/core/components/productcategories';
import AaLogo from 'common/images/boxed/aa-logo.svg';
import AaLogoSmall from 'common/images/boxed/aa-sm.svg';
import {LanguageSwitcherActivator} from 'aa/shop/core/components/languageswitcher';
import {shopCoreDataProvider} from 'common/provider';
import {useCartStore} from 'shop/store/cart';
import {useStoreHeaderStore} from 'aa/shop/store/storeHeader';
import {useLanguageSwitcherStore} from 'aa/shop/store/languageSwitcher';

@Component({
        components: {
            LanguageSwitcherActivator,
            FadeTransition,
            CategoriesHeader,
            AaLogo,
            AaLogoSmall
        }
    })
    export default class StoreHeader extends Vue {
        @Prop() camerasUrl!: string;
        @Prop() myAppsUrl!: string;
        @Prop() baseUrl!: string;
        @Prop() userName!: string;
        @Prop() numberOfCartItems!: number;
        @Prop() pageTitle!: string;
        @Prop() homeTitle!: string;

        cartStore = useCartStore();
        storeHeaderStore = useStoreHeaderStore();

        pageLabel = '';
        homeLabel = '';
        devconLabel = '';
        userPermissionService = userPermissionService;
        navigationService = navigationService;

        languageSwitcherStore = useLanguageSwitcherStore();
        isLanguageSwitcherFeatureOn = Boolean(shopCoreDataProvider.data.moduleConfig.LANGUAGE_SWITCHER);

        mounted(): void {
            this.pageLabel = this.pageTitle || this.$t('navigation.storeTitle') as string;
            this.homeLabel = this.homeTitle || this.$t('navigation.deviceManagementPortal') as string;
            this.cartStore.incrementQuantity(this.numberOfCartItems);
        }

        CDFlyoutHeader = {
            text: this.userName
        };

        FlyoutMenuFormatting = {
            left: false,
            nudgeTop: -8,
            zIndex: 200
        };

        translateNavigation(navigationItem : NavigationItemData) : string {
            return navigationService.translate(this.$i18n, navigationItem);
        }

        get homeSwitcher (): HomeSwitcherItems {
            return {
                devcon: {
                    title: this.devconLabel,
                    href: this.camerasUrl,
                    key: 'devcon'
                },
                dmt: {
                    title: this.translateNavigation(this.dmtNavigationItem),
                    href: this.dmtNavigationItem.url,
                    key: 'dmt'
                }
            };
        }

        get headerEntries(): Array<CDNavigationItem | EventNavigationItem> {
            return sortBy(this.navigationService.byGroup(NavigationItemGroup.HEADER), 'index')
                .filter(item => item.id !== 'aa_storeRegionAndLanguage' || this.isLanguageSwitcherFeatureOn)
                .map(item => {
                  if (item.id === 'aa_storeRegionAndLanguage') {
                    return {
                      id: item.id,
                      text: this.translateNavigation(item),
                      icon: this.getIconForGlobalNavItem(item),
                      onClick: this.openLanguageSwitcherDialog,
                      highlight: false
                    };
                  } else {
                    return {
                      id: item.id,
                      text: this.translateNavigation(item),
                      icon: this.getIconForGlobalNavItem(item),
                      href: item.url,
                      target: item.target,
                      highlight: item.id === 'aa_storeSignOut',
                      hasDivider: ['aa_storeOrderHistory', 'aa_globalSupport'].includes(item.id)
                    };
                  }
                });
        }

        openLanguageSwitcherDialog(): void {
          this.languageSwitcherStore.dialogOpened = true;
        }

      /**
       * Ensures that Global Navigation Items icon names are prefixed with the '$' sign.
       * Global Navigation Items are used by the Store, Devcon and KPI.
       * Both the Devcon and KPI still use the io-components flyout, which expects io-components compatible icon names.
       *
       * @param navigationItem
       */
        getIconForGlobalNavItem(navigationItem: NavigationItemData): string {
            if (navigationItem.type !== NavigationItemType.GLOBAL) {
              return navigationItem.icon;
            }
            // Cd-system icon names are prefixed with a '$' sign.
            if (!navigationItem.icon?.startsWith('$')) {
              return '$' + navigationItem.icon;
            }
            return navigationItem.icon;
        }

        get productCategories(): CDNavigationItem[] {
            return sortBy(this.navigationService.byGroup(NavigationItemGroup.PRODUCT_CATEGORIES),'index')
                .filter(item => !isNil(item))
                .map(item => ({
                    text: this.translateNavigation(item),
                    href: item.url,
                    target: item.target,
                    highlight: false
                }));
        }

        get marketingHeaders(): CDNavigationItem[] {
            return sortBy(this.navigationService.byGroup(NavigationItemGroup.MARKETING_HEADER),'index')
                .filter(item => !isNil(item))
                .map(item => ({
                    text: this.translateNavigation(item),
                    icon: 'mdi-account',
                    href: item.url,
                    target: item.target,
                    highlight: false
                }));
        }

        get cartPage(): string {
            return this.baseUrl + 'cart';
        }

        get loginEntry(): NavigationItemData { return this.navigationService.byItemCode('storeLogin'); }

        get registerEntry(): NavigationItemData { return this.navigationService.byItemCode('storeRegister'); }

        get dmtNavigationItem(): NavigationItemData { return this.navigationService.byItemCode('deviceManagement'); }

        get appsLink(): NavigationItemData { return this.navigationService.byItemCode('appProductCategory'); }

        get toolsLink(): NavigationItemData { return this.navigationService.byItemCode('toolProductCategory'); }

        get getInTouchLink(): NavigationItemData { return this.navigationService.byItemCode('getInTouch'); }

        get appsActive(): boolean {
            return this.navigationService.appsActive(window.location.href) || this.$route.meta?.appsActive === true;
        }

        get toolsActive(): boolean {
            return this.navigationService.toolsActive(window.location.href) || this.$route.meta?.toolsActive === true;
        }

        get hasAppsLink(): boolean {
            return !!this.appsLink?.url && !!this.appsLink?.text;
        }

        get hasToolsLink(): boolean {
            return !!this.toolsLink?.url && !!this.toolsLink?.text;
        }

        get hasGetInTouchLink(): boolean {
            return !!this.getInTouchLink?.url && !!this.getInTouchLink?.text;
        }

        get hasAccessToCart(): boolean {
            return userPermissionService.hasAccessToCart();
        }

        get mobileMenu(): (Array<CDNavigationItem | EventNavigationItem> | Record<string, unknown>) {
            return [
                ...this.headerEntries,
                ...this.productCategories,
                ...this.marketingHeaders
            ];
        }

        get totalQuantity() : number {
          return this.cartStore.totalQuantity;
        }
}
</script>

<style lang="scss" scoped>
    @import 'cd-system/tokens/scss/variables';
    :deep(.home-switcher-label) {
        font-family: $body-font-family !important;
    }
</style>
