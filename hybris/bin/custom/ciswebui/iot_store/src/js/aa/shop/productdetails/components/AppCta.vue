<template>
  <div v-if="!readOnly && isThirdPartyApp()" id="third-party-app-cta">
    <v-row>
      <v-col cols="12" class="d-md-flex">
        <div v-if="canPurchaseLicense(LicenseType.SUBSCRIPTION)"
             class="action-button purchase-button">
          <CDButton id="purchase"
                    data-license-type="subscription"
                    color="primary"
                    v-on:click="$parent.$emit('licenseButtonClicked', LicenseType.SUBSCRIPTION)">
            {{$t('shop.productDetails.productHeader.purchaseButton')}}
          </CDButton>
        </div>
        <div v-else-if="canPurchaseLicense(LicenseType.FULL)"
             class="action-button purchase-button">
          <CDButton id="purchase"
                    data-license-type="full"
                    color="primary"
                    v-on:click="$parent.$emit('licenseButtonClicked', LicenseType.FULL)">
            {{$t('shop.productDetails.productHeader.purchaseButton')}}
          </CDButton>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {find} from 'lodash';
import {ProductLicenseData} from 'common/generated-types/types';
import {LicenseType} from 'common/types';
import {userPermissionService} from 'common/services';

@Component
export default class AppCta extends Vue {
  @Prop() licenses!: ProductLicenseData[];
  @Prop() readOnly!: boolean;

  LicenseType = LicenseType;

  canPurchaseLicense(licenseType: LicenseType): boolean {
    const license = find(this.licenses, ['licenseType.code', licenseType]);
    return !!license && userPermissionService.canPurchaseLicense(license);
  }

  isThirdPartyApp(): boolean {
    const licenseTypes = this.licenses
        .map(l => l.licenseType.code);

    return !licenseTypes.includes(LicenseType.TOOL);
  }
}
</script>

<style lang="scss">
  .action-button .v-btn {
    width: 100%;
  }
</style>
