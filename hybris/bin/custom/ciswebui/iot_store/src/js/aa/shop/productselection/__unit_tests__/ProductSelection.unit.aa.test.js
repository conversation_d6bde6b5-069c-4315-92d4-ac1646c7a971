import 'common/testtools/unit_tests_mock'
import 'common/test-directive'
import {localVue, wrapperComponentFactory} from 'common/testtools/unit-test-utils'
import ProductSelection from '../ProductSelection.vue'
import productSelection from 'common/testtools/scenariosstore/aa/productSelection.json'
import {cloneDeep} from 'lodash'
import {useProductSelectionStore} from 'shop/store/productSelection'
import {CDQuantity} from 'cd-system'

jest.mock('common/util')

localVue.component('CDQuantity', CDQuantity)

const productCode = 'AA_04012910'
const mountProductSelection = () => wrapperComponentFactory(ProductSelection, {
  props: {
    productCode: productCode
  },
  shallow: false
})

const productSelectionStore = useProductSelectionStore()
productSelectionStore.product = productSelection

const defaultCoreData = {
  userName: 'auth_user',
  currentCompany: {
    managedAccount: false,
    userGroup: 'thlTestUserGroup'
  },
  currentCountry: {
    isocode: 'DE'
  }
}

describe('ProductSelection', () => {
  let coreData

  beforeEach(() => {
    coreData = cloneDeep(defaultCoreData)
    window.frontendData.coreData = coreData
  })

  describe('selection summary', () => {
    it('is displayed', async () => {
      const wrapper = await mountProductSelection()
      await new Promise(process.nextTick)

      expect(wrapper.vm.displaySelectionSummary).toBeTruthy()
      expect(wrapper.find('[data-id="container-product-selection-summary"]').exists()).toBeTruthy()
    })

    it('is not displayed when user is anonymous', async () => {
      coreData.userName = null

      const wrapper = await mountProductSelection()
      await new Promise(process.nextTick)

      expect(wrapper.vm.displaySelectionSummary).toBeFalsy()
      expect(wrapper.find('[data-id="container-product-selection-summary"]').exists()).toBeFalsy()
    })

    it('is not displayed when company is managed account', async () => {
      coreData.currentCompany.managedAccount = true

      const wrapper = await mountProductSelection()
      await new Promise(process.nextTick)

      expect(wrapper.vm.displaySelectionSummary).toBeFalsy()
      expect(wrapper.find('[data-id="container-product-selection-summary"]').exists()).toBeFalsy()
    })

  })

  describe('quantity input', () => {
    it('reflects value in store', async () => {
      const license = productSelection.licenses.find(l => l.code === 'AA_04012910_sub')

      const wrapper = await mountProductSelection()
      await new Promise(process.nextTick)

      const quantity = wrapper.findComponent(`[data-id="button-add-variant-${license.code}"]`)

      expect(productSelectionStore.items).toEqual([])
      expect(quantity.vm.value).toEqual(0)

      productSelectionStore.items = [
        {...license, quantity: 1}
      ]
      await new Promise(process.nextTick)

      expect(quantity.vm.value).toEqual(1)
    })
  })

})
