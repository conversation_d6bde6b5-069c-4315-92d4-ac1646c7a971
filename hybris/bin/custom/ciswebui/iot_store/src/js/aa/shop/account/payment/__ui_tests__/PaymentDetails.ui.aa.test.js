import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import {breakpoints} from "common/breakpoints";
import {cloneDeep} from 'lodash';
import * as Sizes from 'common/testtools/testScreenSizes';

describe("Payment details simple layout regression test", () => {
    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.PaymentDetailsAA);
    });

    it("with cc sepa dd and sepa credit payment infos", async () => {
        await createInlineUiTestPage(data);

        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);
    });

    it("with cc payment infos and invoice", async () => {
        data.pageData.directDebitPaymentInfos = [];

        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: breakpoints.S, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: breakpoints.M, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

    it("with cc payment infos and invoice default", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.ccPaymentInfos[0].defaultPaymentInfo = false;
        data.pageData.invoicePaymentInfos[0].defaultPaymentInfo = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: breakpoints.S, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: breakpoints.M, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

    it("with invoice only", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.ccPaymentInfos = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });

    it("with invoice dpg only", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.ccPaymentInfos = [];
        const infoPaymentInfo = {
            enabled: true,
            accountHolder: 'John Smith',
            iban: '**********************',
            bankName: 'Deutsche Bank',
            bic: 'DEUTDEMM',
            id: '12345',
            paymentMethod: 'SEPA_CREDIT',
            saved: false,
            reusable: false,
            defaultPaymentInfo: true,
            paymentProvider: 'DPG'
        };
        data.pageData.invoicePaymentInfos[1] = infoPaymentInfo;
        data.pageData.invoicePaymentInfos[2] = infoPaymentInfo;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });

    it("with invoice dpg arch only", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.ccPaymentInfos = [];
        const infoPaymentInfo = {
            enabled: true,
            accountHolder: 'John Smith',
            accountNumber: '**********************',
            routingNumber: 'Routing number',
            bankName: 'Deutsche Bank',
            bic: 'DEUTDEMM',
            id: '12345',
            paymentMethod: 'ACH_INTERNATIONAL',
            saved: false,
            reusable: false,
            defaultPaymentInfo: true,
            paymentProvider: 'DPG'
        };
        data.pageData.invoicePaymentInfos[1] = infoPaymentInfo;
        data.pageData.invoicePaymentInfos[2] = infoPaymentInfo;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });

    it("with invoice default", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.ccPaymentInfos = [];
        data.pageData.invoicePaymentInfos[0].defaultPaymentInfo = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: breakpoints.S, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: breakpoints.M, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

    it("cc payment infos only", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.invoicePaymentInfos = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.click(".actions .actions-for-default button");
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options");

        await page.setViewport({width: breakpoints.S, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options on 'S' viewport");

        await page.setViewport({width: breakpoints.M, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options on 'M' viewport");
    });

    it("sepa dd payment infos only", async () => {
        data.pageData.ccPaymentInfos = [];
        data.pageData.invoicePaymentInfos = [];

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);
    });

    it("add sepa mandate dialog", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.invoicePaymentInfos = [];
        data.coreData.moduleConfig.ENABLE_SEPA_MANDATE = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click("button[data-id='button-trigger-sepa-dd-payment-info-creation']");
        await page.waitFor(600);
        await Sizes.testAllScreenSizes(2000);
    });

    it("view sepa mandate dialog", async () => {
        data.pageData.ccPaymentInfos = [];
        data.pageData.invoicePaymentInfos = [];
        data.coreData.moduleConfig.ENABLE_SEPA_MANDATE = true;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(600);
        await page.click(".actions .actions-for-default button");
        await page.waitFor(1000);
        await page.click(".menuable__content__active div[data-id='view-payment-option']");
        await page.waitFor(600);
        await Sizes.testAllScreenSizes(2000);
    });

    it("sepa dd and sepa credit payment infos", async () => {
        data.pageData.ccPaymentInfos = [];

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);
    });

    it("no SEPA DD payment info creation", async () => {
        // SEPA DD payment method not allowed in country
        data.coreData.currentCompany.companyCountry = 'CH';

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2000);
    });

    it("cc payment infos only remove default", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.invoicePaymentInfos = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.click(".actions .actions-for-non-default button");
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options not default");

        await page.setViewport({width: breakpoints.S, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options on 'S' viewport not default");

        await page.setViewport({width: breakpoints.M, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options on 'M' viewport not default");
    });

    it("with long card holder name", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.invoicePaymentInfos = [];
        data.pageData.ccPaymentInfos[0].accountHolderName = "LongName of AccountHolderToFitTheScreen";
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.setViewport({width: breakpoints.L, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: breakpoints.S, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: breakpoints.M, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

    it("cc payment delete confirmation", async () => {
        data.pageData.directDebitPaymentInfos = [];
        data.pageData.invoicePaymentInfos = [];

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(600);
        await page.click(".card-data .actions .actions-for-non-default button");
        await page.waitFor(1000);
        await page.click(".menuable__content__active div[data-id='delete-non-default-payment']");
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: breakpoints.S, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: breakpoints.M, height: 2000});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });
});
