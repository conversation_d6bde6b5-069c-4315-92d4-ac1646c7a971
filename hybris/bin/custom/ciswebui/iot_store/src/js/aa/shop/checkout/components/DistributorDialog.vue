<template>
  <v-dialog v-model="isOpen"
            :fullscreen="$vuetify.breakpoint.smAndDown"
            width="650"
            content-class="white pa-8">

    <div class="ml-1 mt-1" data-id="container-distributor-switcher-dialog">
      <v-row class="mb-8" justify="space-between">
        <h2 class="text-capitalize">
          {{ $t('shop.checkout.distributor.dialog.title') }}
        </h2>
        <CDButtonIcon
            icon="$close"
            @click="isOpen = false"
            class="mt-n3"
            data-id="button-distributor-dialog-close"></CDButtonIcon>
      </v-row>
      <v-row>
        <p>
          {{ $t('shop.checkout.distributor.dialog.description') }}
        </p>
      </v-row>
      <v-row class="mt-4">
        <span class="distributor-select">
          {{ $t('shop.checkout.distributor.dialog.listName') }}
        </span>
      </v-row>
      <v-row class="mb-10">
        <CDSelect
            return-object
            :items="distributorsList"
            item-text="name"
            v-model="selectedDistributor"
            data-id="distributor-switch">
        </CDSelect>
      </v-row>

      <div data-id="container-distributor-dialog-footer">
        <v-row justify="end">
          <CDButton
              class="mr-4"
              data-id="button-distributor-dialog-close"
              @click="isOpen = false">
            {{ $t('cancel') }}
          </CDButton>
          <CDButton
              color="primary"
              data-id="button-distributor-dialog-submit"
              @click="assignNewDistributor">
            {{ $t('confirm') }}
          </CDButton>
        </v-row>
      </div>
    </div>
  </v-dialog>
</template>

<script lang="ts">
import {Component, ModelSync, Prop, Vue} from 'vue-property-decorator';
import {UmpDistributorData} from 'common/generated-types/types';

@Component
export default class DistributorDialog extends Vue {

  @Prop() distributorsList!: UmpDistributorData[];
  @Prop() currentDistributor!: UmpDistributorData;

  @ModelSync('value', 'input', {type: Boolean, default: false})
  isOpen!: boolean;
  selectedDistributor = this.currentDistributor;

  assignNewDistributor() {
    this.$emit('distributor-changed', this.selectedDistributor);
    this.isOpen = false;
  }
}
</script>

<style scoped lang="scss">
.v-dialog__content:has(.v-dialog--persistent) {
  backdrop-filter: blur(6px);
}

.distributor-select {
  color: var(--v-grey-darken2);
}
</style>
