<template>
  <v-row class="products-category-details"
         :data-id="`container-category-details-${category.code}`">

    <!--
      If a category has products, display them first and then append its subcategories in expansion panels
    -->
    <v-col cols="12" md="5" v-if="$vuetify.breakpoint.mdAndUp">
      <v-img :src="pictureSrc" height="900" class="sticky" eager></v-img>
    </v-col>

    <v-col cols="12" md="7" class="mt-10">
      <h5>{{ categoryGroupName }}</h5>
      <h1 class="my-4">{{ category.name }}</h1>

      <v-img :src="thumbnailSrc" height="200" v-if="$vuetify.breakpoint.smAndDown"></v-img>
      <!--
        If a category has products, display them first and then append its subcategories in expansion panels
      -->
      <div v-if="category.products && category.products.length"
           :data-id="`container-category-with-products-${category.code}`">
        <category-product-list
            :category="category"
            :data-id="`container-category-products-list-${category.code}`">
        </category-product-list>
        <categories-expansion-panels
            :categories="subCategories(category)"
            :data-id="`container-category-subcategories-expansion-panels-${category.code}`"></categories-expansion-panels>
      </div>

      <!--
        Else if a category has no products, then display its subcategories in tabs, and then its subcategories' subcategories in expansion panels
      -->
      <CDTabs v-else
              fixed-tabs
              class="my-8"
              :dense="$vuetify.breakpoint.mdAndDown"
              :data-id="`container-category-tabs-${category.code}`"
              @change="displayPicturesFromCategoryWithOrder($event)">
        <template v-for="subcategory in subCategories(category)">
          <v-tab :key="subcategory.code"
                 class="flex-column justify-start">
            <div class="d-flex flex-wrap justify-center flex-column flex-lg-row">
              <CDIcon v-if="subcategory.iconCode"
                      class="mr-lg-4 align-self-center"
                      :icon="subcategory.iconCode"
                      color="black"></CDIcon>
              <h3 class="black--text">
                {{ subcategory.name }}
              </h3>
            </div>
            <div v-if="hardwareRequirementsForCategoryProducts(subcategory).length > 0"
                 class="d-flex flex-wrap justify-center flex-shrink-1 mt-4 mt-lg-6 mr-n2 mb-n2">
              <CDChip v-for="(requirement, i) in hardwareRequirementsForCategoryProducts(subcategory).slice(0, maxHardwareChips)"
                      :key="i"
                      class="mr-2 mb-2"
                      style="letter-spacing: normal"
                      disabled
                      large
                      :data-id="`container-category-chips-${requirement.value}`">
                <v-img v-if="requirement.iconUrl"
                       :src="requirement.iconUrl"
                       class="mr-2"
                       width="24"
                       height="24"></v-img>
                {{ requirement.value }}
              </CDChip>
              <OtherHardwareCompatibilityDialog
                  :hardware-requirements="hardwareRequirementsForCategoryProducts(subcategory)"
                  activator-class="mr-2 mb-2">
              </OtherHardwareCompatibilityDialog>
            </div>
          </v-tab>
          <v-tab-item :transition="false" :key="subcategory.code">
            <category-product-list :category="subcategory" :hideChips="true"></category-product-list>
            <categories-expansion-panels :categories="subCategories(subcategory)"></categories-expansion-panels>
          </v-tab-item>
        </template>
      </CDTabs>

    </v-col>
  </v-row>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryHierarchyDTO, FeatureValueDTO, ImageDTO} from 'common/generated-types/types';
import CategoryProductList from 'aa/shop/productsoverview/components/categorydetails/CategoryProductList.vue';
import CategoriesExpansionPanels from 'aa/shop/productsoverview/components/categorydetails/CategoriesExpansionPanels.vue';
import {productFeaturesService} from 'aa/shop/services/productFeaturesService';
import {categoriesService} from 'aa/shop/services/categoriesService';
import OtherHardwareCompatibilityDialog from 'aa/shop/productsoverview/components/categorydetails/OtherCompatibleHardwareDialog.vue';
import {ProductsOverview} from 'common/constants';
import {uniqBy} from 'lodash';

/**
 * The categories displayed in this component are at the third level of the category hierarchy.
 * As an example, in the following tree the component would take cat_101 as a prop,
 * and would display cat_101 and its subcategories, together with their products.
 * -> root : "main"
 * ----> cat_1: ECU diagnosis
 * --------> cat_101: ESI[tronic] 2.0
 * ------------> cat_10101: Car
 * ------------> cat_10102: Truck & Off-highway
 * ----> cat_2: Vehicle system analysis
 * --------> cat_201: CompacSoft[plus]
 */
@Component({
  components: {CategoriesExpansionPanels, CategoryProductList, OtherHardwareCompatibilityDialog}
})
export default class CategoryDetails extends Vue {
  @Prop() category!: CategoryHierarchyDTO;
  @Prop() categoryGroupName!: string;

  pictureToDisplay = {} as ImageDTO;
  thumbnailToDisplay = {} as ImageDTO;
  maxHardwareChips = ProductsOverview.MAX_HARDWARE_REQUIREMENTS_TO_DISPLAY_ON_TAB;

  created() {
    this.pictureToDisplay = this.category.picture;
    this.thumbnailToDisplay = this.category.thumbnail;
  }

  get pictureSrc(): string {
    return this.pictureToDisplay?.url;
  }

  get thumbnailSrc(): string {
    return this.thumbnailToDisplay?.url;
  }

  subCategories(category: CategoryHierarchyDTO): CategoryHierarchyDTO[] {
    const subCategoriesToDisplay = categoriesService.filterEmptyCategories(category.subcategories);
    return categoriesService.sortCategories(subCategoriesToDisplay);
  }

  hardwareRequirementsForCategoryProducts(category: CategoryHierarchyDTO): FeatureValueDTO[] {
    const requirements = category.products?.flatMap(product => productFeaturesService.hardwareRequirementsForProduct(product));
    return uniqBy(requirements, requirement => requirement.value);
  }

  displayPicturesFromCategoryWithOrder(categoryOrder: number) {
    const subCategory = this.category.subcategories.find(subCategory => subCategory.order === categoryOrder + 1);
    if (subCategory?.picture) {
      this.pictureToDisplay = subCategory.picture;
    }
    if (subCategory?.thumbnail) {
      this.thumbnailToDisplay = subCategory.thumbnail;
    }
  }
}
</script>

<style scoped lang="scss">
.sticky {
  position: sticky;
  top: 0;
}
</style>
