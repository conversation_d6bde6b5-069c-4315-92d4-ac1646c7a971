import { CoreData, ShopCoreData } from 'common/generated-types/types';
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { userPermissionService } from 'common/services';

export const useStoreRootStore = defineStore('storeRoot', () => {
    const coreData = ref<CoreData>({} as CoreData);

    const isManagedAccount = computed(() =>
        coreData.value.moduleConfig?.DIRECT_SALES &&
        userPermissionService.isUserOfManagedAccount()
    );
    
    const isNewInfoForAppEnabled = computed(() =>
        Boolean(coreData.value.moduleConfig?.NEW_INFO_FOR_APP)
    );

    const thlAppCodeInCart = computed(() => (coreData.value as ShopCoreData).thlAppCodeInCart);

    function setCoreData<T extends CoreData>(val: T): void {
        coreData.value = val;
    }

    return {
        coreData,
        setCoreData,
        isManagedAccount,
        isNewInfoForAppEnabled,
        thlAppCodeInCart,
    };
});