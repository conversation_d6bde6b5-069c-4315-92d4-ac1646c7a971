<template>
  <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="mt-4 mt-lg-16">
    <div class="order-history">
    <h1>{{$t('shop.orderHistory.title')}}</h1>
    <div class="item-list">
      <template v-if="pageData.pagination.totalNumberOfResults === 0">
        <div class="no-orders">
          <sheet-icon class="title-icon"></sheet-icon>
          <div class="no-orders-text">
            <h3>{{$t('shop.orderHistory.noOrders.title')}}</h3>
            <p>{{$t('shop.orderHistory.noOrders.text')}}</p>
          </div>
        </div>
      </template>

      <template v-else>
        <CDDataTable :expanded="pageData.results"
                     :headers="header"
                     :items="pageData.results"
                     :items-per-page="pageData.pagination.pageSize"
                     :sort-desc="true"
                     hide-default-footer
                     item-key="code"
                     mobile-breakpoint="0"
                     sort-by="lastUpdated"
                     @click:row="onRowClick($event)">
          <template v-slot:[`item.code`]="{ item }">
            <div class="entry" v-bind:data-order="item.code">{{item.code}}</div>
          </template>
          <template v-slot:[`item.appIconUrls`]="{ item }">
            <div class="entry">
              <img v-for="(appIcon) in item.appIconUrls" :src="appIcon" class="icon rounded-circle"/>
            </div>
          </template>
          <template v-slot:[`item.placedBy`]="{ item }">
            <div class="entry overflow">
              {{item.placedBy}}
            </div>
          </template>
          <template v-slot:[`item.placed`]="{ item }">
            <div class="entry">{{ formattedDate(item.placed) }}</div>
          </template>
          <template v-slot:[`item.lastUpdated`]="{ item }">
            <div class="entry">{{ formattedDate(item.lastUpdated) }}</div>
          </template>
          <template v-slot:[`item.statusDisplay`]="{ item }">
            <div :class="{highlighted: item.statusDisplay === 'OVERDUE'}"
                 :data-status="item.code"
                 class="entry">
              {{ formattedStatus(item) }}
            </div>
          </template>
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length" class="d-table-cell d-lg-none">
              <v-row class="my-2 d-none d-md-table-cell d-lg-none" no-gutters>
                <v-col class="my-1" cols="12">
                  <span class="text-h5">
                    {{$t('shop.orderHistory.table.placedBy')}}:
                  </span>
                  {{item.placedBy}}
                </v-col>
              </v-row>
              <v-row class="my-2 d-md-none" no-gutters>
                <v-col class="my-1">
                  <v-row no-gutters>
                    <v-col cols="1 mr-4 mt-2">
                      <span class="text-h5">{{$t('shop.orderHistory.table.apps')}}</span>
                    </v-col>
                    <v-col>
                      <img v-for="(appIcon) in item.appIconUrls" :src="appIcon" class="icon"/>
                    </v-col>
                  </v-row>
                  <v-row no-gutters>
                    <v-col>
                      <span class="text-h5">
                    {{$t('shop.orderHistory.table.placedBy')}}:
                  </span>
                      {{item.placedBy}}
                    </v-col>
                  </v-row>
                  <v-row no-gutters>
                    <v-col>
                      <span class="text-h5">
                    {{$t('shop.orderHistory.table.orderDate')}}:
                  </span>
                      {{ formattedDate(item.placed) }}
                    </v-col>
                  </v-row>
                  <v-row no-gutters>
                    <v-col>
                      <span class="text-h5">
                    {{$t('shop.orderHistory.table.lastUpdated')}}:
                  </span>
                      {{ formattedDate(item.lastUpdated) }}
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </td>
          </template>

        </CDDataTable>
        <div class="item no-hover pagination">
          <pagination
              page-url="/my-account/orders"
              v-bind:pagination-data="pageData.pagination">
          </pagination>
        </div>
      </template>
    </div>
    </div>
  </v-container>
</template>

<script lang="ts">
  import {Component, Prop, Vue} from 'vue-property-decorator';
  import {Pagination, Tooltip} from 'common/components';
  import {commercefacades, core, ShopCoreData} from 'common/generated-types/types';
  import SheetIcon from 'common/images/boxed/icon-36-sheet.svg';
  import {toLower} from 'lodash';
  import SearchPageData = core.SearchPageData;
  import OrderHistoryData = commercefacades.OrderHistoryData;
  import {i18nService, messageService} from 'common/services';

  @Component({
    components: {
      SheetIcon,
      Pagination,
      Tooltip
    }
  })
  export default class OrderHistory extends Vue {
    @Prop() coreData!: ShopCoreData;
    @Prop() pageData!: SearchPageData<OrderHistoryData>;
    paidOrders!: string[];

    localeForFormats = '';

    mounted(): void {
      this.localeForFormats = i18nService.getLocaleForFormats();
    }

    header = [
      {text: this.$t('shop.orderHistory.table.orderNumber'), value: 'code', sortable:false, align: 'start d-table-cell'},
      {text: this.$t('shop.orderHistory.table.apps'), value: 'appIconUrls', sortable:false, align: 'start d-none d-md-table-cell'},
      {text: this.$t('shop.orderHistory.table.placedBy'), value: 'placedBy', sortable:false, align: 'start d-none d-lg-table-cell'},
      {text: this.$t('shop.orderHistory.table.orderDate'), value: 'placed', sortable:false, align: 'start d-none d-md-table-cell'},
      {text: this.$t('shop.orderHistory.table.lastUpdated'), value: 'lastUpdated',sortable:false, align: 'start d-none d-md-table-cell'},
      {text: this.$t('shop.orderHistory.table.status'), value: 'statusDisplay',sortable:false, align: 'end text-md-start d-table-cell'}
    ];

    created(): void {
      this.setPageTitle();
      const arr = this.$route.query['paid-orders'] as string;
      if (arr) {
        messageService.success(this.paidWithCreditCard(arr), false, true);
      }
    }

    paidWithCreditCard(arr: string): string {
      return `${this.$t('shop.orderHistory.creditCardPaymentSuccess')}` + arr.split(',').join(', ');
    }

    onRowClick(item: OrderHistoryData): void {
      window.location.assign(`orders/${item.code}`);
    }

    formattedStatus(item: OrderHistoryData): string {
      return this.$t(`shop.orderStatus.${toLower(item.statusDisplay)}`).toString();
    }

    formattedDate(date: string): string {
      return this.$d(new Date(date), 'long', this.localeForFormats).toString();
    }

    private setPageTitle(): void {
      document.title = this.$t('shop.orderHistory.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
    }
  };
</script>

<style lang="scss" scoped>
  @import 'common/design';
  @import 'shop/core/constants';
  @import 'common/vuetify';

  :deep(tbody tr) {
    cursor: pointer;
  }
  :deep(.v-data-table__expanded__content) {
    box-shadow: none !important;
  }

  :deep(.v-data-table__expanded__row) {
    td {
      vertical-align: middle;
      @media only screen and (max-width: #{map-get($display-breakpoints, 'lg-only') - 0.02}) {
        border-bottom: none !important;
      }
    }
  }

  :deep(.v-data-table-header) {
    th {
      vertical-align: middle;
    }
  }

  .entry {
    margin: 4px 0;
    padding-top: 4px;
  }

  .icon {
    width: 36px;
    height: 36px;
    flex-basis: 36px;
    background-size: auto;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 4px;
    box-sizing: border-box;
  }

  .highlighted {
    color: var(--v-error-base);
  }

  .item-list {
    margin-top: $spacing-m;
  }

  .overflow {
    overflow: hidden;
    overflow-wrap: normal;
    text-overflow: ellipsis;
    width: 100%;
    white-space: nowrap;
  }

  .no-orders {
    margin: $spacing-xl auto;
    padding: 0 $spacing-m;
    max-width: 426px;

    display: grid;
    grid-template-columns: 36px 1fr;
    grid-column-gap: 30px;
    align-items: center;
  }

  .no-orders-text {
    max-width: 360px;

    h3 {
      margin-top: 0;
    }

    p {
      margin-top: 0;
      color: var(--v-grey-darken2);
      line-height: normal;
    }
  }
</style>
