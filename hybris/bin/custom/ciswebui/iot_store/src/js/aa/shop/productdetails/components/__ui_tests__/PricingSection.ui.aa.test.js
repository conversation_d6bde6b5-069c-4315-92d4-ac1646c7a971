import {cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";

describe("PricingSection", () => {

  let styleContent;

  beforeEach(async () => {
    styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    await page.setRequestInterception(true);
    page.on('request', coreDataRestHandler);
    await page.setCookie(...cookies_en_AT);
  });

  it("looks as expected in initial state", async () => {
    await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productdetails-components-__ui_tests__-PricingSection');

    await page.setViewport({width: 1440, height: 700});
    await page.waitFor(400);
    expect(await page.screenshot()).toMatchImageSnapshot("PricingSection on 'XL' viewport");

    await page.setViewport({width: 576, height: 700});
    await page.waitFor(600);
    expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("PricingSection on 'S' viewport");

  });

  it("looks as expected quantity exceeds state", async () => {
    await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productdetails-components-__ui_tests__-PricingSection');

    await page.addStyleTag({ content: styleContent });

    await page.setViewport({width: 1440, height: 1300});
    await page.waitFor(400);
    expect(await page.screenshot()).toMatchImageSnapshot("PricingSection on 'XL' viewport");

    await page.waitFor(500);
    const input = await page.$('input[name="FULL"]');
    await page.keyboard.press('Backspace');
    await input.type('1234', {});

    await page.waitFor(500);
    expect(await page.screenshot()).toMatchImageSnapshot('edited quantity exceeds');
  });
});
