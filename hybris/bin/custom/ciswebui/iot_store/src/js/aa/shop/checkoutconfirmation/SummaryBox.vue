<template>
  <v-card class="my-apps pa-8 pa-lg-16 rounded-lg d-flex flex-column flex-md-row mt-12" flat>
    <v-card class="mb-6 mr-4 mr-md-16" flat>
      <aa-checkout-confirmation-image></aa-checkout-confirmation-image>
    </v-card>
    <v-card flat>
      <h2 class="mb-6">{{ $t('shop.checkout.summary.header') }}</h2>
      <span v-html="summaryText"></span>
    </v-card>
  </v-card>
</template>
<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import AaCheckoutConfirmationImage from 'common/images/boxed/aa-checkout-confirmation.svg';
import {CheckoutConfirmationData} from 'common/generated-types/types';

@Component({
  components: {
    AaCheckoutConfirmationImage
  }
})


export default class SummaryBox extends Vue {
  @Prop() pageData!: CheckoutConfirmationData;
  @Prop() summaryText!: string;
}

</script>

