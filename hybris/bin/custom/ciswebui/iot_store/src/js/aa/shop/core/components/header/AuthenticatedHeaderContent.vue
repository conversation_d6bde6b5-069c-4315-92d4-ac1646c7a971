<template>
  <v-row align="center"
         class="content-row"
         data-id="container-authenticated-header-content">
    <template v-if="!isMobile">
      <CDNavbar2Link v-for="(item, i) in componentsLinksItems"
                     :key="i"
                     :icon="item.icon"
                     :href="item.href"
                     :active="item.code === 'marketplaceComponent'"
                     :data-id="`link-nav-component-${item.code}`">
        {{ item.title }}
      </CDNavbar2Link>
    </template>

    <CDButtonIcon v-if="hasAccessToCart"
                  :href="cartPageLink"
                  class="icon-cart"
                  data-id="link-cart-page">
      <CDIcon icon="$cart"
              color="black"
              data-id="icon-cart"
              :badge-content="totalQuantity > 0 ? totalQuantity : false"
              :badge-value="totalQuantity > 0"
              :badge-dot="totalQuantity <= 0">
      </CDIcon>
    </CDButtonIcon>

    <CDBento :items="moreActionsItems"
             :left="true"
             trigger-icon="$more"
             :trigger-right="true"
             trigger-test-id="-more"></CDBento>

    <template v-if="isMobile">
      <CDBento :items="componentsLinksItems"
               :selected-item="0">
      </CDBento>
    </template>

    <CDUserFlyout v-if="userFlyoutHeader?.text"
                  :header="userFlyoutHeader"
                  :idp="userFlyoutIdp"
                  :offset-x="true"
                  :left="true"
                  :nudge-top="-8"
                  data-id="container-user-flyout">
      <CDUserFlyoutMenuItem>
        <CDButton v-if="logoutNav" :href="logoutNav.url" large>
          <CDIcon left>
            {{ logoutNav.icon }}
          </CDIcon>
          {{ translateNavigation(logoutNav) }}
        </CDButton>
      </CDUserFlyoutMenuItem>
    </CDUserFlyout>

  </v-row>

</template>

<script setup lang="ts">
import {ShopRoute} from 'common/constants';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import {useCartStore} from 'shop/store/cart';
import {computed} from 'vue';
import {navigationService, userPermissionService} from 'common/services';
import {NavigationItemData, NavigationItemGroup} from 'common/generated-types/types';
import {useI18n} from 'vue-i18n-bridge';
import {useVuetify} from 'aa/shop/composables/usevuetify';

const storeRootStore = useStoreRootStore();
const cartStore = useCartStore();
const {t} = useI18n();
const vuetify = useVuetify();

const cartPageLink = ShopRoute.CART;

const isMobile = computed(() => vuetify?.breakpoint.mobile);

const hasAccessToCart = computed(() => userPermissionService.hasAccessToCart());
const totalQuantity = computed(() => cartStore.totalQuantity);

const componentsLinksItems = computed(() => {
  return navigationService.byGroupSorted(NavigationItemGroup.COMPONENTS)
      .map(item => {
        return {
          title: translateNavigation(item),
          icon: item.icon,
          href: item.url,
          code: item.itemCode
        };
      });
});

const moreActionsItems = computed(() => {
      return navigationService.byGroupSorted(NavigationItemGroup.HEADER)
          .filter(item => ['storeOrderHistory', 'storePaymentDetails'].includes(item.itemCode))
          .map(item => {
            return {
              title: translateNavigation(item),
              icon: item.icon,
              href: item.url,
              target: item.target
            };
          });
    }
);

const userFlyoutHeader = computed(() => {
      const userName = storeRootStore.coreData.userName;
      const company = storeRootStore.coreData.currentCompany;
      const companyName = company?.friendlyName || company?.name;
      return {
        text: userName,
        sub: companyName
      };
    }
);

const userFlyoutIdp = computed(() =>
  storeRootStore.coreData.idp && storeRootStore.coreData.idp.loginIdp
    ? {
      'login_idp': storeRootStore.coreData.idp.loginIdp,
      'login_idp_account_url': storeRootStore.coreData.idp.loginIdpAccountUrl
    }
    : undefined
);

const logoutNav = computed(() => navigationService.byItemCode('storeSignOut'));

function translateNavigation(navigationItem: NavigationItemData): string {
  const navTranslationKey = `navigation.items.${navigationItem.itemCode}`;
  return t(navTranslationKey).toString();
}

</script>

<style scoped>
.content-row {
  column-gap: 8px;
}
</style>
