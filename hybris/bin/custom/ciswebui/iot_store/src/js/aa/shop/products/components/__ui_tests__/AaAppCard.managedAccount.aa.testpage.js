import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import AaAppCard from 'aa/shop/products/components/AaAppCard.vue';
import {PriceFrequency} from 'common/generated-types/types';

Vue.use(VueI18n);

const productData = {
    code: 'AA_04012412',
    title: 'Paket Truck Einmalkauf',
    body: 'Unterstützt Werkstätten bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen',
    img: '/sample-data/aa/big-logo.png',
    href: '/shop/p/AA_04015105',
    chips: [
        {
            text: 'Abonnement'
        },
        {
            text: 'Einmal<PERSON>uf (3 Jahre)'
        }
    ],
    priceInfo: {
        minPrice: {
            currencyIso: 'EUR',
            value: 1020.0,
            priceFrequency: PriceFrequency.YEARLY
        },
        specialOffer: false
    }
};

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        AaAppCard
    },
    data: {
        appCardData: productData
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <aa-app-card
              :app-card-data="appCardData" :hide-prices="true">
          </aa-app-card>
        </div>
      </test-page-root>
    `
});
