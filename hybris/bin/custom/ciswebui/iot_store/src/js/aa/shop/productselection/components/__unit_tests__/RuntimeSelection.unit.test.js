import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import RuntimeSelection from '../RuntimeSelection';
import productSelection from 'common/testtools/scenariosstore/aa/productSelection.json';

jest.mock('common/util');


const mountRuntimeSelection = (product) => wrapperComponentFactory(RuntimeSelection, {
    props: {
        product: product
    }
});

describe('RuntimeSelection', () => {
    let product;

    beforeEach(() => {
        product = productSelection;
    });


    describe('runtime card', () => {
        it('is preselected', async () => {
            const wrapper = await mountRuntimeSelection(product);

            const selectedRuntime = wrapper.vm.selectedRuntime;
            expect(selectedRuntime).toBe(wrapper.vm.runtimes[0].code);
        });

        it('sets selected runtime when clicked', async () => {
            const wrapper = await mountRuntimeSelection(product);

            await wrapper.find('[data-id="container-runtime-card-runtime_full_unlimited"]').trigger('click');

            const selectedRuntime = wrapper.vm.selectedRuntime;
            expect(selectedRuntime).toBe('runtime_full_unlimited');
        });
    });


    describe('runtimes computed property', () => {
        it('returns variants runtimes', async () => {
            const wrapper = await mountRuntimeSelection(product);

            const runtimes = wrapper.vm.runtimes;

            expect(runtimes).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({code: 'runtime_subs_unlimited'}),
                    expect.objectContaining({code: 'runtime_full_unlimited'})
                ])
            );
        });
    });

    describe('runtimes sorted property', () => {
        const runtime1 = {code: 'runtime_subs_unlimited', name: 'Unlimited Subs', description: 'Description 1'};
        const runtime2 = {code: 'runtime_full_3y', name: 'Full 3 Years', description: 'Description 2'};
        const runtime3 = {code: 'runtime_full_unlimited', name: 'Full Unlimited', description: 'Description 3'};

        it('should sort when all elements are in the desired order', async () => {
            const wrapper = await mountRuntimeSelection(product);
            const inputArray1 = [runtime1, runtime2, runtime3];
            const sortedArray1 = wrapper.vm.customSortRuntimes(inputArray1);
            expect(sortedArray1).toEqual([runtime1, runtime2, runtime3]);

            const inputArray2 = [runtime2, runtime3];
            const sortedArray2 = wrapper.vm.customSortRuntimes(inputArray2);
            expect(sortedArray2).toEqual([runtime2, runtime3]);

            const inputArray3 = [runtime3, runtime2, runtime1];
            const sortedArray3 = wrapper.vm.customSortRuntimes(inputArray3);
            expect(sortedArray3).toEqual([runtime1, runtime2, runtime3]);

            const inputArray4 = [runtime2];
            const sortedArray4 = wrapper.vm.customSortRuntimes(inputArray4);
            expect(sortedArray4).toEqual([runtime2]);
        });
    });
});
