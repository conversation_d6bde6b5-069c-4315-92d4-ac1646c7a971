<template>
  <div class="product-header">
    <v-container :fluid="true" class="pa-0">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <v-row class="mb-4">
          <v-col cols="12" :xl="video ? 6 : 8">

            <div class="d-md-flex align-md-center mb-4">
              <div class="logo mr-md-6 mb-4 mb-md-0 mx-sm-auto mx-md-0 rounded-circle" ref="logoheader">
                <div v-bind:style="{backgroundImage: `url(${logoUrl})`}"></div>
              </div>
              <h1 class="text-center text-lg-left">
                <span class="product-name" data-id="text-productname">{{productName}}</span>
                <CDChip v-if="isThirdPartyPrivateApp">{{ $t('shop.productDetails.productHeader.privateAppLabel') }}</CDChip>
              </h1>
            </div>

            <p class="short-description cd-text-large">{{shortDescription}}</p>

            <app-cta
                v-if="displayCta"
                :licenses="licenses"
                :read-only="readOnly">
            </app-cta>

          </v-col>
          <v-col cols="12" v-if="video" md="8" xl="6" class="pa-sm-0 my-sm-4">
            <youtube-player v-if="video" class="youtube-player pl-xl-4" v-bind:video="video">
            </youtube-player>
          </v-col>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import AppCta from 'aa/shop/productdetails/components/AppCta.vue';
    import util from 'common/util';
    import {numberRounderService} from 'common/services';
    import {
        ProductLicenseData,
        StoreAvailabilityMode,
        IotCompanyData,
        ShopCoreData,
        AppVideoData
    } from 'common/generated-types/types';
    import IconInfo from 'common/images/boxed/icon-24-info-d.svg';
    import {LicenseType} from 'common/types';
    import {StatusTag} from 'common/components';
    import {ShopCoreDataProvider} from 'common/provider/coreDataProvider';
    import {YoutubePlayer} from 'common/components/videoplayers';

@Component({
        components: {
            AppCta,
            IconInfo,
            StatusTag,
            YoutubePlayer
        }
    })
    export default class ProductHeader extends Vue {
        @Prop() productName!: string;
        @Prop() company!: IotCompanyData;
        @Prop() logoUrl!: string;
        @Prop() averageRating!: number;
        @Prop() numberOfReviews!: number;
        @Prop() numberOfInstalls!: number;
        @Prop() licenses!: ProductLicenseData[];
        @Prop() readOnly!: boolean;
        @Prop() storeAvailabilityMode!: StoreAvailabilityMode;
        @Prop() code!: string;
        @Prop() availableInUserCountry!: boolean;
        @Prop() isThirdPartyApp!: boolean;
        @Prop() shortDescription!: string;
        @Prop() video!: AppVideoData;

        coreData: ShopCoreData = ShopCoreDataProvider.data;
        baseUrl: string = this.coreData.baseUrl;

        numberRounderService = numberRounderService;
        LicenseType = LicenseType;

        $refs!: {
            logoheader: HTMLElement
            headermsg: HTMLElement;
        };

        mounted(): void {
            util.loadLogoImage([{
                logoUrl: this.logoUrl,
                logoElement: this.$refs.logoheader
            }]);
        }

        get isPrivateApp(): boolean {
          return this.storeAvailabilityMode === StoreAvailabilityMode.RESTRICTED_BUYER;
        }

        get isThirdPartyPrivateApp(): boolean {
          return this.isThirdPartyApp && this.isPrivateApp;
        }

        get displayCta(): boolean {
          return this.availableInUserCountry;
        }

        get companyName(): string {
          return this.company?.friendlyName || this.company?.name;
        }
 }
</script>

<style scoped lang="scss">
    @import "common/design";
    @import "shop/core/logo";
    @import "shop/core/constants";
    @import "shop/core/logo";

    .product-header {

        .logo {
            width: 78px;
            height: $header-height;
            overflow: hidden;
            border-radius: 10px;
            div {
                @extend %-logo-inner;
            }
        }

        .info {

            h1 {
                .status-tag {
                    justify-self: start;
                    margin-top: 4px;
                }

                @include respond-to('M') {
                    display: flex;
                    span + span {
                        margin: 0 $spacing-s;
                    }
                    .status-tag {
                        align-self: flex-start;
                    }
                }
            }
        }
    }
</style>
