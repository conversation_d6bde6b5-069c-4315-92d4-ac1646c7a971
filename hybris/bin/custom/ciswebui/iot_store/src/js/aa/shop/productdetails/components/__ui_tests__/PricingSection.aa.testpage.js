import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import { PricingSection } from '../../components/index';
import { PermissionText } from '../../../../../common/components';
import {createApp} from 'vue-demi';

window.frontendData.coreData.allowedToBuy = true;
window.frontendData.coreData.allowedMaxQuantityLineItemFullLicense = 1000;
window.frontendData.coreData.allowedMaxQuantityLineItemSubsLicense = 1000;
window.frontendData.coreData.currentCompany = {
  companyApproved: true
}

Vue.use(VueI18n, { bridge: true });

const options= {
  vuetify,
  i18n,
  el: '#vue-app',
  data: {
    product: {
      licenses: [
        {
          code: 'A_00000002_evaluation',
          name: 'Trial',
          price: 0,
          currencySymbol: null,
          currencyIsocode: null,
          licenseType: {
            code: 'EVALUATION',
            type: 'LicenseType'
          },
          purchasability: 'PURCHASABLE'
        },
        {
          code: 'A_00000002_full',
          name: 'Purchase',
          price: 199,
          currencySymbol: '€',
          currencyIsocode: 'EUR',
          licenseType: {
            code: 'FULL',
            type: 'LicenseType'
          },
          purchasability: 'PURCHASABLE',
          scalePrices: [
            {
              minQuantity: 5,
              discountPercent: 5,
              discountPrice: 190,
              currencyIsoCode: 'EUR'
            }
          ]
        }
      ],
      code: 'A_00000002'
    }
  },
  components: {
    TestPageRoot,
    PricingSection,
    PermissionText
  },
  template: `
    <test-page-root>
      <pricing-section
          v-bind:product="product"
          v-bind:not-available-in-country="false"
          v-bind:display-licenses="true">
      </pricing-section>
    </test-page-root>
  `
};

const app = createApp(options);
app.use(i18n);
app.mount('#app');
