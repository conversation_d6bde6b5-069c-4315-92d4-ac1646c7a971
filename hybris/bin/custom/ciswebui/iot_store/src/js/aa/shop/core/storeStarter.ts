import WebRoot from 'aa/shop/core/components/StoreRoot.vue';
import Vue, {type ComponentOptions} from 'vue';
import {createApp} from 'vue-demi';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import 'common/test-directive';
import {FocusDirective, OnDocumentDirective, TransformExternalLinks} from 'common/directives';
import pinia from 'shop/store';
import router from 'aa/shop/core/routing';

import VueRouter from 'vue-router';
import PortalVue from 'portal-vue';

import VuePlyr from 'vue-plyr';

import LoadScript from 'vue-plugin-load-script';

Vue.use(VueI18n, { bridge: true });

Vue.use(VueRouter);
Vue.use(PortalVue);
Vue.use(VuePlyr);
Vue.use(LoadScript);

Vue.directive('focus', FocusDirective);
Vue.directive('onDocument', OnDocumentDirective());
Vue.directive('transformExternalLinks', TransformExternalLinks);

let createStore = function (template?: string) {
  let options = {
    vuetify,
    el: '#vue-app',
    components: {WebRoot},
    pinia,
    i18n,
    router
  } as ComponentOptions<Vue>;
  if(template){
    options.template= template;
  }
  const app = createApp(options);
  app.use(i18n);
  app.mount('#app');
};

export {createStore};
