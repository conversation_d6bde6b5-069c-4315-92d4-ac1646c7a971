import {
    acceptCookieBanner,
    createInlineUiTestPage,
    resetMouseCoords,
    scrollDown,
    scrollIntoView
} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';
import {StoreAvailabilityMode} from 'common/generated-types/types';
import {cookies_de_DE, cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";


beforeEach(async () => {
    await page.setRequestInterception(true);
    page.on('request', coreDataRestHandler);
    await page.setCookie(...cookies_en_AT);
});

describe('AA ProductDetailsPage', () => {

    let styleContent;
    beforeEach(() => {
        styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    });
    it('looks as expected on different screen sizes', async () => {
        await createInlineUiTestPage(testMap.ProductDetailsAA);
        await acceptCookieBanner();

        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });

    it('looks as expected for de on different screen sizes', async () => {
        await page.setCookie(...cookies_de_DE);
        const coreData = cloneDeep(testMap.ProductDetailsAA.coreData);
        coreData.currentCountry = {
            "isocode": "DE",
            "name": "Deutschland",
            "currencyIsoCode": "EUR",
            "canBuy": true,
            "sepaEnabled": true,
            "blockedCountriesCommercial": []
        }
        await createInlineUiTestPage({...testMap.ProductDetailsAA, coreData: coreData});
        await acceptCookieBanner();

        await Sizes.testExtraLargeScreensize(4400);
    });

    it('buyer from blocked country cannot purchase commercial licences', async () => {
        const coreData = cloneDeep(testMap.ProductDetailsAA.coreData);
        coreData.allowedToBuy = true;
        coreData.currentCompany.companyApproved = true;
        const dataWithBlockedCountry = cloneDeep(testMap.ProductDetailsAA.pageData);
        dataWithBlockedCountry.licenses = dataWithBlockedCountry.licenses
            .map(license => ({ ...license, purchasability: 'COUNTRY_BLOCKED' }));
        dataWithBlockedCountry.integratorCountryBlockedInDeveloperCountry = true;

        await createInlineUiTestPage({...testMap.ProductDetailsAA, pageData: dataWithBlockedCountry, coreData: coreData});
        await acceptCookieBanner();
        await scrollDown();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });

});

describe('AA ProductDetailsPage Sidebar', () => {
    it('does not contain country availability information when app is private', async () => {
        let data = cloneDeep(testMap.ProductDetailsAA);
        data.pageData.storeAvailabilityMode = StoreAvailabilityMode.RESTRICTED_BUYER;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });
});

describe('AA ProductDetailsPage Header', () => {
    it('contains company name as link when profile is published', async () => {
        let data = cloneDeep(testMap.ProductDetailsAA);
        data.pageData.company.hasPublishedProfile = true;
        data.pageData.company.profileUrl = '/company-profile/uuid';
        data.coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });
});

describe('ProductDetailsPage Pricing Section', () => {
    it('displays message about price change when product has future prices for subscription', async () => {
        let data = cloneDeep(testMap.ProductDetailsAA);
        data.coreData.allowedToBuy = true;
        data.coreData.currentCompany.companyApproved = true;
        const subscriptionLicense = {
            'code': 'AA_04015105_sub',
            'name': 'Subscription',
            'summary': null,
            'price': 100,
            'currencyIsocode': 'EUR',
            'currencySymbol': 'EUR',
            'licenseType': {
                'code': 'SUBSCRIPTION',
                'type': 'LicenseType'
            },
            'quantity': 0,
            'purchasability': 'PURCHASABLE',
            'scalePrices': [],
            'futurePrices': [
                {
                    "currencyIsoCode": "EUR",
                    "price": "200",
                    "startDate": "2023-02-01T00:00:00.000Z"
                }
            ]
        };
        data.pageData.licenses = [subscriptionLicense];

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(1000);

        const elementToScrollIntoView = '[data-id="container-pricing-section"]';
        await page.$eval(elementToScrollIntoView, scrollIntoView);
        await page.waitFor(800);

        await Sizes.testExtraLargeScreensize(2000);
        await Sizes.testLargeScreensize(2000);
        await Sizes.testMediumScreensize(3500);
        await Sizes.testSmallScreensize(8000);
    });
});
