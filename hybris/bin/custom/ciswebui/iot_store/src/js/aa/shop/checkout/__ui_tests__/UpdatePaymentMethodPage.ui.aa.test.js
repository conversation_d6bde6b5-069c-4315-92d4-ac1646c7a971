import {acceptCookieBanner, createInlineUiTestPage} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import {cloneDeep} from 'lodash';
import * as Sizes from 'common/testtools/testScreenSizes';
import updatePaymentMethodData from 'common/testtools/scenariosstore/aa/updatePaymentMethodData.json';
import {cookies_en_AT} from "common/testtools/testCookies";

const REST_URL_GET_PAYMENT = '/shop/api/orders/00017001/payment';

const updatePaymentMethodRestHandler = (request, updatePaymentMethodDataResult) => {
    if (request.url().endsWith(REST_URL_GET_PAYMENT)) {
        request.respond({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(updatePaymentMethodDataResult)
        });
    } else {
        request.continue();
    }
};

describe('update payment method page new credit card', () => {

    let data;
    let pageWait = 3000;
    let height = 2000;

    beforeEach(async () => {
        data = cloneDeep(testMap.UpdatePaymentMethodAA);
        await page.setRequestInterception(true);
        page.on('request', (request) => updatePaymentMethodRestHandler(request, updatePaymentMethodData));
        await page.setCookie(...cookies_en_AT);
    });

    afterEach(async () => {
        await page.setRequestInterception(false);
    });

    it('default', async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner(true);

        await Sizes.testAllScreenSizes(height, pageWait);
    });
});

describe('update payment method page new credit card dismiss warning', () => {

    let data;
    let pageWait = 3000;
    let height = 2000;

    beforeEach(async () => {
        data = cloneDeep(testMap.UpdatePaymentMethodAA);
        await page.setRequestInterception(true);
        page.on('request', (request) => updatePaymentMethodRestHandler(request, updatePaymentMethodData));
        await page.setCookie(...cookies_en_AT);
    });

    afterEach(async () => {
        await page.setRequestInterception(false);
    });

    it('default', async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner(true);

        await page.click('[data-id="payment-method-problem-text"] .v-alert__dismissible');

        await Sizes.testAllScreenSizes(height, pageWait);
    });
});