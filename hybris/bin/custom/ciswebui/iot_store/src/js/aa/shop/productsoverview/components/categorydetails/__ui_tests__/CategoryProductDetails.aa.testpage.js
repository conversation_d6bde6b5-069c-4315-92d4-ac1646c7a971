import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import CategoryProductDetails from 'aa/shop/productsoverview/components/categorydetails/CategoryProductDetails.vue';
import categoryProduct from 'common/testtools/scenariosstore/aa/categoryProduct.json';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        CategoryProductDetails
    },
    data: {
        product: categoryProduct
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <category-product-details
              :product="product">
          </category-product-details>
        </div>
      </test-page-root>
    `
});
