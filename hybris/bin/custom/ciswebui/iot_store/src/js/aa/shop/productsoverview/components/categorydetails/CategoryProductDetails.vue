<template>
  <div class="my-8" :data-id="`container-product-details-${product.code}`">
    <v-row dense>
      <v-col order-lg="0" cols="12" lg="9">
          <span class="text-h3 mr-2">{{ product.name }}</span>
          <special-offer-chip
                  v-if="hasSpecialOffer"
                  class="mb-1 mr-2"
                  disabled
                  :data-id="`container-special-offer-chip-product-${product.code}`"></special-offer-chip>
      </v-col>
      <v-col order-lg="1" cols="12" lg="9" v-if="!hideChips && hardwareRequirementsForProduct && hardwareRequirementsForProduct.length">
        <CDChip v-for="(requirement, i) in hardwareRequirementsForProduct.slice(0, 2)"
                :key="i"
                class="mr-2"
                disabled
                large>
          <v-img v-if="requirement.iconUrl"
                 :src="requirement.iconUrl"
                 class="mr-2"
                 width="24"
                 height="24">
          </v-img>
          {{ requirement.value }}
        </CDChip>
        <OtherHardwareCompatibilityDialog :hardware-requirements="hardwareRequirementsForProduct">
        </OtherHardwareCompatibilityDialog>
      </v-col>

      <v-col v-if="product.productNote && hasNewInfoOnApp" order-lg="3" cols="12" lg="9" class="mb-3">
        <v-row>
          <v-col cols="auto" class="pr-0">
            <CDChip>{{ $t('new') }}</CDChip>
          </v-col>
          <v-col cols="10" lg="10">
            <p class="text-body-1 mb-0 mr-2">
              {{ product.productNote }}
            </p>
          </v-col>
        </v-row>
      </v-col>
      <v-col order-lg="3" cols="12" lg="9">
        <p class="text-body-1 mb-0 mr-2">{{ product.summary }}</p>
        <p v-if="isAnonymous"
           class="text-body-2 mb-0 mt-2">
          <a :href="productSelectionUrl">{{ $t('shop.productsOverview.category.product.licenseTypesAndTerms') }}</a>
        </p>
      </v-col>
      <template v-if="!hidePrices">
        <v-col order-lg="2" lg="3" class="mt-4 mt-lg-0 d-flex justify-lg-end align-lg-center">
          <min-price-display v-if="product.minPrice" :min-price="product.minPrice" :data-id="`text-product-min-price-${product.code}`"
                             class="text-h5">
          </min-price-display>
        </v-col>
        <v-col order-lg="4" cols="auto" lg="3" class="mt-4 mt-lg-0 d-flex justify-end">
          <!-- TODO: on disabled prop, add condition to check if product is THL -->
          <CDButton :href="purchaseButtonUrl"
                    color="primary"
                    :disabled="thlAppCodeInCart === product.code"
                    :data-id="`button-product-buy-now-${product.code}`">
            {{ $t('shop.productsOverview.category.product.purchaseButton') }}
          </CDButton>
        </v-col>
      </template>
    </v-row>
  </div>

</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {FeatureValueDTO, ProductDTO} from 'common/generated-types/types';
import {ShopRoute} from 'common/constants';
import {productFeaturesService} from 'aa/shop/services/productFeaturesService';
import OtherHardwareCompatibilityDialog from 'aa/shop/productsoverview/components/categorydetails/OtherCompatibleHardwareDialog.vue';
import MinPriceDisplay from 'common/components/MinPriceDisplay.vue';
import {userPermissionService} from 'common/services';
import SpecialOfferChip from 'aa/shop/core/components/specialoffer/SpecialOfferChip.vue';

@Component({
  components: {SpecialOfferChip, MinPriceDisplay, OtherHardwareCompatibilityDialog}
})
export default class CategoryProductDetails extends Vue {
  @Prop() product!: ProductDTO;
  @Prop() hideChips?: boolean;
  @Prop() hidePrices?: boolean;
  @Prop() thlAppCodeInCart?: string;
  @Prop() hasNewInfoOnApp?: boolean;

  get hardwareRequirementsForProduct(): FeatureValueDTO[] {
    return productFeaturesService.hardwareRequirementsForProduct(this.product);
  }

  get productSelectionUrl(): string {
    return ShopRoute.PRODUCT_SELECTION(this.product.code);
  }

  get purchaseButtonUrl(): string {
    return userPermissionService.isAnonymous() ? ShopRoute.LOGIN_REDIRECT(this.productSelectionUrl) : this.productSelectionUrl;
  }

  get isAnonymous(): boolean {
    return userPermissionService.isAnonymous();
  }
  get hasSpecialOffer(): boolean {
      return !this.hidePrices && this.product.specialOffer;
  }

}
</script>
