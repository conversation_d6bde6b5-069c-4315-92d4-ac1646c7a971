import * as Sizes from 'common/testtools/testScreenSizes';

describe('CategoryProductDetails', () => {

    it('component looks as expected', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-CategoryProductDetails');

        await Sizes.testLargeScreensize(500);
        await Sizes.testSmallScreensize(500);
    });
});
