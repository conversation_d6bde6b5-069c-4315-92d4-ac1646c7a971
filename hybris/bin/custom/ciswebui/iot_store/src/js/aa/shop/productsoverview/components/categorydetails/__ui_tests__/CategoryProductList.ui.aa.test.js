import * as Sizes from 'common/testtools/testScreenSizes';
import {cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";

describe('CategoryProductList', () => {

    beforeEach(async () => {
        await page.setRequestInterception(true);
        page.on('request', coreDataRestHandler);
        await page.setCookie(...cookies_en_AT);
    });

    it('component looks as expected', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-CategoryProductList');

        await Sizes.testLargeScreensize(2000);
        await Sizes.testSmallScreensize(2000);
    });
});
