<template>
  <v-banner v-bind="$attrs"
            v-on="$listeners"
            class="banner text-left"
            size="10">

    <div class="ml-12" style="color: white"
         data-id="text-banner-content">
      <slot></slot>
    </div>

    <template v-slot:actions="{ dismiss }">
      <slot name="actions"></slot>

      <CDButtonTextIcon v-if="closeable"
                        @click="dismiss"
                        icon="$close"
                        small
                        color="white"
                        class="mr-2">
      </CDButtonTextIcon>
    </template>
  </v-banner>
</template>

<script setup lang="ts">

withDefaults(defineProps<{ closeable?: boolean; }>(), {
  closeable: false
});

</script>

<style scoped lang="scss">
  .banner {
    background: linear-gradient(90deg, #533C8F 0%, var(--v-primary-base) 100%);
  }

  ::v-deep .v-banner__actions {
    margin-bottom: 0;
  }
</style>

