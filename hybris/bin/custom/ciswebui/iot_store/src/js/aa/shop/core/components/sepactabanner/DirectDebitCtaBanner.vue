<template>
  <StoreBanner data-id="container-direct-debit-cta-banner">
    {{ $t('shop.directDebitCtaBanner.text') }}
    <template v-slot:actions>
      <CDButton :href="paymentDetailsEntry.url"
                class="mr-2"
                data-id="button-direct-debit-cta-banner">
        {{ $t('shop.directDebitCtaBanner.buttonText') }}
      </CDButton>
    </template>
  </StoreBanner>
</template>

<script setup lang="ts">
import StoreBanner from 'aa/shop/core/components/StoreBanner.vue';
import {computed} from 'vue';
import {navigationService} from 'common/services';

const paymentDetailsEntry = computed(() => navigationService.byItemCode('storePaymentDetails'));

</script>

