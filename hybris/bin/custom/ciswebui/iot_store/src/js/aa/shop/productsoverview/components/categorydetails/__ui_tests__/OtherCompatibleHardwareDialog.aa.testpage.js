import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import OtherCompatibleHardwareDialog from 'aa/shop/productsoverview/components/categorydetails/OtherCompatibleHardwareDialog.vue';

Vue.use(VueI18n);

const hardwareRequirementsData = [
    {
        value: 'KTS 560',
        iconUrl: 'sample-data/aa/products/hardware-features/kts-560.png'
    },
    {
        value: 'KTS 590',
        iconUrl: 'sample-data/aa/products/hardware-features/kts-590.png'
    },
    {
        value: 'KTS 465',
        iconUrl: 'sample-data/aa/products/hardware-features/kts-465.png'
    },
    {
        value: 'KTS 960',
        iconUrl: 'sample-data/aa/products/hardware-features/kts-960.png'
    }
];

new Vue({
    el: '#vue-app',
    vuetify,
    i18n,
    components: {
        TestPageRoot,
        OtherCompatibleHardwareDialog
    },
    data: {
        hardwareRequirements: hardwareRequirementsData
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <other-compatible-hardware-dialog :hardware-requirements="hardwareRequirements">
          </other-compatible-hardware-dialog>
        </div>
      </test-page-root>
    `
});
