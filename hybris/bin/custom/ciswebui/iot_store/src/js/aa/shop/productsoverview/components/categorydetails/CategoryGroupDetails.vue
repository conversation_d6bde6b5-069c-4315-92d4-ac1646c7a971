<template>
  <div class="products-category-details mb-16"
       :data-id="`container-category-group-details-${categoryGroup.code}`">

    <category-details v-for="categoryInGroup in categoryGroup.subcategories"
                      :key="categoryInGroup.code"
                      :category="categoryInGroup"
                      :category-group-name="categoryGroup.name">
    </category-details>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryHierarchyDTO} from 'common/generated-types/types';
import CategoryProductList from 'aa/shop/productsoverview/components/categorydetails/CategoryProductList.vue';
import CategoriesExpansionPanels from 'aa/shop/productsoverview/components/categorydetails/CategoriesExpansionPanels.vue';
import CategoryDetails from 'aa/shop/productsoverview/components/categorydetails/CategoryDetails.vue';

/**
 * By convention a Category group contains only one sub-category.
 * The categories displayed in this component are at the second level of the category hierarchy.
 * e.g cat_1 in
 * -> main
 * ----> cat_1: Steuergeräte-Diagnose
 **/
@Component({
  components: {CategoryDetails, CategoriesExpansionPanels, CategoryProductList}
})
export default class CategoryGroupDetails extends Vue {
  @Prop() categoryGroup!: CategoryHierarchyDTO;
}
</script>
