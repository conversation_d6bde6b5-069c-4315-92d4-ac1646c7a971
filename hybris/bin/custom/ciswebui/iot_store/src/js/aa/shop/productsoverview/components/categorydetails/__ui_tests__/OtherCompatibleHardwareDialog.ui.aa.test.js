import * as Sizes from 'common/testtools/testScreenSizes';

describe('OtherCompatibleHardwareDialog', () => {

    it('activator looks as expected', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-OtherCompatibleHardwareDialog');

        await Sizes.testLargeScreensize(400);
        await Sizes.testSmallScreensize(400);
    });

    it('dialog looks as expected', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-OtherCompatibleHardwareDialog');
        await page.waitFor(400);

        const activatorSelector = '[data-id="button-other-compatible-hardware-dialog-activator"]';
        await page.waitForSelector(activatorSelector);
        await page.click(activatorSelector);

        await Sizes.testAllScreenSizes(1000);
    });
});
