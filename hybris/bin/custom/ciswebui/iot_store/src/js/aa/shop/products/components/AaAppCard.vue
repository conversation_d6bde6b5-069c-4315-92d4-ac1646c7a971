<template>
  <v-card elevation="0" class="my-5 my-md-3">

    <v-row no-gutters dense>

      <v-col cols="auto" class="mr-md-5">
        <img :src="appCardData.img"
             :width="$vuetify.breakpoint.mdAndUp ? 80 : 56"
             :height="$vuetify.breakpoint.mdAndUp ? 80 : 56"
             class="rounded-circle logo"
             alt="logo"/>
      </v-col>

      <v-col md>
        <v-card-title class="black--text pa-0 ml-2 mb-4" style="line-height: 1">
          <v-row no-gutters dense>
            <v-col cols="12" md="auto" class="mb-2 mb-md-0">
              <span class="text-h4 mr-2">{{ appCardData.title }}</span>
            </v-col>
            <v-col cols="12" md class="text-no-wrap">
              <CDChip v-for="(chip, i) in appCardData.chips"
                      :key="`app-card-chip-${i}`"
                      disabled
                      class="mr-2"
                      small>
                {{ chip.text }}
              </CDChip><special-offer-chip
                  v-if="isSpecialOffer"
                  disabled
                  class="mr-2"
                  small
                  :data-id="`container-special-price-chip-product-${appCardData.code}`"></special-offer-chip>
            </v-col>
          </v-row>
        </v-card-title>

        <v-card-text v-if="$vuetify.breakpoint.mdAndUp" class="pa-0 ml-2" @click.once="showFullDescription">
          <p class="body-1 mb-0 black--text" :class="{ 'd-block': fullDescriptionVisible }">
            {{ appCardData.body }}
          </p>
        </v-card-text>
      </v-col>

      <template v-if="!hidePrices">
        <v-col v-if="$vuetify.breakpoint.mdAndUp" cols="auto" class="d-flex flex-column align-end">
            <min-price-display v-if="minPrice" :min-price="minPrice"
                               :data-id="`text-product-min-price-${appCardData.code}`" class="text-h5 mb-4">
            </min-price-display>
          <v-card-actions class="pa-0">
            <CDButton color="primary" :href="appCardData.url" :data-id="`button-buy-product-${appCardData.code}`">
              {{ $t('shop.products.buyButton') }}
            </CDButton>
          </v-card-actions>
        </v-col>
      </template>

      <v-col cols="12" v-if="$vuetify.breakpoint.smAndDown">
        <v-card-text class="pa-0" @click.once="showFullDescription">
          <p class="body-1 mb-8 black--text" :class="{ 'd-block': fullDescriptionVisible }">
            {{ appCardData.body }}
          </p>
        </v-card-text>
      </v-col>

      <template v-if="!hidePrices">
        <v-col cols="12" v-if="$vuetify.breakpoint.smAndDown">
          <v-card-actions class="pa-0 mt-auto">
            <CDButton color="primary" :href="appCardData.url" :data-id="`button-buy-product-${appCardData.code}-mobile`">
              {{ purchaseButtonLabel }}
            </CDButton>
          </v-card-actions>
        </v-col>
      </template>

    </v-row>
  </v-card>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {VCard, VCardActions, VCardText, VCardTitle} from 'vuetify/lib';
import {AppCardInterface} from 'common/types';
import MinPriceDisplay from 'common/components/MinPriceDisplay.vue';
import {commercefacades, PriceFrequency} from 'common/generated-types/types';
import {i18nService} from 'common/services';
import SpecialOfferChip from 'aa/shop/core/components/specialoffer/SpecialOfferChip.vue';
import PriceData = commercefacades.PriceData;

@Component({
  components: {SpecialOfferChip, MinPriceDisplay, VCard, VCardActions, VCardText, VCardTitle}
})

export default class AaAppCard extends Vue {
  @Prop() appCardData!: AppCardInterface;
  @Prop() hidePrices!: boolean;
  dataId!: string;
  fullDescriptionVisible = false;

  created() {
    this.dataId = `text-product-min-price-${this.appCardData.code}`;
  }
  get i18nService() {
    return i18nService;
  }

  get minPrice(): PriceData | undefined {
    return this.appCardData.priceInfo?.minPrice;
  }

  get isSpecialOffer(): boolean {
    return !!this.appCardData.priceInfo?.specialOffer;
  }

  get hasMinPrice(): boolean {
    return Boolean(this.minPrice);
  }

  get purchaseButtonLabel(): string {
      if (this.hasMinPrice) {
          const { currencyIso, value, priceFrequency } = this.appCardData.priceInfo!.minPrice!;
          const price = this.$n(value, 'price', i18nService.getLocaleForFormats());
          if (priceFrequency === PriceFrequency.YEARLY) {
              const frequency = this.$t('shop.products.perYear');
              return this.$t('shop.products.buyButtonWithPriceFrequency', { currency: currencyIso, price, frequency }) as string;
          }
          return this.$t('shop.products.buyButtonWithPrice', { currency: currencyIso, price }) as string;
      } else {
          return this.$t('shop.products.buyButton') as string;
      }
  }

  showFullDescription() {
    this.fullDescriptionVisible = true;
  }
}
</script>

<style lang="scss" scoped>
@import 'cd-system/tokens/scss/mixins';

.v-card {
  width: 100%;

  .logo {
    border: 1px solid var(--v-grey-lighten3);
  }

  .v-card__text {
    width: 85%;

    p {
      @include cd-line-clamp(2);
    }
  }

  .v-card__actions .v-btn {
    width: 100%;
  }
}
</style>
