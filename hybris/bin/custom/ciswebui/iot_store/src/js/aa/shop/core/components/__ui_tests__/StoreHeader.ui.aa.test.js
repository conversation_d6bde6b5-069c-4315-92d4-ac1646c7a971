import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from 'common/testtools/inlineTestPageCreator';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cookies_en_AT, coreDataRestHandler, countryCookie, languageCookie} from 'common/testtools/testCookies';
import {respondRequest} from 'common/testtools/testRequests';
import {testMap} from 'common/testtools/scenariosstore';
import categories from 'common/testtools/scenariosstore/aa/categories.json';
import navigationItemsForAuthenticatedUser from 'common/testtools/scenariosstore/aa/navigationItemsForAuthenticatedUser.json';
import {cloneDeep} from 'lodash';

describe('StoreHeader', () => {

    const REST_URL = '/shop/api/categories/main';
    const productsCategoriesRestHandler = respondRequest(REST_URL, {
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(categories)
    });

    const productsOverviewAA = cloneDeep(testMap.ProductsOverviewAA);
    productsOverviewAA.coreData.moduleConfig.ENABLE_IMPROVED_NAVIGATION = false;

    beforeEach(async () => {

        await page.setRequestInterception(true);
        await page.setCookie(...cookies_en_AT);
    });

    it('looks as expected', async () => {
        page.on('request', productsCategoriesRestHandler);

        await createInlineUiTestPage(productsOverviewAA);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1000);
        await Sizes.testLargeScreensize(1000);
        await Sizes.testMediumScreensize(1000);
        await Sizes.testSmallScreensize(1000);
        await Sizes.testExtraSmallScreensize(1000);
    });

    it('displays item count in the cart', async () => {
        page.on('request', productsCategoriesRestHandler);

        const data = cloneDeep(productsOverviewAA);
        data.coreData.numberOfCartItems = 5;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testLargeScreensize(1000);
        await Sizes.testSmallScreensize(1000);
    });

    describe('with i18n cookies', () => {

        const cookies = [languageCookie('en'), countryCookie('AT')];

        beforeEach(async () => {
            await page.setCookie(...cookies);
        });

        afterEach(async () => {
            await page.deleteCookie(...cookies);
        });

        it('hides cart button if store region is different from company country', async () => {
            page.on('request', productsCategoriesRestHandler);

            const data = cloneDeep(productsOverviewAA);
            data.coreData.moduleConfig.LANGUAGE_SWITCHER = true;
            data.coreData.currentCompany.companyCountry = 'PT';

            await createInlineUiTestPage(data);
            await acceptCookieBanner();
            await resetMouseCoords();

            await Sizes.testExtraLargeScreensize(1000);
            await Sizes.testLargeScreensize(1000);
            await Sizes.testMediumScreensize(1000);
            await Sizes.testSmallScreensize(1000);
            await Sizes.testExtraSmallScreensize(1000);
        });
    });

    it('hides cart button if company is managed account', async () => {
        page.on('request', productsCategoriesRestHandler);

        const data = cloneDeep(productsOverviewAA);
        data.coreData.moduleConfig.DIRECT_SALES = true;
        data.coreData.currentCompany.managedAccount = true;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1000);
        await Sizes.testLargeScreensize(1000);
        await Sizes.testMediumScreensize(1000);
        await Sizes.testSmallScreensize(1000);
        await Sizes.testExtraSmallScreensize(1000);
    });

    it('has correct design for interaction', async () => {
        page.on('request', productsCategoriesRestHandler);

        const data = cloneDeep(productsOverviewAA);
        data.coreData.navigationItems = navigationItemsForAuthenticatedUser;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        const interactionMatchesImageSnapshot = async (promise, identifier) => {
            await promise;
            await page.waitFor(1000);
            expect(await page.screenshot()).toMatchImageSnapshot({
                failureThreshold: 1,
                failureThresholdType: 'percent',
                customSnapshotIdentifier: identifier
            });
        };

        await interactionMatchesImageSnapshot(page.hover('.cd-icon-home'), 'hovering on home');
        await interactionMatchesImageSnapshot(page.hover('[data-id="button-flyout"]'),'hovering on user icon');
        await interactionMatchesImageSnapshot(page.hover('[data-id="link-cart-page"]'), 'hovering on cart icon');
        await interactionMatchesImageSnapshot(page.click('[data-id="button-flyout"]'), 'clicking on user icon');
        await interactionMatchesImageSnapshot(page.hover('a.v-list-item'), 'hovering on menu item');
    });
});
