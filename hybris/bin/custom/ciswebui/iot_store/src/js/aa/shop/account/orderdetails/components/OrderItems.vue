<template>
  <v-row>
    <v-col>
      <div v-for="(entry, i) in orderEntries"
           :key="entry.productCode"
           :data-id="`container-order-entry-${i}`">
        <v-card tile elevation="0">
          <v-list-item dense class="pl-0">
            <v-list-item-icon>
              <a :href="isUserInCompanyCountry ? productSelectionUrl(entry) : undefined"
                 :data-id="`link-order-entry-product-url-${i}`">
                <img alt="logo"
                     :src="entry.smallLogoUrl"
                     class="entry-logo rounded-circle"/>
              </a>
            </v-list-item-icon>
            <v-list-item-content>
              <p class="text-h3 mt-1"
                 :data-id="`text-order-entry-product-name-${i}`">
                {{ entry.productName }}
              </p>
              <p class="mt-1"
                 :data-id="`text-order-entry-seller-product-id-${i}`">
                {{ entry.sellerProductId }}
              </p>
              <p class="mt-1"
                 :data-id="`text-order-entry-company-name-${i}`">
                {{ $t('shop.orderDetails.items.by') }} {{ entry.companyName }}
              </p>
              <p class="mt-1" v-if="entry.licenseType"
                 :data-id="`text-order-entry-license-name-${i}`">
                  {{ $t(`shop.license.${entry.licenseType.toLowerCase()}.name`) }}
              </p>
              <p class="mt-1"
                 :data-id="`text-order-entry-quantity-${i}`">
                {{ $t('shop.orderDetails.items.quantity.label') }}: {{ entry.quantity }}
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <CDIcon v-on="on" dense>$info</CDIcon>
                  </template>
                  {{ $t('shop.orderDetails.items.quantity.tooltipText') }}
                </v-tooltip>
              </p>
            </v-list-item-content>
          </v-list-item>
        </v-card>
      </div>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CartItemData, ShopCoreData} from 'common/generated-types/types';
import {BaseStores, ShopRoute} from 'common/constants';
import {ShopCoreDataProvider} from 'common/provider/coreDataProvider';
import {i18nService} from 'common/services';

@Component
export default class OrderItems extends Vue {
  @Prop() orderEntries!: Array<CartItemData>;

  coreData: ShopCoreData = ShopCoreDataProvider.data;
  BaseStores = BaseStores;

  get isDirectSalesEnabled(): boolean {
    return Boolean(this.coreData.moduleConfig?.DIRECT_SALES) && this.coreData.basestore === BaseStores.AA;
  }

  get isLanguageSwitcherEnabled(): boolean {
    return Boolean(this.coreData.moduleConfig?.LANGUAGE_SWITCHER) && this.coreData.basestore === BaseStores.AA;
  }

  productSelectionUrl(entry: CartItemData): string {
    if(this.isDirectSalesEnabled) {
      return ShopRoute.PRODUCT_SELECTION(entry.appCode);
    } else {
      return entry.productUrl;
    }
  }

  get isUserInCompanyCountry() : boolean {
    return !this.isLanguageSwitcherEnabled || i18nService.isUserInCompanyCountry();
  }
}
</script>

<style lang="scss" scoped>

.entry-logo {
  border: 1px solid white;
  border-radius: 6px;
}

</style>
