<template>
  <v-container class="mb-5 pl-0 mt-0 pt-0 pr-0" v-if="userCompanyUnderReview" data-id="account-notification">
    <CDNotificationPersistent type="info" class="alert-info mb-0 mt-4">
      <p class="mb-0">{{$t('shop.notification.accountUnderReview.summary')}}</p>
      <p class="mb-0"><a target="_blank" rel="noopener noreferrer" data-id="account-under-review-learn-more"
            @click.prevent="openLearnMore">{{$t('shop.productDetails.pricingSection.learnMore')}}</a></p>
    </CDNotificationPersistent>
    <DefaultStoreDialog v-model="showLearnMore"
                        width="500"
                        v-if="showLearnMore"
                        :fullscreen="$vuetify.breakpoint.mdAndDown"
                        @close="handleCloseDialog"
                        @keydown.esc="handleCloseDialog"
                        capitalizetitle="false"
                        data-id="container-account-under-review-learn-more">
      <template #header>{{$t('shop.notification.accountUnderReview.title')}}</template>
      <div data-id="container-account-under-review-description">
        <v-row class="mb-8 mt-1 ml-1">
          <p>{{$t('shop.notification.accountUnderReview.summary')}}</p>
          <ul>
            <li class="mt-4">{{$t('shop.notification.accountUnderReview.description.licenses')}}</li>
            <li class="mt-4">{{$t('shop.notification.accountUnderReview.description.payments')}}</li>
            <li class="mt-4">{{$t('shop.notification.accountUnderReview.description.orders')}}</li>
          </ul>
        </v-row>
        <v-row justify="end">
          <div class="button-wrapper mr-4 mb-1">
            <CDButton id="accountUnderReview-close-btn"
                      @click="handleCloseDialog"
                      color="primary"
                      data-id="accountUnderReview-close-btn"
                      large block class="accountUnderReview-close-btn">
              {{ $t('shop.productsOverview.category.otherCompatibleHardwareDialog.closeButton') }}
            </CDButton>
          </div>
        </v-row>
      </div>
    </DefaultStoreDialog>
  </v-container>
</template>

<script lang="ts">
import {Vue, Component, Prop} from 'vue-property-decorator';
import DefaultStoreDialog from 'common/components/DefaultStoreDialog.vue';
@Component({
  components: {DefaultStoreDialog}
})
export default class AccountNotification extends Vue {
  @Prop({ default: false }) userCompanyUnderReview!: boolean;

  showLearnMore = false;

  openLearnMore(): void {
    this.showLearnMore = true;
  }

  handleCloseDialog(): void {
    this.showLearnMore = false;
  }
}
</script>


<style scoped lang="scss">
</style>