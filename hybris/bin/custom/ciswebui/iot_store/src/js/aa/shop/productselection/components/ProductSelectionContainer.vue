<template>
  <v-container fluid class="pa-0">
    <runtime-selection
        v-model="selectedRuntime"
        :product="product">
    </runtime-selection>
    <thl-notification v-if="productSelectionStore?.isThlBundleAvailable">
      {{
        $t(
            'shop.thl.notification',
            {
                productName: productSelectionStore.appName,
                bundleProductName: product.addonProductName
            }
          )
      }}
    </thl-notification>
    <variant-selection
        v-if="showVariantSelection"
        v-on="$listeners"
        :discount-available="productSelectionStore?.isThlBundleAvailable"
        :variants="variantsPerRuntime[selectedRuntime]">
    </variant-selection>
    <special-offer-conditions v-if="showSpecialOfferConditions" class="mt-16">
    </special-offer-conditions>
  </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ProductLicenseData, SimpleProductData} from 'common/generated-types/types';
import {groupBy, isEmpty} from 'lodash';
import VariantItem from 'aa/shop/productselection/components/VariantItem.vue';
import RuntimeSelection from 'aa/shop/productselection/components/RuntimeSelection.vue';
import VariantSelection from 'aa/shop/productselection/components/VariantSelection.vue';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import {userPermissionService} from 'common/services';
import SpecialOfferConditions from 'aa/shop/core/components/specialoffer/SpecialOfferConditions.vue';
import ThlNotification from 'aa/shop/thl/components/ThlNotification.vue';
import {useProductSelectionStore} from 'shop/store/productSelection';

@Component({
  components: {ThlNotification, SpecialOfferConditions, VariantSelection, RuntimeSelection, VariantItem}
})
export default class ProductSelectionContainer extends Vue {
  @Prop() product!: SimpleProductData;

  selectedRuntime = '';
  storeRootStore = useStoreRootStore();
  productSelectionStore = useProductSelectionStore();

  get variantsPerRuntime(): Record<string, ProductLicenseData[]> {
    return groupBy(this.product.licenses, (variant) => variant.runtime.code);
  }

  get showVariantSelection(): boolean {
    return !isEmpty(this.selectedRuntime)
        && !userPermissionService.isAnonymous()
        && !this.storeRootStore.isManagedAccount;
  }

  get showSpecialOfferConditions(): boolean {
    return this.product.licenses?.some((product) => product.specialOffer);
  }
}
</script>
<style lang="scss" scoped>

.selected-runtime {
  border-color: var(--v-black-lighten1);
  border-width: 2px;
}

</style>
