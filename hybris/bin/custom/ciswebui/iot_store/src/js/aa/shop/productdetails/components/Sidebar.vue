<template>
  <div class="sidebar">

    <v-row>

      <v-col cols="12" v-if="!!($slots && $slots['default'])">
        <div class="mb-4">
          <slot></slot>
        </div>
      </v-col>

      <v-col cols="12" md="6" xl="12" class="pb-sm-0 pb-md-4 pb-xl-0">
        <div class="sidebar-item mb-4" data-id="text-productid">
          <h4>{{$t('shop.productDetails.sidebar.productId')}}</h4>
          <p>{{appCode}}</p>
        </div>
        <div class="sidebar-item mb-4" data-id="text-partnumber">
          <h4>{{$t('shop.productDetails.sidebar.partnumber')}}</h4>
          <p>{{partNumber}}</p>
        </div>
      </v-col>

      <v-col cols="12" md="6" xl="12" class="pt-sm-0 pt-md-4 pt-xl-0">
        <template v-if="!userPermissionService.isAnonymous()">
          <div class="sidebar-item mb-4" v-if="appContactInfo.productWebsiteUrl" data-id="text-companywebsite">
            <h4>{{$t('shop.productDetails.sidebar.companyWebsite')}}</h4>
            <a class="product-website-url" v-bind:href="appContactInfo.productWebsiteUrl"
               target="_blank">{{appContactInfo.productWebsiteUrl}}</a>
          </div>
        </template>

        <div v-if="isThirdPartyApp" class="sidebar-item mb-4" data-id="text-privacypolicy-app">
          <h4>{{ $t('shop.productDetails.sidebar.privacyPolicyApp.title') }}</h4>
          <p>{{ $t('shop.productDetails.sidebar.privacyPolicyApp.description') }}</p>
          <a class="privacy-policy" v-bind:href="appContactInfo.privacyPolicyUrl" target="_blank">
            {{ $t('shop.productDetails.sidebar.privacyPolicyApp.link') }}
          </a>
        </div>

        <div v-if="isThirdPartyApp && eulaAvailable" class="sidebar-item mb-4 terms-of-use" data-id="text-termsofuse-app">
          <h4>{{ $t('shop.productDetails.sidebar.termsOfUseApp.title') }}</h4>
          <a :href="eulaUrl" target="_blank" class="eula-url">
            {{ $t('shop.productDetails.sidebar.termsOfUseApp.link') }}
          <CDChip v-if="termsType === EulaType.STANDARD"
                  outlined x-small
                  class="chip-standard-eula"
                  data-id="chip-standard-eula">
                {{ $t('shop.productDetails.sidebar.termsOfUseApp.standardMark') }}
          </CDChip>
          </a>
          <a v-if="hasAppendix" class="appendix-url" v-bind:href="appendixUrl" target="_blank">
            {{ $t('shop.productDetails.sidebar.termsOfUseApp.appendix') }}
          </a>
        </div>
      </v-col>

    </v-row>
  </div>
</template>

<script lang="ts">
import {Vue, Component, Prop} from 'vue-property-decorator';
import {AppContactData, commercefacades, EulaData, EulaType} from 'common/generated-types/types';
import {userPermissionService} from 'common/services';
@Component({
  components: {}
})
export default class Sidebar extends Vue {
  @Prop() appCode!: string;
  @Prop() versionName!: string;
  @Prop() acquisitionCount!: number;
  @Prop() partNumber!: string;
  @Prop() appContactInfo!: AppContactData;
  @Prop() enabledCountries!: Array<commercefacades.CountryData>;
  @Prop() isThirdPartyApp!: boolean;
  @Prop() isPrivateApp!: boolean;

  EulaType = EulaType;
  userPermissionService = userPermissionService;

  get eulaAvailable(): boolean {
    return !!this.appContactInfo.eula;
  }

  get eulaUrl(): string {
    const eulaData: EulaData = this.appContactInfo.eula;
    return eulaData?.type === EulaType.CUSTOM ? eulaData.customUrl : '';
  }

  get appendixUrl(): string {
    return this.hasAppendix ? this.appContactInfo.eula.standardEulaAppendix.url : '';
  }

  get termsType(): EulaType {
    return this.appContactInfo.eula?.type;
  }

  get hasAppendix(): boolean {
    const eulaData = this.appContactInfo.eula;
    return eulaData?.type === EulaType.STANDARD && eulaData.standardEulaAppendix !== null &&
        eulaData.standardEulaAppendix.url !== null;
  }

}
</script>
