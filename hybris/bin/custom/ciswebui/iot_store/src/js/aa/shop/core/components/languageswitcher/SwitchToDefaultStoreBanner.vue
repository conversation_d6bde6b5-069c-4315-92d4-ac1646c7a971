<template>
  <StoreBanner>
    {{ $t('shop.infoBanner.text') }}
    <template v-slot:actions>
      <CDButton @click="returnToCompanyDefaultStore()" class="mr-12" data-id="button-switch-to-default-store">{{ $t('shop.infoBanner.buttonText') }}</CDButton>
    </template>
  </StoreBanner>
</template>

<script setup lang="ts">
import StoreBanner from 'aa/shop/core/components/StoreBanner.vue';
import {i18nService, messageService} from 'common/services';
import {useI18n} from 'vue-i18n-bridge';

const { tm } = useI18n();

function returnToCompanyDefaultStore(): void {
  i18nService.goToCompanyDefaultMarketPlace()
      .catch(error => {
        const backendError = tm('shop.error.backend') as string;
        if (error.response && error.response.status === 400) {
          const generalError = error.response.data;
          if (generalError) {
            messageService.error(tm(generalError) as string);
          } else {
            messageService.error(backendError);
          }
        } else {
          messageService.error(backendError);
        }
      })
      .then(() => {
        window.location.reload();
      });
}
</script>

