<template>
  <DefaultStoreDialog
      v-bind="$attrs"
      v-on="$listeners"
      :fullscreen="handleFullscreen"
      @close="$emit('close')">

    <template #header>
      {{ $t('shop.productsOverview.category.packetComparisonDialog.title') }}
    </template>

    <p class="my-4">{{ $t('shop.productsOverview.category.packetComparisonDialog.description') }}</p>

    <div class="comparison-wrapper">

      <template v-if="$vuetify.breakpoint.lgAndUp">
        <div data-id="container-comparison-dialog-content">
          <v-row no-gutters class="product-row" :class="{ nowrap : manyProducts }">
            <v-col v-for="(product, index) in sortedProducts" :key="`product-${product.code}`"
                   :lg="columnCalculator"
                   :offset-lg="handleOffset(index)"
                   class="text-center product">
              <v-card-text>{{ product.name }}</v-card-text>
            </v-col>
          </v-row>

          <v-row v-for="material in billsOfMaterials"
                 :key="`material-${material.code}`"
                 :data-id="`material-${material.code}`"
                 class="material-row mb-4"
                 :class="{ nowrap : manyProducts }"
                 no-gutters>

            <v-col class="material-name"
                   :key="material.code"
                   cols="12"
                   :lg="materialsCols()">
              <v-card-text>
                <a href="#" @click.prevent="showMaterialDialog(material)" :data-id="`button-material-name-${material.code}`">{{ material.name }}</a>
              </v-card-text>
            </v-col>

            <v-col v-for="product in sortedProducts" :key="`product-${product.code}`"
                   class="d-flex justify-center align-center material-icon"
                   :lg="columnCalculator">
              <template v-if="materialIncluded(material, product)">
                <span v-if="material.code === 'MAT_040THLPKW'">{{ $t('shop.productsOverview.category.packetComparisonDialog.optional') }}</span>
                <CDIcon v-else
                        icon="$complete"
                        color="black"
                        :data-id="`icon-complete-${material.code}`"></CDIcon>
              </template>
            </v-col>
          </v-row>
        </div>
      </template>

      <template v-else>
        <div data-id="container-comparison-dialog-content-mobile">
          <v-row v-for="(material, i) in billsOfMaterials"
                 :key="`material-${material.code}`"
                 :data-id="`material-${material.code}`"
                 :class="{ 'mb-12' : i === billsOfMaterials.length - 1 }">
            <v-col cols="12">
              <a href="#" @click.prevent="showMaterialDialog(material)" :data-id="`button-material-name-${material.code}-mobile`">{{material.name}}</a>
            </v-col>
            <v-row class="no-gutters nowrap">
              <v-col v-for="(product) in sortedProducts" :key="`product-${product.code}`"
                     class="d-flex flex-column align-center text-center material-icon"
                     :class="{ 'column-spacer' : $vuetify.breakpoint.mdAndDown}">
                <CDIcon v-if="materialIncluded(material, product)"
                        icon="$complete"
                        color="black"
                        :data-id="`icon-complete-${material.code}`"></CDIcon>
                 <CDIcon v-else icon="$close"
                         color="black"
                         :data-id="`icon-close-${material.code}`"></CDIcon>
                <p class="mt-4">{{product.name}}</p>
              </v-col>
            </v-row>
          </v-row>
        </div>
      </template>

      <v-row :class="{ nowrap : manyProducts, 'sticky-ctas white nowrap' : $vuetify.breakpoint.mdAndDown }">
        <v-col v-for="(product, index) in sortedProducts"
               :key="`product-${product.code}`"
               :lg="columnCalculator"
               :offset-lg="handleOffset(index)"
               class="d-flex justify-center"
               :class="{ 'column-spacer white pt-8 pt-lg-0' : $vuetify.breakpoint.mdAndDown }">
          <CDButton class="text-center justify-center"
                    :href="purchaseButtonUrl(product)"
                    :data-id="`button-purchase-${product.code}`">
            {{ $t('shop.productsOverview.category.packetComparisonDialog.buyButton') }}
          </CDButton>
        </v-col>
      </v-row>

      <DefaultStoreDialog v-model="materialDialog"
                          v-if="materialDialog"
                          :fullscreen="$vuetify.breakpoint.mdAndDown"
                          @close="handleCloseMaterialDialog"
                          @keydown.esc="handleCloseMaterialDialog"
                          closeiconmobile="$arrowleft"
                          capitalizetitle="false"
                          data-id="container-materials-dialog">
        <template #header>{{ activeMaterial.name }}</template>
        <div data-id="container-material-content">
          <youtube-player v-if="activeMaterial.videoUrl"
                          class="youtube-player mb-4"
                          :videoUrl="activeMaterial.videoUrl"
                          data-id="player-youtube">
          </youtube-player>
          <p>{{ activeMaterial.description }}</p>
        </div>
      </DefaultStoreDialog>

    </div>

    <div class="hidden-input">
      <input type="text" ref="hiddenInput" />
    </div>

  </DefaultStoreDialog>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import DefaultStoreDialog from 'common/components/DefaultStoreDialog.vue';
import {MaterialDTO, ProductDTO} from 'common/generated-types/types';
import CategoryProductDetails from 'aa/shop/productsoverview/components/categorydetails/CategoryProductDetails.vue';
import {ShopRoute} from 'common/constants';
import {userPermissionService} from 'common/services';
import {YoutubePlayer} from 'common/components/videoplayers';

@Component({
  components: {
    CategoryProductDetails,
    DefaultStoreDialog,
    YoutubePlayer
  }
})
export default class PacketComparisonDialog extends Vue {
  @Prop() billsOfMaterials!: MaterialDTO[];
  @Prop() products!: ProductDTO[];

  $refs!: {
    hiddenInput: HTMLInputElement
  };

  materialDialog = false;
  activeMaterial = {};

  showMaterialDialog(materialSelection: MaterialDTO): void {
    this.activeMaterial = materialSelection;
    this.materialDialog = true;
  }

  handleCloseMaterialDialog(): void {
    this.materialDialog = false;
    this.$nextTick(() => {
      this.$refs.hiddenInput.focus();
    });
  }

  get manyProducts(): boolean {
    return this.products.length > 3;
  }

  get sortedProducts(): ProductDTO[] {
    return this.products.sort((a, b) => a.billsOfMaterials.length - b.billsOfMaterials.length);
  };

  // if rounds down to below 1, set cols to 1, otherwise return a calculated column count
  get columnCalculator(): number {
    const columnCount = Math.floor((12 - this.materialsCols()) / this.products.length);
    return columnCount === 0 ? 1 : columnCount;
  }

  get handleFullscreen(): boolean {
    return this.$vuetify.breakpoint.mdAndDown || this.manyProducts;
  };

  purchaseButtonUrl(product: ProductDTO): string {
    const productSelectionUrl = ShopRoute.PRODUCT_SELECTION(product.code);
    return userPermissionService.isAnonymous() ? ShopRoute.LOGIN_REDIRECT(productSelectionUrl) : productSelectionUrl;
  };

  // different material column width for desktop, depending on odd/even products length (makes layout even)
  materialsCols(): number {
    return this.products.length % 2 === 0 ? 4 : 3;
  };

  // if first column, apply horizontal offset for product columns (desktop only!)
  handleOffset(index: number): number {
    return index === 0 ? this.materialsCols() : 0;
  };

  materialIncluded(material: MaterialDTO, product: ProductDTO): boolean {
    return product.billsOfMaterials.some(item  => item.code === material.code);
  };
}
</script>

<style lang="scss" scoped>
.comparison-wrapper {
  display: flex;
  flex-direction: column;

  .nowrap {
    flex-wrap: nowrap !important;
  }

  // needed to ensure ctas and icons are aligned on tight layouts with many products
  .column-spacer {
    min-width: 140px;
    border-bottom: 1px solid var(--v-grey-lighten2);
  }

  .sticky-ctas {
    position: sticky;
    bottom: -35px;
    padding: 15px;
    margin: -30px;
    .col {
      border-top: 1px solid var(--v-grey-lighten2) !important;
    }
  }
}

// for catching focus to fix esc press close buy in v-dialogs
.hidden-input {
  opacity: 0;
  transform: scale(0,0);
  height: 0 !important;
  input {
    transform: scale(0,0);
    height: 0 !important;
  }
}
</style>
