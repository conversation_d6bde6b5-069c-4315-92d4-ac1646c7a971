import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import DirectDebitCtaBanner from 'aa/shop/core/components/sepactabanner/DirectDebitCtaBanner.vue';
import {wrapperComponentFactory} from "common/testtools/unit-test-utils";

jest.mock('common/util');

const mountComponent = () => {
    return wrapperComponentFactory(DirectDebitCtaBanner, {
        mocks: {},
        shallow: false
    });
};

window.frontendData = {
    coreData: {
        navigationItems: [
            {
                id: "storePaymentDetails",
                url: "/shop/my-account/payment-details",
                text: "Payment Details",
                itemCode: "storePaymentDetails"
            }
        ],
    }
}

describe('DirectDebitCtaBanner', () => {
    it('renders banner correctly', () => {
        const wrapper = mountComponent();

        expect(wrapper.find('[data-id="container-direct-debit-cta-banner"]')).toBeTruthy();
        expect(wrapper.find('[data-id="button-direct-debit-cta-banner"]')).toBeTruthy();
        expect(wrapper.find('[data-id="text-banner-content"]').text()).toBe(('shop.directDebitCtaBanner.text'));
    });
});

