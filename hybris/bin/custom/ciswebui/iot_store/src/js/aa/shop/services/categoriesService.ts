import type {CategoryHierarchyDTO} from 'common/generated-types/types';
import {sortBy} from 'lodash';

const categoriesService = {
    sortCategories: function (categories: CategoryHierarchyDTO[]): CategoryHierarchyDTO[] {
        return sortBy(categories, 'order');
    },

    containsProducts: function (category: CategoryHierarchyDTO): boolean {
        return category.products?.length > 0 || category.subcategories?.some(categoriesService.containsProducts) || false;
    },

    filterEmptyCategories: function (categories: CategoryHierarchyDTO[]): CategoryHierarchyDTO[] {
        return categories.filter(categoriesService.containsProducts);
    },

    containsSpecialOfferProducts: function (category: CategoryHierarchyDTO): boolean {
        return category.products?.some((product) => product.specialOffer)
            || category.subcategories?.some(categoriesService.containsSpecialOfferProducts)
            || false;
    }
};

export {categoriesService};
