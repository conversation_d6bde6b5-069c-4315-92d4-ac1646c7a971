import * as Sizes from 'common/testtools/testScreenSizes';
import {cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";

describe('CategoriesExpansionPanels', () => {
    beforeEach(async () => {
        await page.setRequestInterception(true);
        page.on('request', coreDataRestHandler);
        await page.setCookie(...cookies_en_AT);
    });

    it('component looks as expected', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-CategoriesExpansionPanels');

        await Sizes.testLargeScreensize(2000);
        await Sizes.testSmallScreensize(2000);
    });

    it('expanded component looks as expected', async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/aa-shop-productsoverview-components-categorydetails-__ui_tests__-CategoriesExpansionPanels');

        await page.click('[data-id="container-expansion-panel-header-cat_10102"]');
        await page.waitFor(1000);

        await Sizes.testLargeScreensize(2000);
        await Sizes.testSmallScreensize(2000);
    });
});
