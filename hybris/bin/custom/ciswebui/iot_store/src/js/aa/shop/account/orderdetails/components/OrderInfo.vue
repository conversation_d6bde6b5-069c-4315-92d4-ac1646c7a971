<template>
  <div>
    <v-row>
      <v-col>
        <span class="text-h3">{{ $t('shop.orderDetails.orderInfo.label') }}</span>
      </v-col>
    </v-row>
    <v-row class="mt-4">
      <v-col>
        <div class="mb-1">
          <span class="text-h4">
            {{ $t('shop.orderDetails.orderInfo.placedDate') }}
          </span>
          <span data-id="text-order-placed-date">
            {{ formatDateLong(orderDetailsData.date) }}
          </span>
        </div>
        <div class="mb-1">
          <span class="text-h4">
            {{ $t('shop.orderDetails.orderInfo.placedBy') }}
          </span>
          <span data-id="text-order-placed-by">
            {{ orderDetailsData.placedBy }}
          </span>
        </div>
        <div v-if="hasPaymentInfo">
          <span class="text-h4">{{ $t('shop.orderDetails.orderInfo.payment.method.label') }}</span>
          <span data-id="text-order-payment-method">
              {{ paymentMethodLabel }}
          </span>
        </div>
        <div v-if="paymentFailed" class="failed-order-info d-flex rounded-lg pa-4 mt-9">
          <CDIcon class="mr-2" color="error" icon="$statuserror"></CDIcon>
          <div class="d-flex flex-column">
            <p data-id="text-order-payment-failed" class="mb-1">
              {{$t('shop.orderDetails.orderInfo.paymentFailed', {'ccEnding': cardEnding()})}}
            </p>
            <a :href="updatePaymentUrl"
               class="font-weight-bold"
               :data-id="`failed-payment-invoice-${orderDetailsData.failedPayment.failedPaymentInvoiceId}`">
              {{ $t('shop.orderDetails.orderInfo.update')  }}
            </a>
          </div>
        </div>
      </v-col>
    </v-row>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {OrderDetailsData, PaymentMethodType} from 'common/generated-types/types';
import {camelCase, isNil} from 'lodash';
import {i18nService} from 'common/services';

@Component
export default class OrderInfo extends Vue {
  @Prop() orderDetailsData!: OrderDetailsData;

  localeForFormats = '';

  mounted(): void {
    this.localeForFormats = i18nService.getLocaleForFormats();
  }

  get hasPaymentInfo(): boolean {
    return this.orderDetailsData.paymentMethod && this.orderDetailsData.paymentMethod !== PaymentMethodType.ZERO;
  }

  get paymentFailed(): boolean {
    return !isNil(this.orderDetailsData.failedPayment);
  }

  get updatePaymentUrl(): string {
    return `${this.orderDetailsData.failedPayment.updateUrl}/update-payment?source=order`;
  }

  get paymentMethodLabel(): string {
    if (this.hasPaymentInfo) {
      return this.$t(`shop.orderDetails.orderInfo.payment.method.${camelCase(this.orderDetailsData.paymentMethod)}`) as string;
    } else {
      return '';
    }
  }

  cardEnding(): string {
    return this.orderDetailsData.failedPayment?.cardNumber?.slice(-4);
  }

  formatDateLong(date: string): string {
    return this.$d(new Date(date), 'long', this.localeForFormats);
  }
}
</script>

<style scoped lang="scss">
  .failed-order-info {
    background: var(--v-error-lighten5);
    max-width: 420px;
  }
</style>
