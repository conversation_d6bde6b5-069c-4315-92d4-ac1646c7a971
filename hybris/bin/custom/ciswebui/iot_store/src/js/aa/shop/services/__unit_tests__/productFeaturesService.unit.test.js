import {productFeaturesService} from '../productFeaturesService';
import categoryProduct from 'common/testtools/scenariosstore/aa/categoryProduct.json';
import {cloneDeep} from 'lodash';

describe('Product Features Service', () => {
    describe('hardwareRequirementsForProduct', () => {
        let productData = {};
        beforeEach(() => {
            productData = cloneDeep(categoryProduct);
        });

        it('given product with hardware requirements when hardwareRequirementsForProduct then return requirements labels', () => {
            expect(productFeaturesService.hardwareRequirementsForProduct(productData).map(i => i.value)).toContain('KTS 560/590');
        });

        it('given product with no hardware requirements when hardwareRequirementsForProduct then return empty', () => {
            productData.features = [];

            expect(productFeaturesService.hardwareRequirementsForProduct(productData)).toHaveLength(0);
        });
    });

});
