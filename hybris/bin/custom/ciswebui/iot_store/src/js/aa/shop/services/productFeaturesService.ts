import type {FeatureValueDTO, ProductDTO} from 'common/generated-types/types';

enum ProductFeatureCodes {
    HARDWARE_REQUIREMENTS= 'aaClassificationCatalog/1.0/100.hardware-requirements,100'
}

const productFeaturesService = {
    hardwareRequirementsForProduct: function (product: ProductDTO): FeatureValueDTO[] {
        return product.features
            .filter(feature => feature.code === ProductFeatureCodes.HARDWARE_REQUIREMENTS)
            .flatMap(feature => feature.featureValues);
    }
};

export {productFeaturesService};
