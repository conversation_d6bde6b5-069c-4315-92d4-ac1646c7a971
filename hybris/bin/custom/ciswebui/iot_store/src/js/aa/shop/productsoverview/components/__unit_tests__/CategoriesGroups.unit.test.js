import 'common/test-directive';
import 'common/testtools/unit_tests_mock';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import {cloneDeep} from 'lodash';
import CategoriesGroups from 'aa/shop/productsoverview/components/CategoriesGroups';
import categories from 'common/testtools/scenariosstore/aa/categories.json';

jest.mock('common/util');

const mountCategoriesGroups = (rootCategoryHierarchy) => wrapperComponentFactory(CategoriesGroups, {
    props: {
        rootCategoryHierarchy
    },
    shallow: false
});

let categoriesTestData = {};

beforeEach(() => {
    categoriesTestData = cloneDeep(categories);
});

describe('CategoriesGroups', () => {

    describe('categoriesGroupsToDisplay', () => {
        it('filters out categories that do not contain any products', async () => {
            const wrapper = mountCategoriesGroups(categoriesTestData);

            const categoriesGroupsToDisplay = wrapper.vm.categoriesGroupsToDisplay;
            expect(categoriesGroupsToDisplay).toHaveLength(4);
            expect(categoriesGroupsToDisplay).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({code: 'cat_1'}),
                    expect.objectContaining({code: 'cat_2'}),
                    expect.objectContaining({code: 'cat_3'}),
                    expect.objectContaining({code: 'cat_4'})
                ])
            );
        });
    });

    describe('categoryGroupInFocus', () => {
        it('is initialized to category with lowest order', async () => {
            const wrapper = mountCategoriesGroups(categoriesTestData);
            await wrapper.vm.$nextTick();

            const categoryGroupInFocus = wrapper.vm.categoryGroupInFocus;
            expect(categoryGroupInFocus).toBeTruthy();
            expect(categoryGroupInFocus).toEqual(
                expect.objectContaining({code: 'cat_1', order: 1})
            );
        });
    });

});
