<template>
  <div class="checkout-confirmation-container">
    <account-notification :userCompanyUnderReview="coreData.userCompanyUnderReview"></account-notification>

    <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="mt-4 mt-lg-16">


      <v-row>
        <v-col cols="12" lg="6">
          <div class="checkout-success-header">
            <h1 class="mb-8">{{ $t('shop.checkout.confirmation.confirmationHeader') }}</h1>
            <p class="checkout-success-text">
              <template v-if="coreData.userCompanyUnderReview">
                {{ $t('shop.checkout.confirmation.accountUnderReview.confirmationLine1') }}<br/>
              </template>
              <template v-else>
                {{ $t('shop.checkout.confirmation.confirmationLine1') }}<br/>
                {{ $t('shop.checkout.confirmation.confirmationLine2') }}
              </template>
            </p>
            <p class="checkout-success-text">
              <template v-if="coreData.userCompanyUnderReview">
                <span v-if="orders.length === 1">{{ $t('shop.checkout.confirmation.accountUnderReview.confirmationLine2') }}<br/></span>
              </template>
              <template v-else>
                <span v-if="orders.length === 1 && orders[0].sepaPaymentUsed">{{ $t('shop.checkout.confirmation.confirmationLineInvoice') }}<br/></span>
                <span v-if="orders.length === 1">{{ $t('shop.checkout.confirmation.confirmationLine3') }}:&nbsp;<a target="_blank" v-bind:href="orders[0].url">{{orders[0].code}}</a></span>
              </template>
              <span v-if="orders.length > 1">{{ $t('shop.checkout.confirmation.confirmationLine3plural') }}: <a target="_blank" v-bind:href="item.url" v-for="item in orders">{{item.code}}</a></span>
            </p>
          </div>
        </v-col>

        <v-col cols="12" lg="6">
          <div class="d-flex flex-column flex-md-row justify-start justify-lg-end">
            <CDButton v-if="coreData.numberOfCartItems > 0" :href="cartUrl" data-id="button-back-to-cart">
              {{ $t('shop.checkout.backToCart') }}
            </CDButton>
            <CDButton v-else
                      :href="shopHome"
                      :class="{ 'primary' : coreData.userCompanyUnderReview}"
                      data-id="button-continue-shopping"
            >
              {{ $t('shop.checkout.confirmation.continueShopping') }}
            </CDButton>
            <CDButton v-if="!coreData.userCompanyUnderReview"
                      :href="licenseManagementPortalLink"
                      class="primary ml-md-4 ml-0 mt-md-0 mt-4"
                      data-id="button-licensemanagement"
            >
              {{$t('shop.checkout.confirmation.viewLicenseManagement')}}
            </CDButton>
          </div>
        </v-col>

      </v-row>
      <v-row v-if="summaryBoxEnabled">
        <v-col cols="12">
          <v-card class="my-apps pa-8 pa-lg-16 rounded-lg d-flex flex-column flex-md-row" flat>
            <v-card class="mb-6 mr-4 mr-md-16" flat><aa-checkout-confirmation-image></aa-checkout-confirmation-image></v-card>
            <v-card flat>
              <h2 class="mb-6">{{ $t('shop.checkout.confirmation.checkInboxHeader')}}</h2>
              <p>
                <i18n path="shop.checkout.confirmation.checkInboxText1" tag="span">
                  <template v-slot:buyerEmail><b class="text-h4">{{ pageData.buyerEmail }}</b></template>
                </i18n><br/>
                <i18n path="shop.checkout.confirmation.checkInboxText2" tag="span">
                  <template v-slot:licensingEmail><b class="text-h4">{{ pageData.licensingEmail }}</b></template>
                </i18n>
              </p>
            </v-card>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CheckoutConfirmationData, ShopCoreData, UrlData} from 'common/generated-types/types';
import {ShopRoute, SupportUrlPaths} from 'common/constants';
import AaCheckoutConfirmationImage from 'common/images/boxed/aa-checkout-confirmation.svg';
import {ContentDivider} from 'common/components';
import {navigationService} from 'common/services';
import AccountNotification from 'aa/shop/account/AccountNotification.vue';
import {CheckoutResponsiveGridLayout} from 'shop/checkout/components';

@Component({
  components: {
    CheckoutResponsiveGridLayout,
    AccountNotification,
    ContentDivider,
    AaCheckoutConfirmationImage
  }
})
export default class CheckoutConfirmation extends Vue {

  @Prop() coreData!: ShopCoreData;
  @Prop() pageData!: CheckoutConfirmationData;
  orders: UrlData[] = [];
  camerasUrl = '';
  shopHome: string = ShopRoute.HOME;
  ownAppOrder = false;

  supportLink = this.coreData.supportUrl;
  readonly cartUrl = ShopRoute.CART;
  licenseManagementPortalLink = this.pageData.licensePortalUrl;

  created(): void {
    this.setPageTitle();
  }

  private setPageTitle() {
    document.title = this.$t('shop.checkout.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
  }

  mounted(): void {
    this.orders = this.pageData.urlData;
    this.camerasUrl = this.camerasPurchasedUrl;
    this.ownAppOrder = this.orders.some(v => v.ownAppOrder);
    this.supportLink = navigationService.byItemCode('globalSupport').url;
  }

  get supportOfflineLink(): string {
    return `${this.supportLink}${SupportUrlPaths.INSTALL_APPS_OFFLINE}`;
  }

  get supportOnlineLink(): string {
    return `${this.supportLink}${SupportUrlPaths.INSTALL_APPS_ONLINE}`;
  }

  get summaryBoxEnabled(): boolean {
    return this.coreData.moduleConfig?.ORDER_CONFIRMATION_INFOBOX ?? false;
  }

  get camerasPurchasedUrl(): string {
    const camerasRoute = '/systems';
    const url = new URL(this.pageData.camerasUrl + camerasRoute);
    const query = new URLSearchParams(url.search);
    query.append('purchased-apps', 'true');
    url.search = query.toString();
    return url.toString();
  }
}

</script>

<style scoped lang="scss">
.my-apps {
  border: solid 1px var(--v-grey-lighten1);
}

.checkout-success-text {
  font-family: BoschSansMedium, sans-serif !important;
  font-weight: 500 !important;
  line-height: 150% !important;
  font-size: 20px !important;
}
</style>
