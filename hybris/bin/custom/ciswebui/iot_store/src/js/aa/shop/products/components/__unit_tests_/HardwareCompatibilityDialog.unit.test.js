import 'common/test-directive';
import HardwareCompatibilityDialog from 'aa/shop/products/components/HardwareCompatibilityDialog';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';

jest.mock('common/util');

const mountHardwareCompatibilityDialog = () => wrapperComponentFactory(HardwareCompatibilityDialog, {
    props: {
        selectedFacet: null,
        facet: {
            "group": "hardwareRequirements",
            "orderIndex": 7,
            "items": [
                {
                    "name": "KTS 560/590",
                    "facetIndex": "hardwareRequirements",
                    "facetValue": "KTS 560/590",
                    "count": 44
                },
                {
                    "name": "DCI 700",
                    "facetIndex": "hardwareRequirements",
                    "facetValue": "DCI 700",
                    "count": 4
                },
                {
                    "name": "FSA 500",
                    "facetIndex": "hardwareRequirements",
                    "facetValue": "FSA 500",
                    "count": 1
                },
                {
                    "name": "FSA 7XX",
                    "facetIndex": "hardwareRequirements",
                    "facetValue": "FSA 7XX",
                    "count": 1
                }
            ]
        },
        value: true
    },
    shallow: false
});

describe('HardwareCompatibilityDialog', () => {

    it('renders dialog with radio buttons', async () => {
        const wrapper = mountHardwareCompatibilityDialog();
        await wrapper.vm.$nextTick();
        expect(wrapper.findAll('v-radio').length).toEqual(4);
        expect(wrapper.find('[data-id="container-hardware-compatibility-dialog"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="text-hardware-compatibility-dialog-header"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-close"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="button-hardware-compatibility-mobile-dialog-close"]').exists()).toBeFalsy();
        expect(wrapper.find('[data-id="container-hardware-compatibility-dialog-body"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="container-hardware-compatibility-dialog-footer"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-cancel"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-cancel"]').attributes('disabled')).toBeFalsy();
        expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-submit"]').exists()).toBeTruthy();
    });

    describe('Search',  () => {
        it('returns results if query is given', async () => {
            const wrapper = mountHardwareCompatibilityDialog();
            await wrapper.vm.$nextTick();
            wrapper.vm.query = 'KT';
            await wrapper.vm.$nextTick();
            expect(wrapper.findAll('v-radio').length).toEqual(1);
            const result = wrapper.find('v-radio');
            expect(result.attributes('label')).toEqual('KTS 560/590');
            wrapper.vm.query = 'FSA';
            await wrapper.vm.$nextTick();
            expect(wrapper.findAll('v-radio').length).toEqual(2);
        })

        it('returns all elements if query is cleared', async () => {
            const wrapper = mountHardwareCompatibilityDialog();
            await wrapper.vm.$nextTick();
            expect(wrapper.findAll('v-radio').length).toEqual(4);
            wrapper.vm.query = 'FSA';
            await wrapper.vm.$nextTick();
            expect(wrapper.findAll('v-radio').length).toEqual(2);
            wrapper.vm.query = '';
            await wrapper.vm.$nextTick();
            expect(wrapper.findAll('v-radio').length).toEqual(4);
        })
    })

    describe('SaveButton',  () => {
        it('is disabled if no v-radio is checked', async () => {
            const wrapper = mountHardwareCompatibilityDialog();
            await wrapper.vm.$nextTick();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-submit"]').exists()).toBeTruthy();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-submit"]').attributes('disabled')).toBeTruthy();
        })
        it('is active if v-radio is checked', async () => {
            const wrapper = mountHardwareCompatibilityDialog();
            await wrapper.vm.$nextTick();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-submit"]').exists()).toBeTruthy();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-submit"]').attributes('disabled')).toBeTruthy();
            wrapper.vm.selectedFacetValue = wrapper.vm.facet.items[0].facetValue;
            await wrapper.vm.$nextTick();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-submit"]').attributes('disabled')).toBeFalsy();

        })
    })

    describe('CancelButton',  () => {
        it('is active if no v-radio is checked', async () => {
            const wrapper = mountHardwareCompatibilityDialog();
            await wrapper.vm.$nextTick();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-cancel"]').exists()).toBeTruthy();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-cancel"]').attributes('disabled')).toBeFalsy();
        })

        it('is active if v-radio is checked', async () => {
            const wrapper = mountHardwareCompatibilityDialog();
            await wrapper.vm.$nextTick();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-cancel"]').exists()).toBeTruthy();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-cancel"]').attributes('disabled')).toBeFalsy();
            wrapper.vm.selectedFacetValue = wrapper.vm.facet.items[0].facetValue;
            await wrapper.vm.$nextTick();
            expect(wrapper.find('[data-id="button-hardware-compatibility-dialog-cancel"]').attributes('disabled')).toBeFalsy();

        })
    })
});
