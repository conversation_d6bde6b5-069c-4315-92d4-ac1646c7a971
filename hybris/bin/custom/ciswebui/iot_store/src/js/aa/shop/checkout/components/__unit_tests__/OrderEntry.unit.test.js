import OrderEntry from 'aa/shop/checkout/components/OrderEntry.vue';
import {cloneDeep} from 'lodash';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';

jest.mock('common/util');

const defaultCoreData = {
  moduleConfig : {}
};

const mountOrderEntry = (cartItemData, isOwnAppPurchase = false) => wrapperComponentFactory(OrderEntry, {
    props: {
        data: cartItemData,
        isOwnAppPurchase
    },
    shallow: false,
    mocks: {}
});

const defaultSubscriptionCartItemData = {
  appCode: 'AA2_0401687_CSFSA500',
  sellerProductId: "1987P12820",
  productName: 'Paket Diagnostik',
  productCode: 'AA2_0401687_CSFSA500',
  productUrl: '/p/AA2_0401687_CSFSA500',
  companyName: 'Robert <PERSON> AG',
  versionName: '1.0.0',
  licenseName: 'Subscription',
  licenseType: 'SUBSCRIPTION',
  runtime: {
    code: 'runtime_subs_unlimited'
  },
  logoUrl: '/sample-data/icon-subscription-app-license.png',
  itemPrice: {
    symbol: 'EUR',
    value: '220.00'
  },
  totalPrice: {
    symbol: 'EUR',
    value: '440.00'
  },
  productFuturePrices: [
    {
      symbol: 'EUR',
      value: '1400.00'
    }
  ],
  specialOffer: false
};

describe('AA Order Entry', () => {

    beforeAll(() => {
        window.frontendData = {
            coreData: defaultCoreData
        };
    });

    let cartItem;
    beforeEach(() => {
        cartItem = cloneDeep(defaultSubscriptionCartItemData);
    });

    describe('isSubscription', () => {
        it('given subscription then true', async () => {
            const wrapper = mountOrderEntry(cartItem);
            await wrapper.vm.$nextTick();
            const isSubscription = wrapper.vm.isSubscription;

            expect(isSubscription).toBeTruthy();
        });
    });

    describe('Special offer chip', () => {
        it('is displayed then special offer flag is true', async () => {
            cartItem.specialOffer = true;

            const wrapper = mountOrderEntry(cartItem);

            const specialPriceChip = wrapper.find(`[data-id^="container-special-price-chip-order-item-"]`);
            expect(specialPriceChip.exists()).toBeTruthy();
        });

        it('special offer chip is displayed then special offer flag is false', async () => {
            cartItem.specialOffer = false;

            const wrapper = mountOrderEntry(cartItem);

            const specialPriceChip = wrapper.find(`[data-id^="container-special-price-chip-order-item-"]`);
            expect(specialPriceChip.exists()).toBeFalsy();
        });

    });

});
