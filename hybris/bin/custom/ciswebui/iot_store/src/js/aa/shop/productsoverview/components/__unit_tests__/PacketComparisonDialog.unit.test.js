import {resizeWidth, wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import props from 'common/testtools/scenariosstore/aa/packetComparisonDialogData.json';
import PacketComparisonDialog from '../PacketComparisonDialog.vue';

const mountPacketComparisonDialog = () => wrapperComponentFactory(PacketComparisonDialog, {
    props: {
        billsOfMaterials: props.billsOfMaterials,
        products: props.products,
    }
});

describe('PacketComparisonDialog exists', () => {

    it('exists', async () => {
        const wrapper = mountPacketComparisonDialog();
        expect(wrapper.exists()).toBeTruthy();
    });

    it('sets activeMaterial data after anchor tag is clicked, showing a sub-dialog containing activeMaterial infos', async () => {
        resizeWidth(1440);
        const wrapper = await mountPacketComparisonDialog();
        await wrapper.find('[data-id="button-material-name-MAT_040A"]').trigger('click');
        const generalInfos = {
            'code': 'MAT_040A',
            'name': 'A (Allgemeine Fahrzeuginformationen)',
            'description': 'Umfasst die Anwendungen, Funktionen, sowie die KfZ-Ausrüstung.',
            'videoUrl': null
        };
        expect(wrapper.vm.activeMaterial).toEqual(generalInfos);
    });

    it('renders a purchase button per package', async () => {
        const wrapper = await mountPacketComparisonDialog();
        const productCount = wrapper.vm.$props.products.length;
        const ctaCount = wrapper.findAll('[data-id^="button-purchase-"]').wrappers.length;
        expect(productCount).toEqual(ctaCount);
    });

    it('renders "x" icons on mobile to indicate non-inclusion, but not on desktop', async () => {
        resizeWidth(1440);
        const desktopWrapper = await mountPacketComparisonDialog();
        const desktopIcons = desktopWrapper.findAll('cdicon-stub').wrappers.map(x => x.attributes().icon);
        const uniqueDesktopIcons = new Set(desktopIcons);
        expect(uniqueDesktopIcons.has('$complete')).toBe(true);
        expect(uniqueDesktopIcons.size).toBe(1);
        desktopWrapper.destroy();

        resizeWidth(400);
        const mobileWrapper = await mountPacketComparisonDialog();
        const mobileIcons = mobileWrapper.findAll('cdicon-stub').wrappers.map(x => x.attributes().icon);
        const uniqueMobileIcons = new Set(mobileIcons);
        expect(uniqueMobileIcons.size).toBe(2);
        expect(uniqueMobileIcons.has('$complete')).toBe(true);
        expect(uniqueMobileIcons.has('$close')).toBe(true);
    });

    describe('purchase button', () => {
        it('links to login with redirect for anonymous users', async () => {
            window.frontendData.coreData.userName = null;

            const wrapper = await mountPacketComparisonDialog();
            const buttonUrls = wrapper.findAll('[data-id^="button-purchase-"]')
                .wrappers
                .map(wrapper => wrapper.attributes().href);

            expect(buttonUrls).toHaveLength(props.products.length);
            expect(buttonUrls).not.toEqual(
                expect.arrayContaining([
                    expect.not.stringMatching(/login\?redirect/)
                ])
            );
        });

        it('does not link to login for logged in users', async () => {
            window.frontendData.coreData.userName = 'some_user';

            const wrapper = await mountPacketComparisonDialog();
            const buttonUrls = wrapper.findAll('[data-id^="button-purchase-"]')
                .wrappers
                .map(wrapper => wrapper.attributes().href);

            expect(buttonUrls).toHaveLength(props.products.length);
            expect(buttonUrls).not.toEqual(
                expect.arrayContaining([
                    expect.stringMatching(/login\?redirect/)
                ])
            );
        });
    });
});
