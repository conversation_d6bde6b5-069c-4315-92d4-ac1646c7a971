<template>
  <v-col>
    <v-row no-gutters
         dense
         :data-id="`product-selection-summary-item-${variant.code}`">
      <v-col cols="auto">
          <v-img :src="product.logoUrl"
                 :max-width="32"
                 class="logo rounded-circle mr-4">
          </v-img>
      </v-col>

      <v-col>
        <v-row no-gutters
               dense>
          <v-col cols="12"
                 md
                 class="d-inline-flex flex-wrap">
            <h5 class="mr-2">{{ product.name }}</h5>
            <CDChip x-small
                    disabled>
              {{ $t(`shop.runtime.${variant.runtime.code}`) }}
            </CDChip>
          </v-col>
          <v-col md="12"
                 class="text-body-2"
                 order-md="last">
            {{
              isBundle
                  ? $t(
                      'shop.productSelection.summary.bundle',
                      {
                        quantity: quantity,
                        bundleLabel: bundleLabel
                      }
                  )
                  : $tc('shop.productSelection.summary.license', quantity)
            }}
          </v-col>
          <v-col cols="auto"
                 :class="productSelectionStore.consent ? ['original-price', 'mr-4'] : ''"
                 class="text-body-2"
                 :data-id="`text-item-price-${variant.code}`">
            {{
              $t(
                'shop.productSelection.summary.price',
                {
                  currency: variant.currencyIsocode,
                  price: $n(totalPrice, 'price', localeForFormats)
                }
              )
            }}
            {{ isSubscription ? $t('shop.productSelection.summary.perYear') : '' }}
          </v-col>
          <v-col v-if="productSelectionStore.consent"
                 cols="auto"
                 class="text-body-2"
                 :data-id="`text-item-price-${variant.code}`">
            {{
              $t(
                  'shop.productSelection.summary.price',
                  {
                    currency: variant.currencyIsocode,
                    price: $n(totalPriceDiscount, 'price', localeForFormats)
                  }
              )
            }}
            {{ isSubscription ? $t('shop.productSelection.summary.perYear') : '' }}
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row v-if="productSelectionStore.consent"
           no-gutters
           dense
           :data-id="`product-selection-summary-item-${variant.code}`"
           class="mt-3">
      <v-col cols="auto">
        <v-img :src="product.logoUrl"
               :max-width="32"
               class="logo rounded-circle mr-4">
        </v-img>
      </v-col>
      <v-col>
        <v-row no-gutters
               dense>
          <v-col cols="12"
                 md
                 class="d-inline-flex flex-wrap">
            <h5 class="mr-2">{{ product.addonProductName }}</h5>
            <CDChip x-small
                    disabled>
              {{ $t(`shop.runtime.${variant.runtime.code}`) }}
            </CDChip>
          </v-col>
          <v-col md="12"
                 class="text-body-2"
                 order-md="last">
            {{
                $tc('shop.productSelection.summary.license', quantity)
            }}
          </v-col>
          <v-col cols="auto"
                 class="text-body-2 original-price mr-4"
                 :data-id="`text-item-price-${variant.code}`">
            {{
              $t(
                  'shop.productSelection.summary.price',
                  {
                    currency: variant.currencyIsocode,
                    price: $n(productSelectionStore.getOriginalThlPrice(DiscountProductType.THL), 'price', localeForFormats)
                  }
              )
            }}
            {{ isSubscription ? $t('shop.productSelection.summary.perYear') : '' }}
          </v-col>
          <v-col cols="auto"
                 class="text-body-2"
                 :data-id="`text-item-price-${variant.code}`">
            {{
              $t(
                  'shop.productSelection.summary.price',
                  {
                    currency: variant.currencyIsocode,
                    price: $n(productSelectionStore.getDiscountedPrice(DiscountProductType.THL), 'price', localeForFormats)
                  }
              )
            }}
            {{ isSubscription ? $t('shop.productSelection.summary.perYear') : '' }}
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-col>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {BundleInfoData, ProductLicenseData, SimpleProductData} from 'common/generated-types/types';
import {DiscountProductType, LicenseType} from 'common/types';
import {bundleInfoService, i18nService} from 'common/services';
import {useProductSelectionStore} from 'shop/store/productSelection';

@Component
export default class ProductSelectionSummaryItem extends Vue {
  @Prop() product!: SimpleProductData;
  @Prop() variant!: ProductLicenseData;
  @Prop() quantity!: number;

  productSelectionStore = useProductSelectionStore();
  DiscountProductType = DiscountProductType;

  localeForFormats = '';

  get i18nService() {
    return i18nService;
  }

  mounted(): void {
    this.localeForFormats = i18nService.getLocaleForFormats();
  }

  get isSubscription(): boolean {
    return this.variant.licenseType.code === LicenseType.SUBSCRIPTION;
  }

  get bundleInfo(): BundleInfoData {
    return this.variant.bundleInfo;
  }

  get isBundle(): boolean {
    return !!this.bundleInfo?.code;
  }

  get bundleLabel(): string {
    return bundleInfoService.getBundleLabel(this.bundleInfo, this.$i18n);
  }

  get totalPrice(): number {
    return this.quantity * this.variant.price;
  }

  get totalPriceDiscount(): number {
    return this.quantity * this.productSelectionStore.getDiscountedPriceByCode(this.variant.code);
  }
}
</script>

<style lang="scss" scoped>
.logo {
  border: 1px solid var(--v-grey-lighten3);
}
.original-price {
  color: var(--v-grey-darken2);
  text-decoration: line-through;
}
</style>
