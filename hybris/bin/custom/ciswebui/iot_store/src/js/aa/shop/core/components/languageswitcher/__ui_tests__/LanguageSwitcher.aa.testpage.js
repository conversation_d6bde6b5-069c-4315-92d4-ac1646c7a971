import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import {LanguageSwitcherActivator, LanguageSwitcherDialog} from 'aa/shop/core/components/languageswitcher';
import {useCoreDataStore} from 'shop/store/coreData';
import coreData from 'common/testtools/scenariosstore/aa/coreDataFromRest.json';

Vue.use(pinia);
Vue.use(VueI18n);
const coreDataStore = useCoreDataStore();
coreDataStore.coreData = coreData;

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    pinia,
    components: {
        TestPageRoot,
        LanguageSwitcherActivator,
        LanguageSwitcherDialog
    },
    data: {
        languageSwitcherDialog: false,
        globalFallbackCountry: 'AT',
        globalDefaultLanguage: 'en'
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <language-switcher-dialog
              v-model="languageSwitcherDialog"
              :global-fallback-country="globalFallbackCountry"
              :global-default-language="globalDefaultLanguage">
          </language-switcher-dialog>
          <language-switcher-activator @click="languageSwitcherDialog = true"></language-switcher-activator>
        </div>
      </test-page-root>
    `
});
