<template>
  <div class="checkout-page">
    <account-notification :userCompanyUnderReview="coreData.userCompanyUnderReview"></account-notification>
    <checkout-responsive-grid-layout
        v-bind:header-title="checkoutPaymentMethodHeader"
        v-bind:header-icon="'$lockclosed'">

      <template v-slot:main-content-1>
        <h2 class="mb-8">{{ $t('shop.checkout.paymentMethod') }}</h2>
        <v-radio-group name="paymentMethod" v-model="currentPaymentMethodId">
          <v-row>
            <v-col v-if="creditCardCheckout && (creditCardPaymentInfos || hasDpgCreditCardForm)">
              <v-row>
                <h3 class="ml-3 mt-0 mb-5" >{{ $t('shop.payment.creditCard') }}</h3>
              </v-row>
              <v-row  class="card-entry saved" v-bind:key="item.id" v-for="item in creditCardPaymentInfos">
                <v-col cols="2" sm="1" class="text-center text-md-right">
                  <CDRadio :value="item.id"
                           v-on:change="setPaymentMethod(item)"
                           class="justify-start pt-6"
                           data-id="saved-card-option">
                  </CDRadio>
                </v-col>
                <v-col cols="10" sm="11">
                  <payment-method-entry class="mt-3"
                                        v-bind:payment-data="item"
                                        v-on:delete="confirmCardDelete(item)"
                                        v-bind:actions="true">
                    <template v-slot:creditcard></template>
                  </payment-method-entry>
                  <p class="payment-method-hint mt-0 mr-5 mb-3 ml-16 body-2" v-if="!item.saved">
                    {{ $t('shop.checkout.paymentMethodNotSavedHint') }}
                  </p>
                </v-col>
              </v-row>
              <v-row class="card-entry new " v-bind:class="{'hide-payment-form': !isNewPaymentMethod}" v-if="hasDpgCreditCardForm">
                <v-col cols="2" sm="1" class="text-center text-md-right">
                  <CDRadio :value="'NEW'"
                           v-on:change="enableNewCreditCard()"
                           class="justify-start pt-6"
                           data-id="new-card-option">
                  </CDRadio>
                </v-col>
                <v-col cols="10" sm="11">
                  <payment-method-entry class="mt-3" 
                                        v-bind:newEntry="true"
                                        v-bind:newEntryType="PaymentMethodType.CREDIT_CARD">
                  </payment-method-entry>
                  <p class="overwrite-hint my-0 mr-6 ml-16 body-2" v-if="paymentNotSaved && isNewPaymentMethod">
                    {{ $t('shop.checkout.paymentMethodOverwriteHint') }}
                  </p>
                </v-col>
                <v-col class="payment-form pt-6 px-0 pr-md-6" ref="paymentForm">
                  <v-row no-gutters>
                    <v-col cols="12" md="5" lg="4">
                      <div id="card-number-container" class="payment-provider-element-container dpg-element-container text-left">
                        <div id="card-number" class="payment-provider-element w-20 rounded-lg pa-2 px-3">
                        </div>
                        <small class="payment-provider-field-error">{{ cardNumberError }}</small>
                      </div>
                    </v-col>
                    <v-col cols="12" md="7" lg="6">
                      <v-card class="ml-0 ml-md-6" elevation="0">
                        <div id="card-holder-container" class="payment-provider-element-container dpg-element-container text-left">
                          <div id="card-holder" class="payment-provider-element w-20 rounded-lg pa-2 px-3">
                          </div>
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>
                  <v-row no-gutters>
                    <v-col cols="7" md="4" lg="3">
                      <div id="card-expiry-container" class="payment-provider-element-container dpg-element-container text-left">
                        <div id="card-expiry" class="payment-provider-element w-20 rounded-lg pa-2 px-3">
                        </div>
                        <small class="payment-provider-field-error">{{ cardExpiryError }}</small>
                      </div>
                    </v-col>
                    <v-col cols="6" md="4" lg="3">
                      <v-card class="ml-0 ml-md-6" elevation="0">
                        <div id="card-cvc-container" class="payment-provider-element-container dpg-element-container text-left">
                          <div id="card-cvc" class="payment-provider-element w-20 rounded-lg pa-2 px-3">
                          </div>
                          <small class="payment-provider-field-error">{{ cardCvcError }}</small>
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>
                  <div class="card-save">
                    <CDCheckbox v-if="creditCardCheckout.savableForReuse"
                        v-on:change="cardSaved = $event"
                        data-id="save-card-option"
                        color="primary">
                      <template v-slot:label>
                        <div class="inner-text d-inline-block">
                          {{ $t('shop.checkout.saveCard') }}
                        </div>
                      </template>
                    </CDCheckbox>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="bank-transfer-payment-info" v-if="sepaBanktransferCheckout || achBanktransferCheckout" v-bind:class="{'mt-10': creditCardCheckout}">
              <v-row>
                <h3 class="ml-3 mb-5">{{ $t('shop.payment.bankTransfer') }}</h3>
              </v-row>
              <v-row class="card-entry saved"
                   v-bind:class="{ fullFields: invoiceInfo.defaultPaymentInfo }"
                   v-bind:key="invoiceInfo.id" v-for="invoiceInfo in sepaCreditTransferPaymentInfos">
                <v-col cols="2" sm="1" class="text-center text-md-right">
                  <CDRadio id="sepaCreditInput"
                           :disabled="!invoiceInfo.enabled"
                           :value="invoiceInfo.id"
                           class="justify-start pt-6"
                           v-on:change="setPaymentMethod(invoiceInfo)">
                  </CDRadio>
                </v-col>
                <v-col cols="10" sm="11">
                  <payment-method-entry class="mt-3"
                                        v-bind:payment-data="invoiceInfo"
                                        v-bind:current-payment-method-id="currentPaymentMethodId">
                  </payment-method-entry>
                </v-col>
              </v-row>
              <v-row class="card-entry saved"
                   v-bind:class="{ fullFields: invoiceInfo.defaultPaymentInfo }"
                   v-bind:key="invoiceInfo.id" v-for="invoiceInfo in achCreditTransferPaymentInfos">
                <v-col cols="2" sm="1" class="text-center text-md-right">
                  <CDRadio id="sepaCreditInput"
                           :disabled="!invoiceInfo.enabled"
                           :value="invoiceInfo.id"
                           class="justify-start pt-6"
                           v-on:change="setPaymentMethod(invoiceInfo)">
                  </CDRadio>
                </v-col>
                <v-col cols="10" sm="11">
                  <payment-method-entry class="mt-3"
                                        v-bind:payment-data="invoiceInfo"
                                        v-bind:current-payment-method-id="currentPaymentMethodId">
                  </payment-method-entry>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="direct-debit-payment-info" v-if="sepaDirectDebitCheckout" v-bind:class="{'mt-10': creditCardCheckout}">
              <v-row>
                <h3 class="ml-3 mb-5">{{ $t('shop.payment.directDebit') }}</h3>
              </v-row>
              <v-row class="card-entry new " v-if="sepaDirectDebitCheckout.userCreatable">
                <v-col cols="2" sm="1" class="text-center text-md-right">
                  <CDRadio :value="NEW_SEPA_MANDATE"
                           data-id="new-mandate-option"
                           class="justify-start pt-6"
                           v-on:change="enableNewSepaMandate()">
                  </CDRadio>
                </v-col>
                <v-col cols="10" sm="11">
                  <payment-method-entry class="mt-3"
                                        v-bind:new-entry="true"
                                        v-bind:new-entry-type="PaymentMethodType.SEPA_DIRECTDEBIT">
                  </payment-method-entry>
                </v-col>
              </v-row>
              <v-row class="card-entry saved"
                   v-bind:class="{ fullFields: directDebitInfo.defaultPaymentInfo }"
                   v-bind:key="directDebitInfo.id" v-for="directDebitInfo in sepaDirectDebitPaymentInfos">
                <v-col cols="12" sm="1" class="text-center text-md-right">
                  <CDRadio data-id="saved-mandate-option"
                           :value="directDebitInfo.id"
                           class="justify-start pt-6"
                           v-on:change="setPaymentMethod(directDebitInfo)">
                  </CDRadio>
                </v-col>
                <v-col cols="12" sm="11">
                  <payment-method-entry class="mt-3"
                                        v-bind:payment-data="directDebitInfo"
                                        v-bind:current-payment-method-id="currentPaymentMethodId">
                  </payment-method-entry>
                  <div class="payment-method-hint mt-3 mr-5 ml-3 mb-3 body-2" v-if="!directDebitInfo.saved">
                    {{ $t('shop.checkout.paymentMethodNotSavedHint') }}
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-radio-group>
      </template>
      <template v-slot:main-content-2>
        <mini-summary v-bind:payment-address="pageData.paymentAddress"
                      v-bind:price="pageData.totalPrice"
                      v-bind:payment-method="currentPaymentMethodType"
                      v-bind:is-own-app-purchase="pageData.ownAppsPurchase"
                      v-bind:display-tax-note="!pageData.ownAppsPurchase"
                      v-bind:country-eulas="countryEulas"
                      v-on:onEulaAcceptedClicked="onEulaAcceptedClicked">
          <CDButton class="primary save-button"
                    color="primary"
                    large
                    data-id="place-order-btn"
                    v-on:click="submit">
            {{ currentPaymentMethodType === PaymentMethodType.SEPA_DIRECTDEBIT ? $t('shop.checkout.placeOrderAndPay') : $t('shop.checkout.placeOrder') }}
          </CDButton>
        </mini-summary>
        <p class="agb-confirm text-body-2 text-center grey--text text--darken-2 mt-3">
          {{ $t('shop.checkout.placeOrderConfirmation') }}
        </p>
      </template>

      <template
          v-slot:main-content-3>
        <hr role="separator" aria-orientation="horizontal" class="v-divider theme--light"/>
        <h3 class="mt-12 mb-8">{{ $t('shop.checkout.orderSummary') }}</h3>
        <order-entry
            v-bind:is-own-app-purchase="pageData.ownAppsPurchase"
            v-for="entry in pageData.entries"
            v-bind:data="entry"
            v-bind:country="coreData.currentCountry?.isocode"
            v-bind:user-group="coreData.currentCompany?.userGroup"
            :key="entry.productCode">
        </order-entry>
        <special-offer-conditions v-if="showSpecialOfferConditions" class="mt-16">
        </special-offer-conditions>
      </template>
      <template
          v-slot:main-content-4>
        <distributor-panel :list-distributors="pageData.listOfDistributors"
                           :current-distributor="coreData.currentCompany.distributor"
                           @distributor-changed="onDistributorChanged">
        </distributor-panel>
      </template>
      <template
          v-if="pageData.showInvoiceNotes"
          v-slot:main-content-5>
        <div class="invoice-notes summary">
          <h3 class="mt-12 mb-2">
            {{ $t('shop.checkout.invoiceNote.title') }}
          </h3>
          <p class="mb-6">
            {{ $t('shop.checkout.invoiceNote.description') }}
          </p>

          <CDInput class="mb-4"
                   :label="$t('shop.checkout.invoiceNote.noteLine', { line: 1 })"
                   counter
                   flat
                   :value="invoiceNote.firstNote"
                   :rules="[invoiceNoteValid(invoiceNote.firstNote)]"
                   :maxlength="invoiceNoteSizeLimit"
                   v-on:keyup="invoiceNote.firstNote = $event.target.value"
                   data-id="note_0">
          </CDInput>

          <CDInput class="mb-4"
                   :label="$t('shop.checkout.invoiceNote.noteLine', { line: 2 })"
                   counter
                   flat
                   :value="invoiceNote.secondNote"
                   :rules="[invoiceNoteValid(invoiceNote.secondNote)]"
                   :maxlength="invoiceNoteSizeLimit"
                   v-on:keyup="invoiceNote.secondNote = $event.target.value"
                   data-id="note_1">
          </CDInput>
        </div>
      </template>
      <template v-slot:button-container>
        <CDButton
            large
            :href="backUrl"
            class="mb-4"
            data-id="button-back-to-cart">
          {{ $t('shop.checkout.backToCart') }}
        </CDButton>
      </template>
    </checkout-responsive-grid-layout>
    <confirmation-dialog v-bind:confirmation="confirmationDialog"
                         v-if="showConfirmCard"
                         v-on:cancel="showConfirmCard = false"
                         v-on:confirm="onConfirm">
    </confirmation-dialog>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {
  AchCreditTransferPaymentInfoData,
  CheckoutInfoData,
  CountryEulaData,
  CreditCardPaymentInfoData,
  ErrorMessageData,
  InvoicePaymentInfoData,
  OrderPaymentData,
  PaymentInfoData,
  PaymentMethodType,
  PaymentProvider,
  PlaceOrderData,
  SepaCreditTransferPaymentInfoData,
  SepaMandatePaymentInfoData,
  ShopCoreData,
  StoreErrorResponseData,
  UmpDistributorData
} from 'common/generated-types/types';
import {CheckoutResponsiveGridLayout, MiniSummary, PaymentMethodEntry} from 'shop/checkout/components';
import {ShopRoute} from 'common/constants';
import {pageSpinner} from 'common/components/spinner';
import {messageService} from 'common/services';
import {find, isEmpty, reject, uniqWith} from 'lodash';
import anime from 'animejs';
import def from 'common/defaults';
import {paymentResource} from 'shop/resources';
import ConfirmationDialog from 'common/components/popups/ConfirmationDialog.vue';
import {PaymentMethodIcons, StatusTag} from 'common/components';
import {ConfirmationDialogType} from 'common/types';
import Tooltip from 'common/components/Tooltip.vue';
import util from 'common/util';
import {
  CreditCardOptionsName,
  FormEvent,
  IntegrationCreateTokenError,
  IntegrationCreateTokenResponse,
  prepareOptions,
  validationChangePayload
} from 'common/dpg';
import {DistributorPanel, OrderEntry} from 'aa/shop/checkout/components';
import {shopCoreDataProvider} from 'common/provider';
import {useCoreDataStore} from 'shop/store/coreData';
import {SpecialOfferConditions} from 'aa/shop/core/components';
import {useStoreHeaderStore} from 'aa/shop/store/storeHeader';
import AccountNotification from 'aa/shop/account/AccountNotification.vue';

interface InvoiceNote {
  firstNote: string;
  secondNote: string;
}

@Component({
  computed: {
    PaymentMethodType() {
      return PaymentMethodType;
    }
  },
  components: {
    AccountNotification,
    OrderEntry,
    DistributorPanel,
    PaymentMethodEntry,
    CheckoutResponsiveGridLayout,
    MiniSummary,
    ConfirmationDialog,
    PaymentMethodIcons,
    StatusTag,
    Tooltip,
    SpecialOfferConditions
  }
})
export default class CheckoutPaymentPage extends Vue {
  @Prop() coreData!: ShopCoreData;
  @Prop() pageData!: OrderPaymentData;

  readonly NEW_PAYMENT_METHOD = 'NEW';
  readonly NO_PAYMENT_METHOD = '';
  readonly NEW_SEPA_MANDATE = 'NEW_MANDATE';
  readonly backUrl = ShopRoute.CHECKOUT_PAYMENT_BACK;
  readonly placeOrderUrl = ShopRoute.CHECKOUT_PAYMENT_PLACE_ORDER;
  readonly errorUrl = ShopRoute.CHECKOUT_PAYMENT_ERROR;
  readonly cartUrl = ShopRoute.CART;
  readonly paymentUrl = ShopRoute.CHECKOUT_PAYMENT_ADD;
  readonly confirmUrl = ShopRoute.CHECKOUT_PAYMENT_CONFIRM;

  invoiceNote = {} as InvoiceNote;
  errorMessage = {} as ErrorMessageData;

  PaymentProvider = PaymentProvider;

  checkoutInfos : Map<PaymentMethodType, CheckoutInfoData> = this.checkoutInfosAsMap;

  creditCardPaymentInfos : CreditCardPaymentInfoData[] = this.creditCardCheckoutPaymentInfos;
  sepaCreditTransferPaymentInfos : SepaCreditTransferPaymentInfoData[] = this.sepaBanktransferCheckoutPaymentInfos;
  achCreditTransferPaymentInfos : AchCreditTransferPaymentInfoData[] = this.achBanktransferCheckoutPaymentInfos;
  sepaDirectDebitPaymentInfos : SepaMandatePaymentInfoData[] = this.sepaDirectDebitCheckoutPaymentInfos;
  invoicePaymentInfos : InvoicePaymentInfoData[] = this.mergedBankTransferInfos;
  userDefaultInfo : PaymentInfoData | undefined = this.determineUserDefault;

  buttonDisabled = true;
  currentPaymentMethodId = this.NO_PAYMENT_METHOD;
  currentPaymentMethodType = PaymentMethodType.ZERO;

  renderDummy = 0;

  eulaCheckboxClicked = false;
  distributorId = this.coreData?.currentCompany?.distributor?.id;
  showConfirmCard = false;
  confirmCard: PaymentInfoData | undefined = undefined;
  confirmationPending : boolean = this.pageData.confirmationPending;

  cardSaved = false;

  cardHolderIsCompleted = false;

  cardNumberError = '';
  cardNumberIsCompleted = false;

  cardExpiryError = '';
  cardExpiryIsCompleted = false;

  cardCvcError = '';
  cardCvcIsCompleted = false;

  /* eslint-disable @typescript-eslint/no-explicit-any */
  dpgInstance!: any;
  dpgElements!: any;

  $refs!: {
    paymentForm: HTMLElement
  };

  coreDataStore = useCoreDataStore();
  storeHeaderStore = useStoreHeaderStore();

  created(): void {
    this.setPageTitle();
  };

  get countryEulas(): CountryEulaData[] {
    const eulas = this.pageData.entries?.flatMap(e => e.countryEulas?.flatMap(eula => eula));
    return uniqWith(eulas, (a, b) => a?.url === b?.url && a?.label === b?.label);
  }

  get invoiceNoteSizeLimit(): number {
    return this.pageData.invoiceNoteSizeLimit;
  }

  invoiceNoteValid(note: string): boolean | string {
    return !note || note.length <= this.invoiceNoteSizeLimit;
  }

  onEulaAcceptedClicked(value: boolean) {
    this.eulaCheckboxClicked = value;
  }

  onDistributorChanged(value: UmpDistributorData) {
    this.coreData.currentCompany.distributor = value;
    this.distributorId = value?.id;
  }

  private eulasPresent(): boolean {
    return this.countryEulas && this.countryEulas.length > 0;
  }

  private eulaAcceptanceActivated(): boolean {
    return Boolean(shopCoreDataProvider.data.moduleConfig.ENFORCE_EULA_ACCEPTANCE);
  }

  async mounted(): Promise<void> {
    this.initializeCurrentPaymentSelection();
    if (this.confirmationPending) {
      await this.confirmPendingOrder();
    }

    if (!this.isNewPaymentMethod) {
      this.buttonDisabled = false;
    } else {
      this.showPaymentForm();
    }

    if (this.hasDpgCreditCardForm) {
      const dpgjsUrl = this.creditCardCheckout?.userActionParameters.dpgjsUrl;
      const sessionId = this.creditCardCheckout?.userActionParameters.sessionId;
      const sessionConfig = this.creditCardCheckout?.userActionParameters.sessionConfig;

      if (dpgjsUrl && sessionId && sessionConfig) {
        await util.scriptLoader(dpgjsUrl, PaymentProvider.DPG);
        this.dpgInstance = (window as any).integration;
        await this.initDpgComponents(sessionId, sessionConfig);
      } else {
        messageService.error(this.$t('shop.checkout.error') as string);
      }
    }
  }

  get checkoutInfosAsMap() : Map<PaymentMethodType, CheckoutInfoData> {
    return new Map(this.pageData.checkoutInfos.map(checkoutInfoData => [checkoutInfoData.paymentMethod, checkoutInfoData!]));
  }

  get creditCardCheckout() : CheckoutInfoData | undefined {
    return this.checkoutInfos.get(PaymentMethodType.CREDIT_CARD);
  }

  get creditCardCheckoutPaymentInfos() : CreditCardPaymentInfoData[] {
    return (this.creditCardCheckout ? this.creditCardCheckout!.paymentInfos : []) as CreditCardPaymentInfoData[];
  }

  get sepaBanktransferCheckout() : CheckoutInfoData | undefined {
    return this.checkoutInfos.get(PaymentMethodType.SEPA_CREDIT);
  }

  get sepaBanktransferCheckoutPaymentInfos() : SepaCreditTransferPaymentInfoData[] {
    return (this.sepaBanktransferCheckout ? this.sepaBanktransferCheckout!.paymentInfos : []) as SepaCreditTransferPaymentInfoData[];
  }

  get achBanktransferCheckout() : CheckoutInfoData | undefined {
    return this.checkoutInfos.get(PaymentMethodType.ACH_INTERNATIONAL);
  }

  get achBanktransferCheckoutPaymentInfos() : AchCreditTransferPaymentInfoData[] {
    return (this.achBanktransferCheckout ? this.achBanktransferCheckout!.paymentInfos : []) as AchCreditTransferPaymentInfoData[];
  }

  get sepaDirectDebitCheckout() : CheckoutInfoData | undefined {
    return this.checkoutInfos.get(PaymentMethodType.SEPA_DIRECTDEBIT);
  }

  get sepaDirectDebitCheckoutPaymentInfos() : SepaMandatePaymentInfoData[] {
    return (this.sepaDirectDebitCheckout ? this.sepaDirectDebitCheckout!.paymentInfos : []) as SepaMandatePaymentInfoData[];
  }

  get mergedBankTransferInfos() : InvoicePaymentInfoData[] {
    const sepaInfos : InvoicePaymentInfoData[] = this.sepaCreditTransferPaymentInfos as InvoicePaymentInfoData[];
    const achInfos : InvoicePaymentInfoData[] = this.achCreditTransferPaymentInfos as InvoicePaymentInfoData[];

    return sepaInfos.concat(achInfos);
  }

  get determineUserDefault() : PaymentInfoData | undefined {
    for (const checkoutInfo of this.pageData.checkoutInfos) {
      const foundDefault = checkoutInfo.paymentInfos.find(paymentInfo => paymentInfo.defaultPaymentInfo);
      if (foundDefault) {
        return foundDefault;
      }
    }
    return undefined;
  }

  get hasDpgCreditCardForm() : boolean | undefined {
    return this.creditCardCheckout && this.creditCardCheckout.paymentProvider === PaymentProvider.DPG && this.creditCardCheckout.userCreatable;
  }

  get checkoutPaymentMethodHeader(): string {
    return this.$t('shop.checkout.header') as string;
  }

  async initDpgComponents(sessionId : string, sessionConfig : string): Promise<void> {
    const options = prepareOptions(sessionId, sessionConfig);

    try {
      this.dpgElements = await this.dpgInstance.initialize(options);
      this.dpgElements.on(FormEvent.VALIDATION_CHANGE, this.validationHandler);
    } catch (er) {
      const error = er as IntegrationCreateTokenError;
      if (error && error.detail) {
        messageService.error(error.detail);
      } else {
        messageService.error(this.$t('shop.checkout.error') as string);
      }
    }
  }

  private validationHandler(event: validationChangePayload): void {
    switch (event.field.name) {
      case CreditCardOptionsName.CODE:
        if (!event.validation.valid) {
          this.cardCvcError = this.$t('shop.checkout.wrongValue') as string;
        } else {
          this.cardCvcError = '';
        }
        this.cardCvcIsCompleted = event.validation.completed;
        break;
      case CreditCardOptionsName.EXPIRY:
        if (!event.validation.valid) {
          this.cardExpiryError = this.$t('shop.checkout.wrongValue') as string;
        } else {
          this.cardExpiryError = '';
        }
        this.cardExpiryIsCompleted = event.validation.completed;
        break;
      case CreditCardOptionsName.NUMBER:
        if (!event.validation.valid) {
          this.cardNumberError = this.$t('shop.checkout.wrongValue') as string;
        } else {
          this.cardNumberError = '';
        }
        this.cardNumberIsCompleted = event.validation.completed;
        break;
    }
    this.cardHolderIsCompleted = true;
  }

  private initializeCurrentPaymentSelection() {
    if (this.pageData.paymentInfo) {
      this.setCurrentSelectionAttributes(this.pageData.paymentInfo.id, this.pageData.paymentInfo.paymentMethod);
    } else if (this.pageData.checkoutInfos && this.userDefaultInfo) {
      this.setCurrentSelectionAttributes(this.userDefaultInfo.id, this.userDefaultInfo.paymentMethod);
    } else if (this.invoicePaymentInfos && this.invoicePaymentInfos.length > 0) {
      this.setCurrentSelectionAttributes(this.invoicePaymentInfos[0].id, this.invoicePaymentInfos[0].paymentMethod);
    } else if (this.sepaDirectDebitCheckout) {
      this.enableNewSepaMandate();
    } else if (this.creditCardCheckout) {
      this.enableNewCreditCard();
    }
  }

  private isCCPayment() {
    return this.currentPaymentMethodType === PaymentMethodType.CREDIT_CARD;
  }

  private setPageTitle(): void {
    const pageTitle = this.$t('shop.checkout.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
    document.title = pageTitle;
    this.storeHeaderStore.setCurrentPageTitle(pageTitle);
  }

  get isNewPaymentMethod(): boolean {
    return this.currentPaymentMethodId === this.NEW_PAYMENT_METHOD;
  }

  get isNewPgwSepaMandate() : boolean {
    return (this.currentPaymentMethodId === this.NEW_SEPA_MANDATE && this.currentPaymentMethodType === PaymentMethodType.SEPA_DIRECTDEBIT);
  }

  async submit(): Promise<void> {
    pageSpinner.start();

    if (this.eulaAcceptanceActivated() && this.eulasPresent() && !this.eulaCheckboxClicked) {
      messageService.error(this.$t('shop.error.eula.notAccepted') as string, false, true);
      pageSpinner.stop();
    } else {
      if (this.isCCPayment() && this.dpgInstance) {
        await this.processDpgPayment();
      } else if(this.isNewPgwSepaMandate) {
        await this.processNewSepaMandate();
      } else {
        this.callPlaceOrder();
      }
    }
  }

  private callPlaceOrder() {
    let placeOrderUrl = this.placeOrderUrl;

    if (this.currentPaymentMethodId !== this.NEW_PAYMENT_METHOD && this.currentPaymentMethodId !== this.NEW_SEPA_MANDATE) {
      placeOrderUrl = placeOrderUrl + '?selectedPaymentMethodId=' + this.currentPaymentMethodId;
    }

    const placeOrderData = {
      cartHash: this.pageData.cartHash,
      paymentIntentId: '',
      invoiceNotes: [this.invoiceNote.firstNote, this.invoiceNote.secondNote],
      eulaAcceptanceData: {
        eulaAccepted: this.eulaCheckboxClicked
      },
      distributorID: this.distributorId
    } as PlaceOrderData;

    this.placeOrder(placeOrderUrl, placeOrderData);
  }

  private async processNewSepaMandate() : Promise<void> {
    pageSpinner.start(0);
    util.axios.post(this.paymentUrl, {
      paymentProvider: PaymentProvider.PGW,
      paymentMethod: PaymentMethodType.SEPA_DIRECTDEBIT
    })
        .then(() => this.handlePaymentSuccess())
        .catch((error) => this.handlePaymentError(error));
  }

  private async processDpgPayment(): Promise<void> {
    try {
      pageSpinner.start(0);

      const response = await this.dpgElements.createToken();
      const createTokenResponse = response as IntegrationCreateTokenResponse;

      util.axios.post(this.paymentUrl, {
        token: createTokenResponse.token,
        paymentProvider: PaymentProvider.DPG,
        owner: createTokenResponse.info.creditCard?.holder,
        saveCard: this.cardSaved,
        expiry: createTokenResponse.info.creditCard?.expiry,
        truncatedCardNumber: createTokenResponse.info.creditCard?.paddedPan,
        paymentMethod: PaymentMethodType.CREDIT_CARD
      })
          .then(() => this.handlePaymentSuccess())
          .catch((error) => this.handlePaymentError(error));
    } catch (error) {
      const createTokenErr = error as IntegrationCreateTokenError;
      this.handleCaptureResponseError(createTokenErr);
    }
  }

  private handlePaymentError(error: any): void {
    pageSpinner.stop();
    if (error.response && error.response.status === 400) {
      const generalError = error.response.data;
      if (generalError) {
        messageService.error(this.$t(generalError) as string);
      } else {
        messageService.error(this.$t('shop.error.validation') as string);
      }
    }
    messageService.error(this.$t('shop.error.backend') as string);
  }

  private handlePaymentSuccess(): void {
    pageSpinner.stop();
    this.callPlaceOrder();
  }

  private handleCaptureResponseError(error: IntegrationCreateTokenError): void {
    if (error) {
      if (error.detail) {
        messageService.error(error.detail as string);
      } else {
        messageService.error(this.$t('shop.checkout.error') as string);
      }
    }
  }

  confirmCardDelete(card: PaymentInfoData): void {
    this.confirmCard = card;
    this.showConfirmCard = true;
  }

  get confirmationDialog(): ConfirmationDialogType {
    return {
      header: this.$t('shop.checkout.deletePaymentMethod').toString(),
      buttonLabel: this.$t('delete').toString()
    };
  }

  onConfirm(): void {
    if (this.confirmCard) {
      const card = this.confirmCard;
      pageSpinner.start();
      paymentResource.deletePaymentMethod(card.id)
          .then(() => {
            this.creditCardPaymentInfos = reject(this.creditCardPaymentInfos, ['id', card.id]);

            if (this.currentPaymentMethodId === card.id) {
              this.currentPaymentMethodId = this.NO_PAYMENT_METHOD;
              this.buttonDisabled = true;
            }

            messageService.success(this.$t('shop.payment.removed') as string);
          })
          .catch((error) => {
            const response = error.response;

            if (response && response.data) {
              messageService.errorResponse(error.response?.data, this.$i18n);
            } else {
              messageService.error(this.$t('backendError') as string);
            }
          }).finally(() => {
        pageSpinner.stop();
        this.showConfirmCard = false;
      });
    }
  }

  showPaymentForm(): void {

    if (!this.$refs.paymentForm) {
      return;
    }

    this.$refs.paymentForm.style.display = 'block';
    this.renderDummy = this.$refs.paymentForm.offsetHeight;
    this.buttonDisabled = true;

    anime({
      targets: this.$refs.paymentForm,
      duration: 150,
      height: 'unset',
      easing: def.animation.easeout
    });

  }

  get paymentNotSaved(): boolean {
    return !isEmpty(this.creditCardPaymentInfos) && !find(this.creditCardPaymentInfos, 'saved');
  }

  hidePaymentForm(): void {
    if (!this.$refs.paymentForm) {
      return;
    }

    anime({
      targets: this.$refs.paymentForm,
      duration: 150,
      height: 'inherit',
      easing: def.animation.easeout,
      complete: () => {
        this.$refs.paymentForm.style.display = 'none';
        this.buttonDisabled = false;
      }
    });
  }

  setPaymentMethod(selection: PaymentInfoData): void {
    messageService.clearAll();
    this.setCurrentSelectionAttributes(selection.id, selection.paymentMethod);
    this.hidePaymentForm();
  }

  enableNewCreditCard(): void {
    messageService.clearAll();
    this.showPaymentForm();
    this.setCurrentSelectionAttributes(this.NEW_PAYMENT_METHOD, PaymentMethodType.CREDIT_CARD);
  }

  enableNewSepaMandate(): void {
    messageService.clearAll();
    this.setCurrentSelectionAttributes(this.NEW_SEPA_MANDATE, PaymentMethodType.SEPA_DIRECTDEBIT);
  }

  private setCurrentSelectionAttributes(currentPaymentMethodId : string, paymentMethodType : PaymentMethodType) : void {
    this.currentPaymentMethodId = currentPaymentMethodId;
    this.currentPaymentMethodType = paymentMethodType;
  }

  private async confirmPendingOrder() : Promise<void> {
    const maxAttempts = 5;
    const delayBetweenAttemptsMs = 3000;
    let attempt = 1;
    const placeOrderData = {
      cartHash: this.pageData.cartHash,
      paymentIntentId: '',
    } as PlaceOrderData;

    pageSpinner.start(0);
    while(this.confirmationPending) {
      await util.axios.post(this.confirmUrl, placeOrderData)
          .then((response) => {
            pageSpinner.stop();
            const dataJson: any = JSON.parse(response.data);
            if (response.data.error) {
              this.confirmationPending = false;
              window.location.assign(`${this.errorUrl}?redirectPage=${response.data.redirectPage}&errorCode=${response.data.error}`);
            } else if (dataJson.url) {
              this.confirmationPending = false;
              const url = dataJson.url;
              window.location.assign(url);
            }
          })
          .catch((error) => {
            pageSpinner.stop();
            this.handleOrderPlacementError(error);
          });
      attempt++;
      if (attempt > maxAttempts) {
        messageService.error(this.$t('shop.error.checkout.placeOrderFailed') as string);
        this.confirmationPending = false;
        break;
      }
      await new Promise(resolve => setTimeout(resolve, delayBetweenAttemptsMs));
    }
  }

  private placeOrder(url: string, placeOrderData: PlaceOrderData) {
    util.axios.post(url, placeOrderData)
        .then((response) => {
          pageSpinner.stop();
          if (response.data.error) {
            window.location.assign(`${this.errorUrl}?redirectPage=${response.data.redirectPage}&errorCode=${response.data.error}`);
          } else {
            const dataJson: any = JSON.parse(response.data);
            const url = dataJson.url;
            window.location.assign(url);
          }
        }).catch((error) => {
          pageSpinner.stop();
          this.handleOrderPlacementError(error);
        });
  }

  private handleOrderPlacementError(error : any) {
    if (error.response && error.response.status === 400) {
      const errorData = error.response.data;
      if (errorData) {
        if (!errorData.userMessages) {
          this.errorMessage = JSON.parse(errorData) as ErrorMessageData;
          messageService.error(this.errorMessage.message);
        } else {
          messageService.errorResponse(errorData as StoreErrorResponseData, this.$i18n, false, true);
        }
      } else {
        messageService.error(this.$t('shop.error.validation') as string);
      }
      return;
    }

    if (error.response && error.response.status === 409) {
      const dataJson: any = JSON.parse(error.response.data) as { error: string };
      messageService.error(dataJson.error, true);
      window.location.assign(this.cartUrl);
    }
    messageService.error(this.$t('shop.error.backend') as string);
  }

  get showSpecialOfferConditions(): boolean {
    return this.pageData.entries?.some((entry) => entry.specialOffer);
  }
}
</script>

<style scoped lang="scss">
@import "common/design";
@import "shop/core/constants";
@import "shop/pages/checkout";

.checkout-page {

  .payment-method-hint {
    color: var(--v-grey-base);
  }

  .overwrite-hint {
    color: var(--v-grey-base);
  }

  .card-entry {
    position: relative;

    .payment-form {
      display: none;

      .payment-provider-element-container {
        color: var(--v-grey-darken2);
        min-height: 71px;

        .payment-provider-field-error {
          color: var(--v-error-base);
        }

        .payment-provider-label {
          color: var(--v-grey-base);
        }

        .focus .payment-provider-label {
          color: var(--v-primary-base);
        }

        .error .payment-provider-label {
          color: var(--v-error-base);
        }

        &.dpg-element-container {
          .payment-provider-element {
            height: 40px;
            border: solid 1px var(--v-grey-darken1);
          }
        }
      }

      .card-save {
        .inner-text {
          cursor: pointer;
        }
      }
    }
  }
}
</style>
