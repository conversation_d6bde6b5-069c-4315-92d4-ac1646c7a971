import 'common/test-directive';
import 'common/testtools/unit_tests_mock';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import {cloneDeep} from 'lodash';
import CategoriesGroupsNavigation from 'aa/shop/productsoverview/components/CategoriesGroupsNavigation';
import categories from 'common/testtools/scenariosstore/aa/categories.json';
import {useStoreHeaderStore} from 'aa/shop/store/storeHeader';

jest.mock('common/util');

const mountComponent = (categoriesGroups, categoryGroupInFocus) => wrapperComponentFactory(CategoriesGroupsNavigation, {
    props: {
        categoriesGroups,
        categoryGroupInFocus
    },
    shallow: false
});

let storeHeaderStore = useStoreHeaderStore();
let categoriesTestData = {};

beforeEach(() => {
    categoriesTestData = cloneDeep(categories);
});

describe('CategoriesGroupsNavigation', () => {
    describe('Navigation component position', () => {
        it('given store header is active when render then add class to position navigation component', async () => {
            storeHeaderStore.isActive = true;
            const wrapper = mountComponent(categoriesTestData.subcategories, categoriesTestData.subcategories[0]);

            const navigationContainer = wrapper.find('[data-id="container-categories-groups-navigation"]');
            expect(navigationContainer.exists()).toBeTruthy();
            expect(navigationContainer.classes()).toContain('under-store-header');
        });

        it('given store header is not active when render then do not add class to position navigation component', async () => {
            storeHeaderStore.isActive = false;
            const wrapper = mountComponent(categoriesTestData.subcategories, categoriesTestData.subcategories[0]);

            const navigationContainer = wrapper.find('[data-id="container-categories-groups-navigation"]');
            expect(navigationContainer.exists()).toBeTruthy();
            expect(navigationContainer.classes()).not.toContain('under-store-header');
        });
    });
});
