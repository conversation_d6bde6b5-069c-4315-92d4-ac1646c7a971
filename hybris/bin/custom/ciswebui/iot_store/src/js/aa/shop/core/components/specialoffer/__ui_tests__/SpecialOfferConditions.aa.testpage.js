import 'common/testtools/ui_tests_mock';
import {createApp} from 'vue-demi';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';
import {useCoreDataStore} from 'shop/store/coreData';
import coreData from 'common/testtools/scenariosstore/aa/coreDataFromRest.json';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import SpecialOfferConditions from 'aa/shop/core/components/specialoffer/SpecialOfferConditions.vue';

const options = {
    vuetify,
    components: {
        TestPageRoot,
        SpecialOfferConditions
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <SpecialOfferConditions />
        </div>
      </test-page-root>
    `
};

const app = createApp(options);
app.use(i18n);
app.use(pinia);
app.use(() => {
    const coreDataStore = useCoreDataStore();
    coreDataStore.coreData = coreData;
});
app.mount('#vue-app');
