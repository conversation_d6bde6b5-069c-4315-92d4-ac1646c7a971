<template>
  <div v-if="isToolProduct()" id="tool-app-cta" class="tool-cta button-wrapper">
    <v-row>
      <v-col cols="12" class="d-md-flex">
        <div class="action-button tool-button">
          <CDButton id="tool"
                    data-license-type="tool"
                    color="primary"
                    v-on:click="$parent.$emit('licenseButtonClicked', LicenseType.TOOL)">
            {{$t('shop.productDetails.productHeader.getFreeToolButton')}}
          </CDButton>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {find} from 'lodash';
import {ProductLicenseData} from 'common/generated-types/types';
import {LicenseType} from 'common/types';

@Component
export default class ToolAppCta extends Vue {
  @Prop() licenses!: ProductLicenseData[];

  LicenseType = LicenseType;

  isToolProduct(): boolean {
    return !!find(this.licenses, ['licenseType.code', LicenseType.TOOL]);
  }
}
</script>

