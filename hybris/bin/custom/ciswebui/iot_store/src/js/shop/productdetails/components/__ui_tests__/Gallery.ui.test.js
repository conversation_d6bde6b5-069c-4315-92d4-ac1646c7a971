describe("Gallery", () => {
    it("opens and closes gallery slideshow as expected", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-Gallery');
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.click(".image:nth-of-type(1)");
        await page.waitFor(400);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("zoom opened");

        await page.click(".vgs__gallery__container__img:nth-of-type(5)");
        await page.waitFor(400);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("select another image");

        await page.click(".shop");
        await page.waitFor(400);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("zoom closed on click outside");
    })
});