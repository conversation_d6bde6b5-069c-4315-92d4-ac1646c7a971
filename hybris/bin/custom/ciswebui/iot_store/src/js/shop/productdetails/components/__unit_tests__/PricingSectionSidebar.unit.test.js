import {shallowMount} from '@vue/test-utils';
import {PricingSectionSidebar} from 'shop/productdetails/components';
import {LicenseType} from 'common/types';

jest.mock('common/util');

const thirdPartyLicense = [
    {
        code: LicenseType.EVALUATION,
        licenseType: {
            code: LicenseType.EVALUATION,
            type: LicenseType.EVALUATION
        }
    }
];

const toolLicense = [
    {
        code: LicenseType.TOOL,
        licenseType: {
            code: LicenseType.TOOL,
            type: LicenseType.TOOL
        }
    }
];

const mountPricingSectionSidebar = (licenses) => shallowMount(PricingSectionSidebar, {
    mocks: {
        $t: () => {
        }
    },
    propsData: {
        product: {
            licenses: licenses,
            supportedLicenses: [
            ]
        }
    }
});

describe('Pricing section Sidebar', () => {

    it('Private Offer Request is displayed when Third Party App', () => {
        const wrapper = mountPricingSectionSidebar(thirdPartyLicense);
        expect(wrapper.find('#private-offer-request-container').exists()).toBeTruthy();
    });

    it('Private Offer Request is not displayed when Tool', () => {
        const wrapper = mountPricingSectionSidebar(toolLicense);
        expect(wrapper.find('#private-offer-request-container').exists()).toBeFalsy();
    });
});
