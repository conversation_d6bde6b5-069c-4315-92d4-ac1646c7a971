import {LicenseType} from 'common/types';
import util from 'common/util';
import {AxiosResponse} from 'axios';
import {PrivateOfferProjectRegistrationData} from 'common/generated-types/types';

export interface PrivateOfferRequestData {
    appCode: string;
    messageText: string;
    privateOfferRequestItems: PrivateOfferRequestItemData[];
    projectRegistration: PrivateOfferProjectRegistrationData;
    registerProject: boolean;
}

export interface PrivateOfferRequestItemData {
    quantity: number;
    originalPrice: number;
    requestPrice: number;
    licenseType: LicenseType
}

const axios = util.axios;

export const PRIVATE_OFFER_RESOURCE = `/shop/api/private-offers/requests`;

export default class PrivateOfferResource {
    createPrivateOffer(privateOfferRequestData: PrivateOfferRequestData):Promise<AxiosResponse<void>> {
        return axios.post(PRIVATE_OFFER_RESOURCE, privateOfferRequestData);
    }
};