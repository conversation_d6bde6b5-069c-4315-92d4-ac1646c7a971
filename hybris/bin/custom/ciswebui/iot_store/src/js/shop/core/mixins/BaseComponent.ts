import MessageComponent from 'common/components/MessageComponent.vue';
import StoreHeader from 'shop/core/components/StoreHeader.vue';
import PageFooter from 'common/components/PageFooter.vue';
import {Component, Vue} from 'vue-property-decorator';
import VueI18n from 'vue-i18n';
import 'common/test-directive';
import {i18n} from 'common/i18n';

declare global {
    interface Window {
        frontendData: any;
    }
}

Vue.use(VueI18n);

@Component({
    i18n,
    components: {
        MessageComponent,
        StoreHeader,
        PageFooter
    }
})
export default class BaseComponent extends Vue {

    pageData = window.frontendData.pageData || {};
    coreData = {
        ...window.frontendData.coreData,
        scrollingOffsetY: 0,
        alert: '',
        error: false
    };

    removeAlert() {
        this.coreData.alert = '';
        this.coreData.error = false;
    }

    updateMessageToUser(alertMessage: string, error: boolean) {
        this.coreData.alert = alertMessage;
        this.coreData.error = error;
    }
};
