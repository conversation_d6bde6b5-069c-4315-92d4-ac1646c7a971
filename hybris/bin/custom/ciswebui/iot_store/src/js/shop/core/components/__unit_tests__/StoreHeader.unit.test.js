import 'common/testtools/unit_tests_mock';
import StoreHeader from 'shop/core/components/StoreHeader';
import 'common/test-directive';
import {NavigationItemType} from 'common/generated-types/types';
import coreDataDefault from 'common/testtools/scenariosstore/coreDataDefault.json';
import {cloneDeep} from 'lodash'
import navigationItemsForAnonymousUser from 'common/testtools/scenariosstore/navigationItemsForAnonymousUser.json';
import navigationItemsForAuthenticatedUser from 'common/testtools/scenariosstore/navigationItemsForAuthenticatedUser.json';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';

jest.mock('common/util');

const mountComponent = () => wrapperComponentFactory(StoreHeader);

describe('StoreHeader', () => {

    it('prefixes icon when Global and not prefixed', () => {
        const navigationItem = {
            type: NavigationItemType.GLOBAL,
            icon: 'test'
        };
        const wrapper = mountComponent();

        expect(wrapper.vm.getIconForGlobalNavItem(navigationItem)).toBe('$test');
    });

    it('does not prefix icon when already prefixed', () => {
        const navigationItem = {
            type: NavigationItemType.GLOBAL,
            icon: '$test'
        };
        const wrapper = mountComponent();

        expect(wrapper.vm.getIconForGlobalNavItem(navigationItem)).toBe('$test');
    });

    it('does not prefix icon when not Global Nav item', () => {
        const navigationItem = {
            type: NavigationItemType.DEVCON,
            icon: 'test'
        };
        const wrapper = mountComponent();

        expect(wrapper.vm.getIconForGlobalNavItem(navigationItem)).toBe('test');
    });

    describe('Flyout menu', () => {
        let coreData = {};
        beforeEach(() => {
            coreData = cloneDeep(coreDataDefault);
            window.frontendData.coreData = coreData;
        });
        it('contains correct items for anonymous user in mobile viewport', async () => {
            window.frontendData.coreData.navigationItems = navigationItemsForAnonymousUser;

            const wrapper = mountComponent();
            await wrapper.vm.$nextTick();

            const anonymousMobileMenu = wrapper.vm.anonymousMobileMenu;

            const anonymousMobileMenuIds = anonymousMobileMenu.map(a => a.id);
            expect(anonymousMobileMenuIds).toHaveLength(5);
            expect(anonymousMobileMenuIds).toEqual(
                ["storeRegister", "storeLogin", "appProductCategory", "toolProductCategory", "storeHelpAndResources"]
            );
        });

        it('contains correct items for authenticated user in mobile viewport', async () => {
            window.frontendData.coreData.navigationItems = navigationItemsForAuthenticatedUser

            const wrapper = mountComponent()
            await wrapper.vm.$nextTick()

            const authenticatedMobileMenu = wrapper.vm.authenticatedMobileMenu

            const authenticatedMobileMenuIds = authenticatedMobileMenu.map(a => a.id);
            expect(authenticatedMobileMenuIds).toHaveLength(6)
            expect(authenticatedMobileMenuIds).toEqual(
                ["globalMyProfile", "globalMyCompany", "storeOrderHistory", "storePaymentDetails", "storeHelpAndResources", "storeSignOut"]
            );
        });

        it('contains correct items for authenticated user in desktop viewport', async () => {
            window.frontendData.coreData.navigationItems = navigationItemsForAuthenticatedUser

            const wrapper = mountComponent()
            await wrapper.vm.$nextTick()

            const authenticatedDesktopMenu = wrapper.vm.authenticatedDesktopMenu

            const authenticatedDesktopMenuIds = authenticatedDesktopMenu.map(a => a.id);
            expect(authenticatedDesktopMenuIds).toHaveLength(6)
            expect(authenticatedDesktopMenuIds).toEqual(
                ["globalMyProfile", "globalMyCompany", "storeOrderHistory", "storePaymentDetails", "globalSupport", "storeSignOut"]
            );
        })
    });
});
