import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils'
import SepaDirectDebitActions from 'shop/account/payment/components/SepaDirectDebitActions.vue'
import paymentDetailsData from 'common/testtools/scenariosstore/aa/paymentDetailsData.json';

jest.mock('common/util');
jest.mock('common/services/companyPermissionService');

const defaultPaymentInfoList = paymentDetailsData.directDebitPaymentInfos
const sepaDirectDebit = defaultPaymentInfoList[0];
const mountComponent = (sepaDirectDebitPaymentInfo = sepaDirectDebit) => wrapperComponentFactory(SepaDirectDebitActions, {
    props: {
        sepaDirectDebit: sepaDirectDebitPaymentInfo,
    },
    shallow: false
});

describe('SepaDirectDebitActions', () => {


    it('should render SepaDirectDebitActions component without default payment info has option to set as default', () => {
        sepaDirectDebit.defaultPaymentInfo = false;
        const wrapper = mountComponent(sepaDirectDebit);

        expect(wrapper.find(`[data-id="set-default-payment"]`).exists()).toBeTruthy();
        expect(wrapper.find(`[data-id="delete-non-default-payment"]`).exists()).toBeTruthy();
        expect(wrapper.find(`[data-id="remove-payment-option-dialog"]`).exists()).toBeTruthy();
    });

    it('should render SepaDirectDebitActions component with default payment info has option to remove as default', () => {
        sepaDirectDebit.defaultPaymentInfo = true;
        const wrapper = mountComponent(sepaDirectDebit);
        expect(wrapper.find(`[data-id="unset-default-payment"]`).exists()).toBeTruthy();
        expect(wrapper.find(`[data-id="delete-default-payment"]`).exists()).toBeTruthy();
        expect(wrapper.find(`[data-id="remove-payment-option-dialog"]`).exists()).toBeTruthy();
    });
});

