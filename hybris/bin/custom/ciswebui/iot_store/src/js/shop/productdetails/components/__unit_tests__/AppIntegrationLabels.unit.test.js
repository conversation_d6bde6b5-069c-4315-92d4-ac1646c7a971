import {shallowMount} from '@vue/test-utils';
import AppIntegrationLabels from 'shop/productdetails/components/AppIntegrationLabels'
import {AppIntegrationType, StandardAppIntegrationType} from "../../../../common/generated-types/types";

jest.mock('common/util');

const propsData = {
    appIntegrations: [
        {
            type: AppIntegrationType.CUSTOM,
            integrationType: null,
            displayName: 'A custom integration'
        },
        {
            type: AppIntegrationType.STANDARD,
            integrationType: StandardAppIntegrationType.STANDARD,
            displayName: 'ONVIF Integration'
        },
        {
            type: AppIntegrationType.CUSTOM,
            integrationType: null,
            displayName: 'Another integration'
        },
        {
            type: AppIntegrationType.STANDARD,
            integrationType: StandardAppIntegrationType.GATEWAY,
            displayName: 'IoT Gateway'
        }
    ]
};

const mountAppIntegrationLabels = (props) => shallowMount(AppIntegrationLabels, {
    mocks: {
        $t: (word) => {
            return word;
        }
    },
    stubs: {},
    propsData: props
});

describe('AppIntegrationLabels', () => {
    it('reduces labels', async () => {
        const wrapper = mountAppIntegrationLabels(propsData);
        expect(wrapper.vm.appIntegrationList).toHaveLength(3);
        expect(wrapper.vm.appIntegrationList).toStrictEqual([
            {displayName: 'ONVIF Integration', iconType: StandardAppIntegrationType.STANDARD},
            {displayName: 'IoT Gateway', iconType: StandardAppIntegrationType.GATEWAY},
            {displayName: 'shop.productDetails.integrationsSection.customIntegrationName', iconType: null}
        ]);
    });

    it('populates items with correct text', async () => {
        const wrapper = mountAppIntegrationLabels(propsData);
        await wrapper.vm.$nextTick();
        const integrationLabels = wrapper.findAll('[data-id="text-app-integration"]').wrappers;
        expect(integrationLabels).toHaveLength(3);
        expect(integrationLabels[0].text()).toEqual('ONVIF Integration');
        expect(integrationLabels[1].text()).toEqual('IoT Gateway');
        expect(integrationLabels[2].text()).toEqual('shop.productDetails.integrationsSection.customIntegrationName');
    });

    it('does not show custom integration when there are none', async () => {
        const props = {
            appIntegrations: [
                {
                    type: AppIntegrationType.STANDARD,
                    integrationType: StandardAppIntegrationType.GATEWAY,
                    displayName: 'IoT Gateway'
                },
                {
                    type: AppIntegrationType.STANDARD,
                    integrationType: StandardAppIntegrationType.STANDARD,
                    displayName: 'ONVIF Integration'
                }
            ]
        };

        const wrapper = mountAppIntegrationLabels(props);
        await wrapper.vm.$nextTick();
        const integrationLabels = wrapper.findAll('[data-id="text-app-integration"]').wrappers;
        expect(integrationLabels).toHaveLength(2);
        expect(integrationLabels[0].text()).toEqual('IoT Gateway');
        expect(integrationLabels[1].text()).toEqual('ONVIF Integration');
    });

})
