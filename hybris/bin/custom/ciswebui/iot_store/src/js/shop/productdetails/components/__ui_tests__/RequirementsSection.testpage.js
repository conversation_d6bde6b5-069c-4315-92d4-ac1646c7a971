import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import { RequirementsSection } from 'shop/productdetails/components';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    vuetify,
    i18n,
    data: {
        osCompatibility: {
            minOsVersion: '1.0',
            maxOsVersion: ''
        },
        deviceCapabilities: [
            {
                code: 'Ptz'
            },
            {
                code: 'SOME_CAPABILITIES'
            }
        ],
        permissions: [
            {
                name: 'Internet'
            },
            {
                name: 'SOME_PERMISSION'
            }
        ]
    },
    components: {
        TestPageRoot,
        RequirementsSection
    },
    methods: {},
    template: `
      <test-page-root>
        <requirements-section 
            v-bind:os-compatibility="osCompatibility"
            v-bind:device-capabilities="deviceCapabilities" 
            v-bind:permissions="permissions">
        </requirements-section>
      </test-page-root>
    `
});
