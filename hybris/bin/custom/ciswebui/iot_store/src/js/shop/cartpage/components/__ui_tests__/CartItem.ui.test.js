describe('Cart Item', () => {

    let styleContent;

    beforeEach(() => {
        styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    });

    it('Edit quantity by pressing enter', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-cartpage-components-__ui_tests__-CartItem');
        await page.addStyleTag({ content: styleContent });
        expect(await page.title()).toBe('UI test - shop-cartpage-components-__ui_tests__-CartItem');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'initial cart list'
        });

        const input = await page.$('input');
        input.focus();
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'active input'
        });

        await page.keyboard.press('Backspace');
        await input.type('3', {});

        await page.keyboard.press('Enter');
        await page.waitFor(() => !document.querySelector('.spinner .loader'));
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'edited cart list'
        });
    });

    it('Edit quantity by losing focus', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-cartpage-components-__ui_tests__-CartItem');
        await page.addStyleTag({ content: styleContent });
        expect(await page.title()).toBe('UI test - shop-cartpage-components-__ui_tests__-CartItem');

        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'initial cart list'
        });

        const input = await page.$('input[class*="detail-quantity"]');
        await page.keyboard.press('Backspace');
        await input.type('3', {});
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'edited cart list'
        });
    });

    it('Edit quantity and pressing escape', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-cartpage-components-__ui_tests__-CartItem');
        await page.addStyleTag({ content: styleContent });
        await page.waitFor(1000);
        expect(await page.title()).toBe('UI test - shop-cartpage-components-__ui_tests__-CartItem');

        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'initial cart list'
        });

        const input = await page.$('input[class*="detail-quantity"]');
        await page.keyboard.press('Backspace');
        await input.type('123', {});
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'edited cart list'
        });

        await page.keyboard.press('Escape');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'cart list after escape'
        });
    });

    it('Cart with discounts', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-cartpage-components-__ui_tests__-CartItem.with.discounts');
        await page.addStyleTag({ content: styleContent });
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'initial cart with discount'
        });

        await page.hover('.hint');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'cart discounts hovered'
        });
    });
});
