<template>
  <CDHelpFlyout v-bind="FlyoutMenuFormatting" :header="cdHelpHeader">
    <v-row v-if="globalHelpSupport && globalHelpContact" class="mb-2">
      <v-col cols="12">
        <CDButton target="_blank" v-if="globalHelpSupport" :href="globalHelpSupport.url" class="mr-2">{{ translateNavigation(globalHelpSupport) }}</CDButton>
        <CDButton target="_blank" v-if="globalHelpContact" :href="globalHelpContact.url">{{ translateNavigation(globalHelpContact) }}</CDButton>
      </v-col>
    </v-row>
    <CDFlyoutMenuItem
        class="mb-4"
        v-for="item in helpEntries"
        :key="item.id"
        :href="item.url"
        :target="item.target"
        :img="item.icon"
        :title="translateNavigation(item)"
        :text="item.description"
        titleclass="text-h3"
        textclass="text-body-1">
    </CDFlyoutMenuItem>
  </CDHelpFlyout>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {NavigationItemData} from 'common/generated-types/types';
import {navigationService, userPermissionService} from 'common/services';

@Component
export default class HelpFlyout extends Vue {

  navigationService = navigationService;
  userPermissionService = userPermissionService;

  cdHelpHeader = {
    text: this.$t('navigation.helpAndResources'),
    label: ''
  };

  FlyoutMenuFormatting = {
    nudgeBottom: 8,
    nudgeRight: 38,
    zIndex: 200
  };

  translateNavigation(navigationItem : NavigationItemData) : string {
    return navigationService.translate(this.$i18n, navigationItem);
  }

  get globalHelpSupport() {
    return navigationService.byId('globalHelpSupport');
  }

  get globalHelpContact() {
    return navigationService.globalHelpContactItem();
  }

  get helpEntries(): NavigationItemData[] {
    return navigationService.helpItems();
  }

}
</script>
