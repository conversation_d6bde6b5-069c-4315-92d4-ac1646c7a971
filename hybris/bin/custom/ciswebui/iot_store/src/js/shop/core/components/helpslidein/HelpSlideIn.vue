<template>
  <CDNeedHelpButton :logged-in="isUserLoggedIn"
                    id="need-help-slidein-button"
                    data-id="button-need-help-slidein"
                    :support-request-url="contactUrl">
  </CDNeedHelpButton>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { userPermissionService, navigationService } from 'common/services';

@Component
export default class HelpSlideIn extends Vue {

  get contactUrl(): string {
    return navigationService.globalHelpContactItem()?.url;
  }

  get isUserLoggedIn(): boolean {
    return !userPermissionService.isAnonymous();
  }

}
</script>

<style scoped lang="scss">

#need-help-slidein-button {
  :deep(b) {
    color: inherit;
    font-weight: inherit;
  }
}
</style>
