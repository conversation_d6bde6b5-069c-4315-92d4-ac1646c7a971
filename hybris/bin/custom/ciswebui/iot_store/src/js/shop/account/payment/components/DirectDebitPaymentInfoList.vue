<template>

  <div v-if="displaySepaDD"
       data-id="container-sepa-dd-payment-infos"
       class="mb-16">
    <h2 class="mb-4">{{ $t('shop.payment.sepaDirectDebit') }}</h2>

    <div v-if="companyHasDDPaymentInfos">
      <CDBlock
          v-for="paymentInfo in sortedPaymentInfos"
          :key="paymentInfo.id"
          :padding-all="false"
          :border="false"
          :border-bottom="true"
          :data-id="`container-sepa-dd-payment-info-${paymentInfo.id}`"
          class="mb-8 py-2 pa-md-8">
            <payment-method-entry :payment-data="paymentInfo"
                                  :actions="true"
                                  :is-sepa-mandate-enabled="props.isSepaMandateEnabled"
                                  @payment-info-deleted="raiseEvent"
                                  @default-payment-info-set="setAsDefault"
                                  @default-payment-info-unset="removeDefault"
                                  @payment-info-viewed="viewPaymentInfo">
            </payment-method-entry>
      </CDBlock>
    </div>

    <div v-if="allowCreationOfDDPaymentInfo"
         data-id="container-sepa-dd-payment-info-creation">
      <CDBlock
          :padding-all="false"
          :border="false"
          :border-bottom="false"
          class="mb-8 py-2 pa-md-8">
        <v-row>
          <v-col cols="12">
            <payment-method-entry :new-entry="true"
                                  :new-entry-type="PaymentMethodType.SEPA_DIRECTDEBIT">
            </payment-method-entry>
          </v-col>
          <v-col cols="3">
            <CDButton data-id="button-trigger-sepa-dd-payment-info-creation"
                      @click="triggerPaymentInfoCreation">
              {{ $t('shop.payment.createDirectDebitPaymentInfo') }}
            </CDButton>
          </v-col>
        </v-row>
      </CDBlock>
    </div>

  </div>
</template>

<script setup lang="ts">

import {
	PaymentMethodType,
	PaymentProvider,
	SepaMandatePaymentInfoData
} from 'common/generated-types/types';
import {PaymentMethodEntry} from 'shop/checkout/components';
import {computed} from 'vue';
import {companyPermissionService, messageService} from 'common/services';
import {pageSpinner} from 'common/components/spinner';
import {paymentResource, StoreDataProvider} from 'shop/resources';
import {useI18n} from 'vue-i18n-bridge';
import {sortBy} from 'lodash';

const props = withDefaults(defineProps<{
  directDebitPaymentInfoList: SepaMandatePaymentInfoData[],
  isSepaMandateEnabled?: boolean
}>(), {
  isSepaMandateEnabled: false
});

const {t} = useI18n();

const displaySepaDD = computed(() => companyHasDDPaymentInfos.value || allowCreationOfDDPaymentInfo.value);

const companyHasDDPaymentInfos = computed(() => props.directDebitPaymentInfoList.length > 0);

const allowCreationOfDDPaymentInfo = computed(() => companyPermissionService.allowCurrentCompanyCreationOfSepaDDPaymentInfo());

const sortedPaymentInfos = computed(() => sortBy(props.directDebitPaymentInfoList, (paymentInfo) => new Date(paymentInfo.dateOfSignature)).reverse());

const emit = defineEmits(['removeSepaDDEntry', 'addSepaMandate', 'viewSepaMandate']);

const triggerPaymentInfoCreation = () => {
  pageSpinner.start();
  if (props.isSepaMandateEnabled) {
    emit('addSepaMandate');
    pageSpinner.stop();
    return;
  }
  paymentResource.createPaymentInfo(PaymentProvider.PGW, PaymentMethodType.SEPA_DIRECTDEBIT)
      .then(response => {
        const data = response.data;
        // HPP redirect url
        const url = data.userActionParameters.url;
        if (url) {
          window.location.assign(url);
        }
      })
      .catch(error => {
        if (error.response && error.response.status === 400 && error.response.data) {
          messageService.error(t(error.response.data) as string);
        } else {
          messageService.error(t('shop.error.backend') as string);
        }
      })
      .finally(() => pageSpinner.stop());
};

function getDirectDebitPaymentInfos() {
	return StoreDataProvider.pageData.directDebitPaymentInfos;
}
function setDirectDebitPaymentInfos(paymentInfos: SepaMandatePaymentInfoData[]): void {
    StoreDataProvider.pageData.directDebitPaymentInfos = paymentInfos;
}

function raiseEvent(paymentInfoId: string): void {
	emit('removeSepaDDEntry', paymentInfoId);
}

function setAsDefault(paymentInfoId: string): void {
	setDirectDebitPaymentInfos(getDirectDebitPaymentInfos().map((paymentInfo: SepaMandatePaymentInfoData) => {
		paymentInfo.defaultPaymentInfo = paymentInfo.id === paymentInfoId;
		return paymentInfo;
	}));
}

function removeDefault(paymentInfoId: string): void {
	setDirectDebitPaymentInfos(getDirectDebitPaymentInfos().map((paymentInfo: SepaMandatePaymentInfoData) => {
		if (paymentInfo.id === paymentInfoId) {
			paymentInfo.defaultPaymentInfo = false;
		}
		return paymentInfo;
	}));
}

function viewPaymentInfo(paymentInfoId: string): void {
  if (props.isSepaMandateEnabled) {
    emit('viewSepaMandate', paymentInfoId);
  }
}

</script>
