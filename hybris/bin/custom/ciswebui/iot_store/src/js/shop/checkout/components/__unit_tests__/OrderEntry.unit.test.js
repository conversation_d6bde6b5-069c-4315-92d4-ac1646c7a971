import { mount } from '@vue/test-utils';
import OrderEntry from 'shop/checkout/components/OrderEntry';
import { cloneDeep } from 'lodash';

jest.mock('common/util');

const defaultCoreData = {
  moduleConfig : {}
};

const mountOrderEntry = (cartItemData, isOwnAppPurchase = false) => mount(OrderEntry, {
  mocks: {
    $t: (key) => {
      return key;
    }
  },
  propsData: {
    data: cartItemData,
    isOwnAppPurchase
  }
});

const defaultSubscriptionCartItemData = {
  appCode: 'A_00000119',
  sellerProductId: "1 687 P15 100",
  productName: 'SampleSubscription 1',
  productCode: 'A_00000119_subscription',
  productUrl: '/p/A_00000119',
  companyName: 'DPG Company',
  versionName: '1.0.0',
  licenseName: 'Subscription',
  licenseType: 'SUBSCRIPTION',
  logoUrl: '/sample-data/icon-subscription-app-license.png',
  itemPrice: {
    symbol: 'EUR',
    value: '20.00'
  },
  totalPrice: {
    symbol: 'EUR',
    value: '40.00'
  },
  productFuturePrices: [
    {
      symbol: 'EUR',
      value: '400.00'
    }
  ]
};

describe('Order Entry', () => {

  beforeAll(() => {
    window.frontendData = {
      coreData: defaultCoreData
    };
  });

  let cartItem;
  beforeEach(() => {
    cartItem = cloneDeep(defaultSubscriptionCartItemData);
  });

  describe('futureSubscriptionPrice', () => {
    it('given subscription entry has future price then return price data', async () => {
      const wrapper = mountOrderEntry(cartItem);
      await wrapper.vm.$nextTick();
      const futureSubscriptionPrice = wrapper.vm.futureSubscriptionPrice;

      expect(futureSubscriptionPrice).not.toBeNull();
      expect(futureSubscriptionPrice).toBe(cartItem.productFuturePrices[0]);
    });

    it('given subscription entry has no future price then return null', async () => {
      cartItem.productFuturePrices = [];

      const wrapper = mountOrderEntry(cartItem);
      await wrapper.vm.$nextTick();
      const futureSubscriptionPrice = wrapper.vm.futureSubscriptionPrice;

      expect(futureSubscriptionPrice).toBeNull();
    });
  });

  describe('isSubscription', () => {
    it('given subscription then true', async () => {
      const wrapper = mountOrderEntry(cartItem);
      await wrapper.vm.$nextTick();
      const isSubscription = wrapper.vm.isSubscription;

      expect(isSubscription).toBeTruthy();
    });

    it('given subscription entry has no future price then return null', async () => {
      cartItem.licenseType = 'FULL';

      const wrapper = mountOrderEntry(cartItem);
      await wrapper.vm.$nextTick();
      const isSubscription = wrapper.vm.isSubscription;

      expect(isSubscription).toBeFalsy();
    });
  });

  it('given order is not own app purchase then display total price', async () => {
    const wrapper = mountOrderEntry(cartItem);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="text-total-price"]').exists()).toBeTruthy();
  });

  it('given order is own app purchase then do not display total price', async () => {
    const wrapper = mountOrderEntry(cartItem, true);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="text-total-price"]').exists()).toBeFalsy();
  });

  it('given order is not own app purchase then display item price', async () => {
    const wrapper = mountOrderEntry(cartItem);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="container-item-price"]').exists()).toBeTruthy();
  });

  it('given order is own app purchase then do not display item price', async () => {
    const wrapper = mountOrderEntry(cartItem, true);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="container-item-price"]').exists()).toBeFalsy();
  });

  it('given order is a subscription order and item has future prices then display future prices', async () => {
    const wrapper = mountOrderEntry(cartItem);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="text-future-price"]').exists()).toBeTruthy();
  });

  it('given order is a subscription order and item has no future prices then do not display future prices', async () => {
    cartItem.productFuturePrices = [];

    const wrapper = mountOrderEntry(cartItem);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="text-future-price"]').exists()).toBeFalsy();
  });

  it('given order is not a subscription order and item has future prices then do not display future prices', async () => {
    cartItem.licenseType = 'FULL';

    const wrapper = mountOrderEntry(cartItem);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="text-future-price"]').exists()).toBeFalsy();
  });

  it('given order is not own app purchase then do not display fee note', async () => {
    const wrapper = mountOrderEntry(cartItem);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="container-fee-note"]').exists()).toBeFalsy();
  });

  it('given order is own app purchase then display fee note', async () => {
    const wrapper = mountOrderEntry(cartItem, true);
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-id="container-fee-note"]').exists()).toBeTruthy();
  });
});