<template>
    <div class="description mt-0">
        <section>
            <div class="rich-text" v-html="productDescription" v-transformExternalLinks></div>
        </section>
    </div>
</template>

<script lang="ts">
    import { Component, Vue, Prop } from 'vue-property-decorator';
    @Component
    export default class DescriptionSection extends Vue {
        @Prop() productName!: string;
        @Prop() productDescription!: string;
    }
</script>
