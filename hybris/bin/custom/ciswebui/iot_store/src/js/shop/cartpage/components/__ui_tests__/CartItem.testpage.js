import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import CartItem from 'shop/cartpage/components/CartItem';
import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import pinia from 'shop/store';
import vuetify from 'common/plugins/brands/azena/vuetify';

const mock = new MockAdapter(axios);
mock.onPut('/shop/api/cart/test-cart').reply(200, {
    response: {
        cartItems: [{
            appCode: 'A_12345678',
            quantity: 3,
            logoUrl: '/sample-data/square-16.png',
            entryNumber: 0,
            productCode: 'productCode',
            productUrl: 'productUrl',
            productName: 'productName',
            companyName: 'companyName',
            licenseName: 'licenseName',
            licenseType: 'FULL',
            itemPrice: {
                value: '19.99',
                symbol: 'USD'
            },
            totalPrice: {
                value: '19.99',
                symbol: 'USD'
            }
        }]
    }
});

new Vue({
    el: '#vue-app',
    vuetify,
    i18n: {
        locale: 'en'
    },
    components: {
        TestPageRoot,
        CartItem
    },
    pinia,
    data: {
        cartItems: [{
            appCode: 'A_12345678',
            quantity: 1,
            logoUrl: '/sample-data/square-16.png',
            entryNumber: 0,
            productCode: 'productCode',
            productUrl: 'productUrl',
            productName: 'productName',
            companyName: 'companyName',
            licenseName: 'licenseName',
            licenseType: 'FULL',
            itemPrice: {
                value: '19.99',
                symbol: 'USD'
            },
            totalPrice: {
                value: '19.99',
                symbol: 'USD'
            }
        }]
    },
    methods: {
        updateCartStatus() {
            console.log('update cart status');
        },
        onCartDataChanged(data) {
            console.log(this.cartItems);
            this.cartItems = data.cartItems;
            console.log(data.cartItems);
        },
        updateMessageToUser() {
            console.log('update message to user');
        }
    },
    template: `
      <test-page-root>
        <div style="padding: 20px">
          <cart-item v-for="item in cartItems" v-bind:item="item"
                     :key="item.productCode"
                     cart-code="test-cart"
                     v-on:cartquantitychanged="updateCartStatus"
                     v-on:cartdatachanged="onCartDataChanged"
                     v-on:showmessagetouser="updateMessageToUser">
          </cart-item>
        </div>
      </test-page-root>
    `
});
