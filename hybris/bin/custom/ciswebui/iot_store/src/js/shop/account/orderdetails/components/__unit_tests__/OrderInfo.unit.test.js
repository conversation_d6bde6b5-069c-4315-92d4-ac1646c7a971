import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import OrderInfo from 'shop/account/orderdetails/components/OrderInfo';
import 'common/test-directive';
import orderDetailsWithInvoicesData from 'common/testtools/scenariosstore/orderDetailsWithInvoicesData.json';
import {cloneDeep} from 'lodash';
import {PaymentMethodType} from 'common/generated-types/types';
import en from 'common/i18n/en.json';

Vue.use(VueI18n)

const messages = {
    en: en
}
const i18n = {
    locale: 'en',
    messages
};

const mountOrderInfo = (props) => mount(OrderInfo, {
    i18n,
    propsData: {
        orderDetailsData: props.orderDetailsData,
    }
});

describe('OrderInfo', () => {
    let propsData = {};
    beforeEach(() => {
        propsData = cloneDeep({
            orderDetailsData: orderDetailsWithInvoicesData
        });
    });

    it('displays order info', async () => {
        const wrapper = mountOrderInfo(propsData);
        await wrapper.vm.$nextTick();

        const orderPlacedDate = wrapper.find('[data-id="text-order-placed-date"]');
        expect(orderPlacedDate.exists()).toBeTruthy();
        const orderPlacedBy = wrapper.find('[data-id="text-order-placed-by"]');
        expect(orderPlacedBy.exists()).toBeTruthy();
        expect(orderPlacedBy.text()).toBe('Tim Installer');
    });

    describe('displays correct label for payment method', () => {
        it('SEPA', async () => {
            propsData.orderDetailsData.paymentMethod = PaymentMethodType.SEPA_CREDIT;

            const wrapper = mountOrderInfo(propsData);
            await wrapper.vm.$nextTick();

            const orderPlacedBy = wrapper.find('[data-id="text-order-payment-method"]');
            expect(orderPlacedBy.exists()).toBeTruthy();
            expect(orderPlacedBy.text()).toBe('SEPA Credit Transfer');
        });

        it('ACH International', async () => {
            propsData.orderDetailsData.paymentMethod = PaymentMethodType.ACH_INTERNATIONAL;

            const wrapper = mountOrderInfo(propsData);
            await wrapper.vm.$nextTick();

            const orderPlacedBy = wrapper.find('[data-id="text-order-payment-method"]');
            expect(orderPlacedBy.exists()).toBeTruthy();
            expect(orderPlacedBy.text()).toBe('ACH Credit Transfer');
        });

        it('Credit Card', async () => {
            propsData.orderDetailsData.paymentMethod = PaymentMethodType.CREDIT_CARD;

            const wrapper = mountOrderInfo(propsData);
            await wrapper.vm.$nextTick();

            const orderPlacedBy = wrapper.find('[data-id="text-order-payment-method"]');
            expect(orderPlacedBy.exists()).toBeTruthy();
            expect(orderPlacedBy.text()).toBe('Credit Card');
        });

        it('Invoice By Seller', async () => {
            propsData.orderDetailsData.paymentMethod = PaymentMethodType.INVOICE_BY_SELLER;

            const wrapper = mountOrderInfo(propsData);
            await wrapper.vm.$nextTick();

            const orderPlacedBy = wrapper.find('[data-id="text-order-payment-method"]');
            expect(orderPlacedBy.exists()).toBeTruthy();
            expect(orderPlacedBy.text()).toBe('Managed By Seller');
        });
    });

});
