import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import Portal from 'portal-vue';
import {i18n} from 'common/i18n';

import coreDataDefault from 'common/testtools/scenariosstore/coreDataDefault.json';
import productFilterData from 'common/testtools/scenariosstore/productFilterData.json';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import ProductFilter from 'shop/products/components/ProductFilter';
import HeaderImage from 'common/images/boxed/application-store-header.svg';
import vuetify from 'common/plugins/brands/azena/vuetify';

Vue.use(Portal);

window.frontendData.coreData = coreDataDefault;

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    data: productFilterData,
    components: {
        TestPageRoot,
        ProductFilter,
        HeaderImage
    },
    methods: {},
    template: `
      <test-page-root>
        <div style="padding: 60px;">
          <product-filter :facets="facets"
                          :current-country="currentCountry"
                          :user-company-id="userCompanyId"
                          :init-facets="[]">
          </product-filter>
        </div>
      </test-page-root>
    `
});
