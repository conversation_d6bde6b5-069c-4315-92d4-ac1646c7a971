<template>
  <div class="sidebar">

    <v-row>

      <v-col cols="12" v-if="!!($slots && $slots['default'])">
        <div class="mb-4">
          <slot></slot>
        </div>
      </v-col>

      <v-col cols="12" md="6" xl="12" class="pb-sm-0 pb-md-4 pb-xl-0">
        <div class="sidebar-item mb-4" data-id="text-version">
          <h4>{{$t('shop.productDetails.sidebar.version')}}</h4>
          <p>{{versionName}}</p>
        </div>
        <div class="sidebar-item mb-4" data-id="text-productid">
          <h4>{{$t('shop.productDetails.sidebar.productId')}}</h4>
          <p>{{appCode}}</p>
        </div>
        <div class="sidebar-item mb-4" data-id="text-legalcompanyname">
          <h4>{{$t('shop.productDetails.sidebar.legalCompanyName')}}</h4>
          <p>{{company.name}}</p>
        </div>
        <template v-if="displayCountryAvailability">
          <div class="sidebar-item mb-4" v-if="countryList" data-id="text-countrylist">
            <h4>{{$t('shop.productDetails.sidebar.countries.title')}}</h4>
            <p>{{countryList}}</p>
          </div>
        </template>
      </v-col>

      <v-col cols="12" md="6" xl="12" class="pt-sm-0 pt-md-4 pt-xl-0">
        <template v-if="!userPermissionService.isAnonymous()">
          <div class="sidebar-item mb-4" v-if="appContactInfo.productWebsiteUrl" data-id="text-companywebsite">
            <h4>{{$t('shop.productDetails.sidebar.companyWebsite')}}</h4>
            <a class="product-website-url" v-bind:href="appContactInfo.productWebsiteUrl"
               target="_blank">{{appContactInfo.productWebsiteUrl}}</a>
          </div>
        </template>

        <div v-if="isThirdPartyApp" class="sidebar-item mb-4" data-id="text-privacypolicy-app">
          <h4>{{ $t('shop.productDetails.sidebar.privacyPolicyApp.title') }}</h4>
          <p>{{ $t('shop.productDetails.sidebar.privacyPolicyApp.description') }}</p>
          <a class="privacy-policy" v-bind:href="appContactInfo.privacyPolicyUrl" target="_blank">
            {{ $t('shop.productDetails.sidebar.privacyPolicyApp.link') }}
          </a>
        </div>

        <div v-else class="sidebar-item mb-4" data-id="text-privacypolicy-tool">
          <h4>{{ $t('shop.productDetails.sidebar.privacyPolicyTool.title') }}</h4>
          <p>{{ $t('shop.productDetails.sidebar.privacyPolicyTool.description') }}</p>
          <a class="privacy-policy" v-bind:href="appContactInfo.privacyPolicyUrl" target="_blank">
            {{ $t('shop.productDetails.sidebar.privacyPolicyTool.link') }}
          </a>
        </div>

        <div v-if="isThirdPartyApp" class="sidebar-item mb-4 terms-of-use" data-id="text-termsofuse-app">
          <h4>{{ $t('shop.productDetails.sidebar.termsOfUseApp.title') }}</h4>
          <a class="eula-url" v-bind:href="eulaUrl" target="_blank">
            {{ $t('shop.productDetails.sidebar.termsOfUseApp.link') }}
        <CDChip v-if="termsType === EulaType.STANDARD"
                outlined x-small
                class="chip-standard-eula"
                data-id="chip-standard-eula">
              {{ $t('shop.productDetails.sidebar.termsOfUseApp.standardMark') }}
        </CDChip>
          </a>
          <a v-if="hasAppendix" class="appendix-url" v-bind:href="appendixUrl" target="_blank">
            {{ $t('shop.productDetails.sidebar.termsOfUseApp.appendix') }}
          </a>
        </div>

        <div v-else class="sidebar-item mb-4 terms-of-use" data-id="text-termsofuse-tool">
          <h4>{{ $t('shop.productDetails.sidebar.termsOfUseTool.title') }}</h4>
          <a class="eula-url" v-bind:href="eulaUrl" target="_blank">
            {{ $t('shop.productDetails.sidebar.termsOfUseTool.link') }}
        <CDChip
                v-if="termsType === EulaType.STANDARD"
                outlined x-small
                class="chip-standard-eula"
                data-id="chip-standard-eula">
              {{ $t('shop.productDetails.sidebar.termsOfUseTool.standardMark') }}
        </CDChip>
          </a>
          <a v-if="hasAppendix" class="appendix-url" v-bind:href="appendixUrl" target="_blank">
            {{ $t('shop.productDetails.sidebar.termsOfUseTool.appendix') }}
          </a>
        </div>
      </v-col>

    </v-row>
  </div>
</template>

<script setup lang="ts">
import { AppContactData, commercefacades, EulaData, EulaType, IotCompanyData } from 'common/generated-types/types';
import { userPermissionService } from 'common/services';
import { computed, ComputedRef } from 'vue';

const props = defineProps<{
  appCode: string,
  versionName: string,
  company: IotCompanyData,
  appContactInfo: AppContactData,
  enabledCountries: Array<commercefacades.CountryData>,
  isThirdPartyApp: boolean,
  isPrivateApp: boolean
}>();

const STANDARD_EULA_URL = `https://legal.azena.com/assets/pdf/EULA_for_App%20Developers_Azena.pdf`;

const countryList: ComputedRef<string | undefined> = computed(() => {
    return props.enabledCountries!.map(country => country.name).sort().join(', ');
});

const eulaUrl: ComputedRef<string> = computed(() => {
  const eulaData: EulaData = props.appContactInfo!.eula;
  if (eulaData.type === EulaType.CUSTOM) {
    return eulaData.customUrl;
  }
  return STANDARD_EULA_URL;
});

const appendixUrl: ComputedRef<string> = computed(() => {
  return hasAppendix!.value ? props.appContactInfo!.eula.standardEulaAppendix.url : '';
});

const termsType: ComputedRef<EulaType> = computed(() => {
  return props.appContactInfo!.eula.type;
});

const displayCountryAvailability: ComputedRef<boolean> = computed(() => {
  return !props.isPrivateApp;
});

const hasAppendix: ComputedRef<boolean> = computed(() => {
  const eulaData = props.appContactInfo!.eula;
  return eulaData.type === EulaType.STANDARD &&
      eulaData.standardEulaAppendix !== null &&
      eulaData.standardEulaAppendix.url !== null;
});

defineExpose({
  countryList,
  hasAppendix,
  appendixUrl,
  eulaUrl,
  termsType,
  displayCountryAvailability,
  STANDARD_EULA_URL
});

</script>
