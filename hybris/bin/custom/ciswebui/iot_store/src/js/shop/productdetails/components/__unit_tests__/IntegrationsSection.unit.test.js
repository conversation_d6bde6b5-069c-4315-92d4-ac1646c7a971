import {mount} from '@vue/test-utils';
import IntegrationsSection from 'shop/productdetails/components/IntegrationsSection'
import {AppIntegrationType, StandardAppIntegrationType} from "../../../../common/generated-types/types";
import {cloneDeep} from "lodash";
import vuetify from 'common/plugins/brands/azena/vuetify';

jest.mock('common/util');

const propsData = {
    appIntegrations: [
        {
            type: AppIntegrationType.CUSTOM,
            integrationType: null,
            externalDescription: 'custom',
            displayName: 'Custom',
            documentation: null
        },
        {
            type: AppIntegrationType.STANDARD,
            integrationType: StandardAppIntegrationType.STANDARD,
            externalDescription: 'standard',
            displayName: 'Standard',
            documentation: ''
        },
        {
            type: AppIntegrationType.CUSTOM,
            integrationType: null,
            externalDescription: 'custom',
            displayName: 'Custom'
        }
    ],
    appContactInfo: {
        emailAddress: '<EMAIL>'
    }
};

const mountIntegrationSection = props => mount(IntegrationsSection, {
    mocks: {
        $t: (word) => {
            return word;
        }
    },
    propsData: props,
    vuetify
});

describe('IntegrationsSection', () => {

    let props;

    beforeEach(() => {
        props = cloneDeep(propsData);

        window.frontendData = {
            coreData: {}
        };
    });

    it('counts integrations properly', async () => {
        const wrapper = mountIntegrationSection(props);
        await wrapper.vm.$nextTick();
        expect(wrapper.findAll('.integration-item').wrappers).toHaveLength(4);
        expect(wrapper.findAll('.integration-item-static').wrappers).toHaveLength(1);
    });

    it('renders standard integrations first', async () => {
        const wrapper = mountIntegrationSection(props);
        await wrapper.vm.$nextTick();
        const integrationItems = wrapper.findAll('.integration-item');
        expect(integrationItems.wrappers[0].find('h3').text()).toEqual('Standard');
        expect(integrationItems.wrappers[1].find('h3').text()).toEqual('Custom');
        expect(integrationItems.wrappers[2].find('h3').text()).toEqual('Custom');
    });

    it('places correct icon', async () => {
        props.appIntegrations = [
            {
                type: AppIntegrationType.STANDARD,
                integrationType: StandardAppIntegrationType.STANDARD,
                displayName: 'Standard',
                externalDescription: 'Description'
            },
            {
                type: AppIntegrationType.STANDARD,
                integrationType: StandardAppIntegrationType.GATEWAY,
                displayName: 'Standard',
                externalDescription: 'Description'
            },
            {
                type: AppIntegrationType.STANDARD,
                integrationType: null,
                displayName: 'Standard',
                externalDescription: 'Description'
            },
            {
                type: AppIntegrationType.STANDARD,
                integrationType: '',
                displayName: 'Standard',
                externalDescription: 'Description'
            },
            {
                type: AppIntegrationType.STANDARD,
                displayName: 'Standard',
                externalDescription: 'Description'
            }
        ];

        const wrapper = mountIntegrationSection(props);
        await wrapper.vm.$nextTick();
        const integrationItems = wrapper.findAll('.integration-item');
        expect(integrationItems.wrappers[0].find('v-icon').exists()).toBeTruthy();
        expect(integrationItems.wrappers[0].find('v-icon').element.textContent).toContain('$certificate');
        expect(integrationItems.wrappers[1].find('v-icon').element.textContent).toContain('$gateway');
        expect(integrationItems.wrappers[2].find('v-icon').element.textContent).toContain('$doublesquares');
        expect(integrationItems.wrappers[3].find('v-icon').element.textContent).toContain('$doublesquares');
        expect(integrationItems.wrappers[4].find('v-icon').element.textContent).toContain('$doublesquares');
    });

    it('shows PDF link', async () => {
        props.appIntegrations = [
            {
                type: AppIntegrationType.STANDARD,
                displayName: 'standard with documentation',
                externalDescription: '',
                documentation: {
                    size: 44005,
                    url: 'https://console.securityandsafetythings.com/media/mypdf'
                }
            }
        ];

        const wrapper = mountIntegrationSection(props);
        await wrapper.vm.$nextTick();
        const integrationItem = wrapper.findAll('.integration-item').wrappers[0];
        expect(integrationItem.find('h3').text()).toEqual('standard with documentation');
        expect(integrationItem.find('v-btn').attributes('href')).toEqual('https://console.securityandsafetythings.com/media/mypdf');
        expect(integrationItem.find('.integration-download').text()).toContain('$download');
    });

})
