import {mount} from '@vue/test-utils';
import ChangelogSection from '../ChangelogSection';

describe('ChangelogSection', () => {

    const wrapper = mount(ChangelogSection, {
        mocks: {
            $t: () => {
            },
            $d: () => {
            }
        },
        propsData: {
            changelogs: [
                {
                    version: '1.2.10',
                    description: '<p>This should be a real long description of the new version:</p><p><br /></p><ul><li>Did a lot</li><li>Did a really a lot</li><li>How much did I do</li><li>One more</li><li>And one more<ul><li>A sub item also</li><li>one more</li></ul></li><li>And back</li></ul><p><br /></p>',
                    creationDate: 1573221522673
                },
                {
                    version: '1.2.9',
                    description: 'Test test test',
                    creationDate: 1573220862435
                }
            ]
        }
    });

    it('simple existence test', () => {
        expect(wrapper.exists()).toBe(true);
    });

    it('has two histories and first one with list', () => {
        const all = wrapper.findAll('.rich-text');
        expect(all.length).toBe(2);
        expect(all.at(0).find('ul').exists()).toBe(true);
        expect(wrapper.find('.more-button').exists()).toBeTruthy();
    });
});
