import util from 'common/util';
import {AxiosResponse} from 'axios';

let axios = util.axios;

const basePath: string = '/shop/api/integrator/current';

function getPath(appCode: string): string {
    return `${basePath}/${appCode}/subscription`;
}

let followAppSubscriptionResource = {
    updateFollowAppSubscriptionForCurrent(appCode: string, followApp: boolean): Promise<AxiosResponse<void>> {
        const path = getPath(appCode);
        const followAppSubscriptionRequest = {
            followApp: followApp
        };
        return axios.post(path, followAppSubscriptionRequest);
    }
};

export default followAppSubscriptionResource;
