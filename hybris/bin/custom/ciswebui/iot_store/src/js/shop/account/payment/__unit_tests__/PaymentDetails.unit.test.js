import {shallowMount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import PaymentDetails from '../PaymentDetails.vue';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';
import 'common/test-directive';
import {messageService} from 'common/services';
import {paymentResource} from 'shop/resources';
import {testMap} from 'common/testtools/scenariosstore';
import {cloneDeep} from 'lodash';
import {PaymentMethodType} from 'common/generated-types/types';
import {i18n} from 'common/i18n';

jest.mock('common/util');
jest.mock('shop/resources/paymentResource');
jest.mock('common/services/companyPermissionService');
jest.mock('axios');

Vue.use(VueI18n);

const createWrapper = (options) => {
    return shallowMount(PaymentDetails, {
        mocks: {
            $t: () => {
                return 'Message'
            }
        },
        i18n,
        vuetify,
        propsData: {
            pageData: options.pageData,
            coreData: options.coreData
        }
    });
};

describe('PaymentDetailsPage bank payment info distincted per payment method', () => {

    let data;
    beforeEach(() => {
        data = cloneDeep(testMap.PaymentDetails);
    });

    it('sepa case', async () => {
        const numberOfSepaBefore = data.pageData.invoicePaymentInfos
            .filter(i => i.paymentMethod === PaymentMethodType.SEPA_CREDIT).length;
        expect(numberOfSepaBefore > 1).toBeTruthy();

        const wrapper = createWrapper(data);

        const numberOfSepaAfter = wrapper.vm.invoicePaymentInfos.length;
        expect(numberOfSepaAfter === 1).toBeTruthy();
    });

    it('ach case', async () => {
        data.pageData.invoicePaymentInfos = data.pageData.invoicePaymentInfos.map(i => {
            if (i.paymentMethod === PaymentMethodType.SEPA_CREDIT) {
                i.paymentMethod = PaymentMethodType.ACH_INTERNATIONAL;
            }
            return i;
        });
        const numberOfAchBefore = data.pageData.invoicePaymentInfos
            .filter(i => i.paymentMethod === PaymentMethodType.ACH_INTERNATIONAL).length;
        expect(numberOfAchBefore > 1).toBeTruthy();

        const wrapper = createWrapper(data);

        const numberOfAchAfter = wrapper.vm.invoicePaymentInfos.length;
        expect(numberOfAchAfter === 1).toBeTruthy();
    });

    it('both sepa and ach case', async () => {
        data.pageData.invoicePaymentInfos.push({
            "id": "***************",
            "paymentMethod": PaymentMethodType.ACH_INTERNATIONAL,
            "saved": true,
            "reusable": false,
            "defaultPaymentInfo": false,
            "paymentProvider": "DPG",
            "enabled": true,
            "disableReason": null,
            "accountHolder": "Security & Safety Things GmbH",
            "iban": "**********************",
            "bankName": "First Ferengi Interplanetary",
            "bic": "BYLADEM1001"
        });
        data.pageData.invoicePaymentInfos.push({
            "id": "*************",
            "paymentMethod": PaymentMethodType.ACH_INTERNATIONAL,
            "saved": true,
            "reusable": false,
            "defaultPaymentInfo": false,
            "paymentProvider": "DPG",
            "enabled": true,
            "disableReason": null,
            "accountHolder": "Security & Safety Things GmbH",
            "iban": "**********************",
            "bankName": "First Ferengi Interplanetary",
            "bic": "BYLADEM1001"
        });
        const numberOfSepaBefore = data.pageData.invoicePaymentInfos
            .filter(i => i.paymentMethod === PaymentMethodType.SEPA_CREDIT).length;
        expect(numberOfSepaBefore > 1).toBeTruthy();
        const numberOfAchBefore = data.pageData.invoicePaymentInfos
            .filter(i => i.paymentMethod === PaymentMethodType.ACH_INTERNATIONAL).length;
        expect(numberOfAchBefore > 1).toBeTruthy();

        const wrapper = createWrapper(data);

        expect(wrapper.vm.invoicePaymentInfos.length === 2).toBeTruthy();
        const numberOfSepaAfter = wrapper.vm.invoicePaymentInfos
            .filter(i => i.paymentMethod === PaymentMethodType.ACH_INTERNATIONAL).length;
        expect(numberOfSepaAfter === 1).toBeTruthy();

        const numberOfAchAfter = wrapper.vm.invoicePaymentInfos
            .filter(i => i.paymentMethod === PaymentMethodType.ACH_INTERNATIONAL).length;
        expect(numberOfAchAfter === 1).toBeTruthy();
    });
});

describe('PaymentDetailsPage payment deleted', () => {

    let data;
    beforeEach(() => {
        data = cloneDeep(testMap.PaymentDetails);
    });

    it('success case', async () => {
        let mockData = 'testData';
        const deletePaymentMethodMock = paymentResource.deletePaymentMethod.mockImplementation(() => Promise.resolve({
            data: mockData
        }));

        const wrapper = createWrapper(data);
        let messageToUser = wrapper.find('.success > info-box');

        let globalMessages = [];
        messageService.subscribe(globalMessages);

        await wrapper.vm.confirmDeletePaymentInfo(123);
        await wrapper.vm.findInfos(123);

        await wrapper.vm.onConfirm();
        expect(deletePaymentMethodMock).toBeCalledWith(123);
        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('success');
    });

    it('failure case', async () => {
        const deletePaymentMethodMock = paymentResource.deletePaymentMethod.mockImplementation(() => Promise.reject({
            error: {
                data: {
                    message: 'error'
                }
            }
        }));

        const wrapper = createWrapper(data);
        let messageToUser = wrapper.find('.danger > info-box');

        let globalMessages = [];
        messageService.subscribe(globalMessages);

        await wrapper.vm.confirmDeletePaymentInfo(123);
        await wrapper.vm.findInfos(123);

        await wrapper.vm.onConfirm();
        await new Promise(process.nextTick);

        expect(deletePaymentMethodMock).toBeCalledWith(123);
        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('error');
    });

});


describe('PaymentDetailsPage payment set to default', () => {

    let data;
    beforeEach(() => {
        data = cloneDeep(testMap.PaymentDetails);
    });

    it('success case', async () => {
        let mockData = 'testData';
        const setDefaultPaymentMethodMock = paymentResource.setDefaultPaymentMethod.mockImplementation(() => Promise.resolve({
            data: mockData
        }));

        const wrapper = createWrapper(data);
        let messageToUser = wrapper.find('.success > info-box');

        let globalMessages = [];
        messageService.subscribe(globalMessages);

        await wrapper.vm.setDefault(456);

        expect(setDefaultPaymentMethodMock).toBeCalledWith(456);
        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('success');
    });

    it('failure case', async () => {
        const setDefaultPaymentMethodMock = paymentResource.setDefaultPaymentMethod.mockImplementation(() => Promise.reject({
            message: 'error'
        }));

        const wrapper = createWrapper(data);
        let messageToUser = wrapper.find('.danger > info-box');

        let globalMessages = [];
        messageService.subscribe(globalMessages);

        await wrapper.vm.setDefault(456);
        await new Promise(process.nextTick);

        expect(setDefaultPaymentMethodMock).toBeCalledWith(456);
        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('error');
    });

});

describe('PaymentDetailsPage payment removed as default', () => {

    let data;
    beforeEach(() => {
        data = cloneDeep(testMap.PaymentDetails);
    });

    it('success case', async () => {
        let mockData = 'testData';
        const removeEntryMock = paymentResource.removeDefaultPaymentMethod.mockImplementation(() => Promise.resolve({
            data: mockData
        }));

        const wrapper = createWrapper(data);
        let messageToUser = wrapper.find('.success > info-box');

        let globalMessages = [];
        messageService.subscribe(globalMessages);

        await wrapper.vm.removeDefault(456);

        expect(removeEntryMock).toBeCalledWith(456);
        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('success');
    });

    it('failure case', async () => {
        const removeEntryMock = paymentResource.removeDefaultPaymentMethod.mockImplementation(() => Promise.reject({
            message: 'error'
        }));

        const wrapper = createWrapper(data);
        let messageToUser = wrapper.find('.danger > info-box');

        let globalMessages = [];
        messageService.subscribe(globalMessages);

        await wrapper.vm.removeDefault(456);
        await new Promise(process.nextTick);

        expect(removeEntryMock).toBeCalledWith(456);
        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('error');
    });

});
