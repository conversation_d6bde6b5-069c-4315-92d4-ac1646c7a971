import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {CartItemBundleInfo} from 'shop/core/components/cartitembundleinfo';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';

jest.mock('common/util');


const mountCartItemBundleInfo = (bundleInfo) => wrapperComponentFactory(CartItemBundleInfo, {
    props: {
        bundleInfo: bundleInfo
    },
    mocks: {}
});


describe('CartItemBundleInfo', () => {
    describe('Bundle info', () => {
        it('should be displayed when bundle available', async () => {
            const wrapper = await mountCartItemBundleInfo({
                code: 'bundle_code',
                name: 'S',
                size: 1
            });
            await new Promise(process.nextTick);

            const bundleInfo = wrapper.find('[data-id="text-bundle-info"]');
            expect(bundleInfo.text()).toEqual('S (1 license)');
        });

        it('should be correctly pluralized when bundle with size bigger than 1', async () => {
            const wrapper = await mountCartItemBundleInfo({
                code: 'bundle_code',
                name: 'M',
                size: 5
            });
            await new Promise(process.nextTick);

            const bundleInfo = wrapper.find('[data-id="text-bundle-info"]');
            expect(bundleInfo.text()).toEqual('M (5 licenses)');
        });

        it('should not be displayed when bundle not available', async () => {
            const wrapper = await mountCartItemBundleInfo(null);
            await new Promise(process.nextTick);

            const bundleInfo = wrapper.find('[data-id="text-bundle-info"]');
            expect(bundleInfo.exists()).toBeFalsy();
        });
    });

});
