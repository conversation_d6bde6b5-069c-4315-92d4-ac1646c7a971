import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import ToolEnabledPopup from 'shop/productdetails/components/ToolEnabledPopup';

Vue.use(VueI18n);

window.frontendData = {
    coreData: {
        navigationItems: [
            {
                'id': 'globalSupport',
                'itemCode': 'globalSupport',
            }
        ],
    }
};

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        ToolEnabledPopup
    },
    data: {
        show: true,
        productName: 'Queue Management'
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <tool-enabled-popup :show="show"
                              :product-name="productName">
          </tool-enabled-popup>
        </div>
      </test-page-root>
    `
});
