import 'common/testtools/unit_tests_mock'
import ProductFilter from '../ProductFilter'
import productFilterData from 'common/testtools/scenariosstore/productFilterData.json'
import productFilterPrivateData from 'common/testtools/scenariosstore/productFilterPrivateData.json'
import 'common/test-directive'
import { cloneDeep } from 'lodash'
import { FacetGroup } from '../../types'
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils'

jest.mock('common/util')
jest.mock('axios')

const mountProductFilter = (options) => wrapperComponentFactory(ProductFilter, {
    shallow: false,
    props: {
        facets: options.facets,
        currentCountry: options.currentCountry,
        userCompanyId: options.userCompanyId,
        initFacets: options.initFacets
    },
    mocks: {},
})

const defaultCoreData = {
    currentLanguage: 'en',
    moduleConfig: {
        DIRECT_SALES: false
    }
}

describe('ProductFilter', () => {
    let data
    beforeEach(() => {
        data = cloneDeep(productFilterData)
        window.frontendData = {
            coreData: defaultCoreData
        }
    })

    it('facet is not selected when no initial facets set', () => {

        let facetItemExpected = {
            count: 92,
            facetIndex: 'industries',
            facetValue: 'Other',
            name: 'Other'
        }

        data.initFacets = []
        const wrapper = mountProductFilter(data)

        let isFacetSelected = wrapper.vm.isFacetSelected(facetItemExpected)
        expect(isFacetSelected).toBeFalsy()
    })

    it('facet is selected when correct initial facets set', () => {
        let facetItem = {
            count: 92,
            facetIndex: 'industries',
            facetValue: 'Other',
            name: 'Other'
        }

        data.initFacets = [facetItem]

        const wrapper = mountProductFilter(data)

        let isFacetSelected = wrapper.vm.isFacetSelected(facetItem)
        expect(isFacetSelected).toBeTruthy()
    })

    it('Prepared Facets to match private', () => {
        data = cloneDeep(productFilterPrivateData)

        const wrapper = mountProductFilter(data)
        const preparedExpectedFacets = [
            {
                'group': 'Availability',
                'orderIndex': 1,
                'items': [
                    {
                        'name': 'Available in your country',
                        'facetIndex': 'countries',
                        'facetValue': 'DE',
                        'count': 92
                    },
                    {
                        'name': 'Trial',
                        'facetIndex': 'licenseTypes',
                        'facetValue': 'EVALUATION',
                        'count': 92
                    },
                    {
                        'name': 'Your private apps',
                        'facetIndex': 'privateApps',
                        'facetValue': 'a7293bd2-203d-4589-bde6-0373101d8126',
                        'count': 1
                    }
                ]
            },
            {
                'group': 'useCases',
                'orderIndex': 4,
                'items': [
                    {
                        'name': 'Other',
                        'facetIndex': 'useCases',
                        'facetValue': 'Other',
                        'count': 92
                    }
                ]
            },
            {
                'group': 'industries',
                'orderIndex': 5,
                'items': [
                    {
                        'name': 'Other',
                        'facetIndex': 'industries',
                        'facetValue': 'Other',
                        'count': 92
                    }
                ]
            }
        ]

        let preparedActualFacets = wrapper.vm.preparedFacets
        expect(preparedActualFacets).toEqual(preparedExpectedFacets)
    })

    it('Prepared Facets to contain only filter facets', () => {
        data = cloneDeep(productFilterData)

        const wrapper = mountProductFilter(data)

        let preparedFacets = wrapper.vm.preparedFacets
        let preparedFacetsGroups = preparedFacets.map(facet => facet.group)
        expect(preparedFacetsGroups).toEqual(
          expect.arrayContaining([
                'Availability',
                FacetGroup.INDUSTRIES.toString(),
                FacetGroup.USE_CASES.toString()
            ]
          )
        )
        expect(preparedFacetsGroups).toEqual(
          expect.not.arrayContaining([
                FacetGroup.COMPANY_UID.toString(),
            ]
          )
        )
    })

    it('activeGroups computed property is populated based upon data.initFacets', () => {
        data.initFacets = [
            { 'name': 'Metro', 'facetIndex': 'industries', 'facetValue': 'Metro', 'count': 1 },
            { 'name': 'Health & safety', 'facetIndex': 'useCases', 'facetValue': 'Health & safety', 'count': 1 }
        ]
        const wrapper = mountProductFilter(data)
        expect(wrapper.vm.activeGroups.length).toBe(2)
    })

    it('if only useCases in activeGroups, industries should contain 8 items and have a showMore button', () => {
        data.initFacets = [
            { 'name': 'Health & safety', 'facetIndex': 'useCases', 'facetValue': 'Health & safety', 'count': 1 }
        ]
        const wrapper = mountProductFilter(data)
        const industriesList = wrapper.find('ul.industries')

        // Doesn't test anything (and would fail if it did)
        expect(industriesList.find('a').exists()).toBeTruthy()
    })

    describe('filterFacetGroups', () => {
        it('given direct sales FT disabled, then include licenses facet group', () => {
            window.frontendData.coreData.moduleConfig.DIRECT_SALES = false
            const wrapper = mountProductFilter(data)

            const availabilityFacetGroups = wrapper.vm.filterFacetGroups

            expect(availabilityFacetGroups).toContain(FacetGroup.LICENSES.toString())
            expect(availabilityFacetGroups).not.toContain(FacetGroup.LICENSE_RUNTIMES.toString())
        })

        it('given direct sales FT enabled, then include license runtimes facet group', () => {
            window.frontendData.coreData.moduleConfig.DIRECT_SALES = true
            const wrapper = mountProductFilter(data)

            const availabilityFacetGroups = wrapper.vm.filterFacetGroups

            expect(availabilityFacetGroups).toContain(FacetGroup.LICENSE_RUNTIMES.toString())
            expect(availabilityFacetGroups).not.toContain(FacetGroup.LICENSES.toString())
        })

        it('given direct sales FT disabled, then include package facet group', () => {
            window.frontendData.coreData.moduleConfig.DIRECT_SALES = false
            const wrapper = mountProductFilter(data)

            const availabilityFacetGroups = wrapper.vm.filterFacetGroups

            expect(availabilityFacetGroups).toContain(FacetGroup.PACKAGES.toString())
            expect(availabilityFacetGroups).not.toContain(FacetGroup.FIRST_LEVEL_CATEGORIES.toString())
        })

        it('given direct sales FT enabled, then include category facet group', () => {
            window.frontendData.coreData.moduleConfig.DIRECT_SALES = true
            const wrapper = mountProductFilter(data)

            const availabilityFacetGroups = wrapper.vm.filterFacetGroups

            expect(availabilityFacetGroups).toContain(FacetGroup.FIRST_LEVEL_CATEGORIES.toString())
            expect(availabilityFacetGroups).not.toContain(FacetGroup.PACKAGES.toString())
        })
    })

    describe('getValueLabel', () => {
        it('given translatable facet then return translated label', () => {
            const wrapper = mountProductFilter(data)

            const labelForKnownKey = wrapper.vm.getValueLabel('runtime_subs_unlimited', FacetGroup.LICENSE_RUNTIMES.toString())

            expect(labelForKnownKey).toBe('Subscription')
        })

        it('given non translatable facet then return label', () => {
            const wrapper = mountProductFilter(data)

            const label = wrapper.vm.getValueLabel('Some Label', FacetGroup.VEHICLE_TYPES.toString())

            expect(label).toBe('Some Label')
        })
    })

})
