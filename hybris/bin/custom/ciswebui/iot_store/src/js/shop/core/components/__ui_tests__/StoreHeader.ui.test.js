import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from 'common/testtools/inlineTestPageCreator';
import * as Sizes from 'common/testtools/testScreenSizes';
import {testMap} from 'common/testtools/scenariosstore';
import {cloneDeep} from 'lodash';

describe('StoreHeader', () => {

    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.Cart);
    });

    it('has items in the cart', async () => {
        data.coreData.numberOfCartItems = 5;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'pixel',
            customSnapshotIdentifier: 'with 5 items in cart'
        });
    });

    it('tests the storeheader across all breakpoints', async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testExtraLargeScreensize(1000);
        await Sizes.testLargeScreensize(1000);
        await Sizes.testMediumScreensize(1000);
        await Sizes.testSmallScreensize(1000);
    });

    it('has correct design for interaction', async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.setViewport({width: 1440, height: 1000});

        await page.hover('.cd-icon-home');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'pixel',
            customSnapshotIdentifier: 'hovering on home'
        });

        await page.hover('[data-id="button-flyout"]');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'pixel',
            customSnapshotIdentifier: 'hovering on user icon'
        });


        await page.hover('[data-id="link-cart-page"]');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'pixel',
            customSnapshotIdentifier: 'hovering on cart icon'
        });

        await page.click('[data-id="button-flyout"]');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'pixel',
            customSnapshotIdentifier: 'flyout menu open'
        });

        await page.hover('a.v-list-item');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'pixel',
            customSnapshotIdentifier: 'hovering on flyout menu item'
        });

        await page.click('[data-id="button-help-flyout"]');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'pixel',
            customSnapshotIdentifier: 'help flyout menu open'
        });
    });
});
