<template>
    <div class="support">
        <v-container :fluid="true" class="pa-0">
            <v-container :fluid="$vuetify.breakpoint.lgAndDown">
                <v-row>
                    <v-col cols="12">
                        <h2 class="label">{{$t('shop.productDetails.supportSection.title')}}</h2>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" md="4" lg="3" v-if="supportData.supportPhoneNumber">
                        <div class="phone support-element">
                            <div class="support-header d-flex align-center">
                                <CDIcon class="mr-2" color="black" icon="$call"></CDIcon>
                                <h4>{{$t('shop.productDetails.supportSection.phone')}}</h4>
                            </div>
                            <a class="mt-2" v-bind:href="`tel: ${supportData.supportPhoneNumber}`">{{supportData.supportPhoneNumber}}</a>
                        </div>
                    </v-col>
                    <v-col cols="12" md="4" lg="3">
                        <div class="email support-element">
                            <div class="support-header d-flex align-center">
                                <CDIcon class="mr-2" color="black" icon="$letter"></CDIcon>
                                <h4>{{$t('shop.productDetails.supportSection.email')}}</h4>
                            </div>
                            <a class="mt-2" v-bind:href="`mailto:${supportData.emailAddress}`">{{supportData.emailAddress}}</a>
                        </div>
                    </v-col>
                    <v-col cols="12" md="4" lg="3">
                        <div class="website support-element" v-if="supportData.supportPageUrl">
                            <div class="support-header d-flex align-center">
                                <CDIcon class="mr-2" color="black" icon="$globe"></CDIcon>
                                <h4>{{$t('shop.productDetails.supportSection.website')}}</h4>
                            </div>
                            <a class="mt-2" v-bind:href="supportData.supportPageUrl" target="_blank">
                                {{supportData.supportPageUrl}}
                            </a>
                        </div>
                    </v-col>
                </v-row>
            </v-container>
        </v-container>
    </div>
</template>

<script lang="ts">
    import {Component, Vue, Prop} from 'vue-property-decorator';
    import {AppContactData} from 'common/generated-types/types';

    @Component
    export default class SupportSection extends Vue {
        @Prop() supportData!: AppContactData;
    }
</script>

<style scoped>
    h4 {
        margin-bottom: 0;
    }
</style>
