import util from 'common/util';
import {AxiosResponse} from 'axios';
import {LicenseActivationData, LicenseActivationStatus} from 'common/generated-types/types';

let axios = util.axios;

const basePath: string = '/shop/api/company/current/license';

function getPath(licenseCode: string): string {
    return `${basePath}/${licenseCode}/activation`;
}

let licenseActivationResource = {
    getLicenseActivationStatusForCurrent(licenseCode: string): Promise<AxiosResponse<LicenseActivationData>> {
        const path = getPath(licenseCode);
        return axios.get(path);
    },

    setLicenseActivationStatusForCurrent(licenseCode: string, status: LicenseActivationStatus): Promise<AxiosResponse<void>> {
        const path = getPath(licenseCode);
        const activationStatusRequest = {
            activationStatus: status
        };
        return axios.patch(path, activationStatusRequest);
    }
};

export default licenseActivationResource;