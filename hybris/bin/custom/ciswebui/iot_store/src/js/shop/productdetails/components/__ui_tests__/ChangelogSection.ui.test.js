describe("ChangelogSection", () => {

    it("Lots of content should be cut off in display and include showmore button", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-ChangelogSection');
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("not expanded");

        await page.click(".more-button");
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("expanded");

        await page.click(".more-button");
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("not expanded");
    });

});
