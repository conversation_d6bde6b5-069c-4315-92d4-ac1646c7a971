<template>
    <div class="license-details">
        <ul class="wrap-list">
            <li
                v-for="detail in tm(`shop.productDetails.pricingSection.license.${licenseCode.toLowerCase()}.detailsMap`)"
                v-bind:key="detail">
                <span>{{detail}}</span>
            </li>
        </ul>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {useI18n} from 'vue-i18n-bridge';

    @Component
    export default class LicenseDetails extends Vue {
        @Prop() licenseCode!: string;

        tm(code: string) {
          const { tm } = useI18n();
          return Object.fromEntries(Object.entries(tm(code)).sort());
        }
    }
</script>

<style lang="scss">
@import "common/utilities";
</style>
