import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import { ChangelogSection } from 'shop/productdetails/components';
import { TransformExternalLinks } from 'common/directives';

Vue.directive('transformExternalLinks', TransformExternalLinks);
Vue.use(VueI18n);

const changelogData = [
    {
        version: '1.2.10',
        description: '<p>This should be a real long description of the new version:</p><p><br /></p><ul><li>Did a lot</li><li>Did a really a lot</li><li>How much did I do</li><li>One more</li><li>And one more<ol><li>A sub item also</li><li>one more</li></ol></li><li>And back</li></ul><p><br /></p>',
        creationDate: 1573221522673
    },
    {
        version: '1.2.9',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.8',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.7',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.6',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.5',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.4',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.3',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.2',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.1',
        description: 'Test test test',
        creationDate: 1573220862435
    },
    {
        version: '1.2.0',
        description: 'Test test test',
        creationDate: 1573220862435
    }
    ];

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    data: {
        changelogs: changelogData
    },
    components: {
        TestPageRoot,
        ChangelogSection,
    },
    methods: {},
    template: `
      <test-page-root>
        <div style="padding: 60px;">
          <changelog-section
              v-bind:changelogs="changelogs">
          </changelog-section>
        </div>
      </test-page-root>
    `
});
