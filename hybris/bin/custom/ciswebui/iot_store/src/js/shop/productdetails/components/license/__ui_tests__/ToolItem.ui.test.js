describe("ToolItem", () => {
    it('Toggle Enable S&ST Tool toggle displays Tool Enabled Popup', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-license-__ui_tests__-ToolItem');
        await page.waitFor(1000);
        await page.click('[data-id="toggle-get-free-tool"]');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('Tool Enabled Popup displayed');
    });
});
