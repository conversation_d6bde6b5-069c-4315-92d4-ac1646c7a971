<template>
  <CDBlock>
    <div v-if="paymentAddress && !isOwnAppPurchase">
      <h4 class="header-address">{{ $t('shop.checkout.miniSummary.headerAddress') }}</h4>
      <p>{{ paymentAddress.companyName }}</p>
      <p class="address">
        {{ paymentAddress.line1 }} {{ paymentAddress.line2 }},
        {{ paymentAddress.postalCode }} {{ paymentAddress.town }},
        {{ $t(`country.${paymentAddress.country.isocode}`) }}
      </p>
    </div>
    <div v-if="paymentAddress && isOwnAppPurchase">
      <h4 class="header-address">{{ $t('shop.checkout.miniSummary.ownPurchaseHeader') }}</h4>
      <p>{{ paymentAddress.email }}</p>
    </div>
    <div class="price-container">
      <div class="mb-2" v-if="companyName">
        <h5 class="header-company-name">{{ $t('shop.cart.orderFrom') }}{{ companyName }}</h5>
      </div>
      <div class="total-excl single-line">
        <h4 class="header-price">{{ $t('shop.checkout.miniSummary.totalPrice') }}</h4>
        <div class="price d-flex flex-row font-weight-bold" v-if="!isOwnAppPurchase">
          <div>
            <span v-if="showOriginalPrice" class="original-price mr-2">
              {{ originalPrice.symbol }} {{ $n(i18nService.stringToDefaultNumericFormat(originalPrice.value), 'price', localeForFormats) }}
            </span>
            <h4>
              {{ price.symbol }} {{ $n(i18nService.stringToDefaultNumericFormat(price.value), 'price', localeForFormats) }}
            </h4>
          </div>
        </div>
        <h4 class="price text-right" v-if="isOwnAppPurchase">{{ $t('shop.checkout.ownApp.feeNote') }}</h4>
      </div>
      <div class="taxes single-line">
        <div class="estimated-tax-container" v-if="paymentMethod === PaymentMethodType.INVOICE_BY_SELLER">
          <p class="estimated-tax" id="taxNoteInvoiceBySeller">
            {{ $t('shop.checkout.miniSummary.taxNoteInvoiceBySeller') }}
          </p>
        </div>
        <div class="estimated-tax-container" v-else-if="displayTaxNote">
          <p class="estimated-tax" id="taxNoteInfo">
            {{ $t('shop.checkout.miniSummary.taxNote') }}
          </p>
          <tooltip v-if="paymentMethod === PaymentMethodType.CREDIT_CARD"
                   id="creditcardTooltipWarning"
                   class="info-hint"
                   v-bind:text="$t('shop.checkout.miniSummary.taxHint')">
            <icon-warning></icon-warning>
          </tooltip>
        </div>
      </div>
      <div v-if="isOwnAppPurchase">
        <p class="info--text">
          <CDIcon class="mr-2" color="info">$statusinfo</CDIcon>
          {{ $t('shop.cartItem.ownApp.info') }}
        </p>
      </div>
      <div v-if="displayEulas">
        <CDCheckbox
            v-model="eulaAccepted"
            @click="$emit('onEulaAcceptedClicked', eulaAccepted)"
            color="primary"
            data-id="checkbox-accept-eula"
            :label="$t('shop.cartItem.eulaAcceptanceText')">
        </CDCheckbox>
        <v-row no-gutters>
          <v-col cols="12"
                 v-for="(eula, index) in countryEulas"
                 :key="`eula-${index}`">
            <a :href="eula.url"
               class="ml-8"
               :data-id="`link-eula-${index}`"
               target="_blank">{{ eula.label }}</a>
          </v-col>
        </v-row>
      </div>
      <div class="slot-box">
        <slot></slot>
      </div>
    </div>
  </CDBlock>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {commercefacades, CountryEulaData, PaymentMethodType, PriceData} from 'common/generated-types/types';
import IconWarning from 'common/images/boxed/icon-16-info-b.svg';
import Tooltip from 'common/components/Tooltip.vue';
import {i18nService} from 'common/services';
import AddressData = commercefacades.AddressData;

@Component({
  components: {
    IconWarning,
    Tooltip,
  }
})
export default class MiniSummary extends Vue {
  @Prop() paymentAddress!: AddressData;
  @Prop() price!: PriceData;
  @Prop({ default: null, required: false }) originalPrice!: PriceData | null;
  @Prop() paymentMethod!: PaymentMethodType;
  @Prop() displayTaxNote!: boolean;
  @Prop() companyName!: string;
  @Prop() isOwnAppPurchase!: boolean;
  @Prop() countryEulas!: CountryEulaData[];

  PaymentMethodType = PaymentMethodType;

  eulaAccepted = false;

  $refs!: {
    taxInfo: HTMLElement;
    tooltip: HTMLElement;
  };

  localeForFormats = '';

  mounted(): void {
    this.localeForFormats = i18nService.getLocaleForFormats();
  }

  get i18nService() {
    return i18nService;
  }

  get displayEulas(): boolean {
    return this.countryEulas && this.countryEulas.length > 0;
  }

  get showOriginalPrice(): boolean {
    return !!this.originalPrice && this.originalPrice.value !== this.price.value;
  }
}

</script>

<style lang="scss" scoped>
@import 'shop/core/constants';

p {
  &:empty {
    display: none;
  }
}

.single-line {
  display: flex;
  justify-content: space-between;
  align-items: baseline;

  .original-price {
    color: var(--v-grey-darken2);
    text-decoration: line-through;
  }
}

.header-address, .header-price {
  padding-bottom: $spacing-s;
}

.header-company-name {
  font-size: 14px;
  line-height: 130%;
  letter-spacing: 0.2px;
}

.address {
  padding-bottom: 35px;
}

.total {
  padding-top: 20px;
}

.estimated-tax-container {
  display: flex;

  .estimated-tax {
    position: relative;

    svg {
      position: absolute;
      margin-left: 5px;
      width: 25px;
      top: -2px;
      cursor: pointer;
    }
  }

  .tooltip-container {
    margin-left: $spacing-xs;
  }
}

.split-line {
  width: 100%;
  height: 1px;
  border: 1px solid var(--v-grey-lighten4);
}

.slot-box {
  padding-top: 45px;
  display: flex;
  justify-content: flex-end;
}

.tooltip {
  display: none;
  border-radius: 6px;
  box-shadow: 0 2px 8px 0 rgba(176, 192, 237, 0.2);
  border: solid 1px #ededed;
  background-color: white;
  max-width: 245px;
  position: absolute;
  opacity: 0;
  bottom: 50px;
  z-index: 1000;

  .tooltip-inner {
    color: var(--v-grey-darken2);
    padding: 5px;
    text-align: center;
    overflow-wrap: break-word;
  }
}

.foreground {
  z-index: 100;
}
</style>
