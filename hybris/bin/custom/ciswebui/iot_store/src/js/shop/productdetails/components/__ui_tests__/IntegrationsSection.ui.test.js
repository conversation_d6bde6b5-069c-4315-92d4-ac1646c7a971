describe("IntegrationsSection", () => {
    it("has correct views", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-IntegrationsSection');

        await page.setViewport({width: 1440, height: 1200});
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("integrations on 'XL' viewport");

        await page.setViewport({width: 576, height: 1200});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("integrations on 'S' viewport");

    });
});
