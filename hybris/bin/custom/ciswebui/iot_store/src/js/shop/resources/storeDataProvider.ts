import type {CoreData, DevconData, GalleryItemData} from 'common/generated-types/types';

interface StoreFrontendData {
    coreData: CoreData;
    pageData: DevconData;
    galleryItems: GalleryItemData[];
}

class StoreDataProvider {

    private static get galleryItems() {
        if (window.frontendData.pageData) {
            return window.frontendData.pageData.galleryItems || {};
        } else {
            return {};
        }
    }

    static get pageData() {
        return window.frontendData.pageData || {};
    }

    static get coreData() {
        return window.frontendData.coreData || {};
    }

    static getMergedFrontendData() {
        return {
            coreData: StoreDataProvider.coreData,
            galleryItems: StoreDataProvider.galleryItems,
            pageData: StoreDataProvider.pageData || {}
        } as StoreFrontendData;
    }
}

export {StoreDataProvider, type StoreFrontendData};
