import {mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import {FollowAppChangesSection} from '../index';
import {LicenseType} from '../../../../common/types';
import vuetify from 'common/plugins/brands/azena/vuetify';

jest.mock('common/util');
const translationMap = {
    'shop.productDetails.followAppSection.app.label': 'Follow app',
    'shop.productDetails.followAppSection.tool.label': 'Follow tool',
    'shop.productDetails.followAppSection.app.summary': 'Get email notifications about app updates.',
    'shop.productDetails.followAppSection.tool.summary': 'Get email notifications about tool updates.'
}

const mountFollowAppChanges = () => mount(FollowAppChangesSection, {
    mocks: {
        $t: (key) => {return translationMap[key]}
    },
    vuetify,
    propsData: {}
});
const appCode = 'A_12345'
const expectedApp = { label:'Follow app', summary:'Get email notifications about app updates.'}
const expectedTool = { label:'Follow tool', summary:'Get email notifications about tool updates.'}

describe('FollowAppChangesSection', () => {
    beforeEach(() => {
        window.frontendData = {
            pageData: {
                code: appCode,
                name: 'test app',
                followAppData: {
                    subscribed:false
                }
            }
        };
    });
    function assertFollowAppComponent(wrapper,expected) {
        expect(wrapper.find('.follow-app-changes').exists()).toBe(true);
        expect(wrapper.find('v-switch').attributes('label')).toEqual(expected.label);
        expect(wrapper.find('#summary').text()).toEqual(expected.summary);
    }

    it('No FollowApp Information -> Follow App checkbox is unchecked.', async () => {
        const wrapper = mountFollowAppChanges();
        expect(wrapper.vm.subscribed).toBeFalsy();
        assertFollowAppComponent(wrapper,expectedApp);
    });

    it('With FollowAppSubscribe true -> Follow App checkbox is checked.', async () => {
        window.frontendData.pageData.followAppData.subscribed = true;
        const wrapper = mountFollowAppChanges();
        expect(wrapper.vm.subscribed).toBeTruthy();
        assertFollowAppComponent(wrapper,expectedApp);
    });

    it('No Follow tool Information -> Follow tool checkbox is unchecked.', async () => {
        window.frontendData.pageData.licenses=[{code: appCode + '_tool',
            name: '',
            price: 0,
            licenseType: {
                code : LicenseType.TOOL
            }
        }];
        const wrapper = mountFollowAppChanges();
        expect(wrapper.vm.subscribed).toBeFalsy();
        assertFollowAppComponent(wrapper,expectedTool);
    });

    it('With FollowAppSubscribe true -> Follow tool checkbox is checked.', async () => {
        window.frontendData.pageData.followAppData.subscribed=true;
        window.frontendData.pageData.licenses=[{code: appCode + '_tool',
            name: '',
            price: 0,
            licenseType: {
             code : LicenseType.TOOL
            }
        }];
        const wrapper = mountFollowAppChanges();
        expect(wrapper.vm.subscribed).toBeTruthy();
        assertFollowAppComponent(wrapper,expectedTool);
    });
});
