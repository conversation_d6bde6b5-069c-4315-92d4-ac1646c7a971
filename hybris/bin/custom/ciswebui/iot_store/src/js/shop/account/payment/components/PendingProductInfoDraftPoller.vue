<template>
  <div data-id="container-pending-product-info-poller">
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>

import {PaymentInfoDraftData} from 'common/generated-types/types';
import {computed, onMounted} from 'vue';
import {paymentResource} from 'shop/resources';
import {messageService} from 'common/services';
import {useI18n} from 'vue-i18n-bridge';
import {pageSpinner} from 'common/components/spinner';

const props = defineProps<{
  pendingPaymentInfoDrafts: PaymentInfoDraftData[]
}>();

const {t} = useI18n();

onMounted(() => {
  if (props.pendingPaymentInfoDrafts.length > 0) {
    scheduleRequestToFinalizePaymentInfos();
  }
});

const pendingProductInfoDraftsIds = computed(() => {
  return props.pendingPaymentInfoDrafts.map(infoDraft => infoDraft.code);
});

const scheduleRequestToFinalizePaymentInfos = () => {
  messageService.info(t('shop.payment.waitForPaymentInfoConfirmation'), false);
  setTimeout(() => sendRequest(), 5000);
};


const sendRequest = async () => {
  pageSpinner.start();
  paymentResource.finalizePendingPaymentInfosCreation(pendingProductInfoDraftsIds.value)
      .then(response => {
        const {paymentInfoDraftsToRetry} = response.data;
        if (paymentInfoDraftsToRetry.length > 0) {
          scheduleRequestToFinalizePaymentInfos();
        } else {
          window.location.reload();
        }
      })
      .catch(error => {
        if (error.response && error.response.status === 400 && error.response.data) {
          messageService.error(t(error.response.data) as string);
        } else {
          messageService.error(t('shop.error.backend') as string);
        }
      })
      .finally(() => pageSpinner.stop());
};

</script>
