describe("PricingSection", () => {

  let styleContent;

  beforeEach(() => {
    styleContent = `input {
                      caret-color: transparent !important;
                    }`;
  });

  it("looks as expected in initial state", async () => {
    await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-PricingSection');

    await page.setViewport({width: 1440, height: 700});
    await page.waitFor(400);
    expect(await page.screenshot()).toMatchImageSnapshot("PricingSection on 'XL' viewport");

    await page.setViewport({width: 576, height: 700});
    await page.waitFor(600);
    expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("PricingSection on 'S' viewport");

  });

  it("looks as expected quantity exceeds state", async () => {
    await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-PricingSection');

    await page.addStyleTag({ content: styleContent });

    await page.setViewport({width: 1440, height: 700});
    await page.waitFor(400);
    expect(await page.screenshot()).toMatchImageSnapshot("PricingSection on 'XL' viewport");

    await page.waitFor(500);
    const input = await page.$('input[name="FULL"]');
    await page.keyboard.press('Backspace');
    await input.type('1234', {});

    await page.waitFor(500);
    expect(await page.screenshot()).toMatchImageSnapshot('edited quantity exceeds');
  });

  it("shows correct permission text when there are no published licenses", async () => {
    await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-PricingSection.permission.text.no.licenses');

    await page.addStyleTag( { content: styleContent } );

    await page.setViewport( {width: 1440, height: 700} );
    await page.waitFor(400);
    expect(await page.screenshot()).toMatchImageSnapshot("PricingSection on 'XL' viewport");
  });

  it("subscription with enabled sepa", async () => {
    await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-PricingSection.subscription');

    await page.addStyleTag({ content: styleContent });

    await page.setViewport({width: 1440, height: 700});
    await page.waitFor(400);
    expect(await page.screenshot()).toMatchImageSnapshot("PricingSection with subscription on 'XL' viewport");

    await page.setViewport({width: 576, height: 700});
    await page.waitFor(600);
    expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("PricingSection with subscription on 'S' viewport");
  });

  it("subscription with disabled sepa", async () => {
    await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-PricingSection.subscription.no.sepa');

    await page.addStyleTag({ content: styleContent });

    await page.setViewport({width: 1440, height: 700});
    await page.waitFor(400);
    expect(await page.screenshot()).toMatchImageSnapshot("PricingSection with subscription with no sepa on 'XL' viewport");

    await page.setViewport({width: 576, height: 700});
    await page.waitFor(600);
    expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("PricingSection with subscription with not sepa on 'S' viewport");
  });
});
