import {shallowMount} from '@vue/test-utils';
import StarRatingSelection from 'shop/core/components/starrating/StarRatingSelection';

jest.mock('common/util');

const createWrapper = () => {
    return shallowMount(StarRatingSelection, {
        propsData: {
            selectedRating: 0.0
        }
    });
};

describe('Check star selection', () => {

    it('Rating is changed when mouse is moved.', () => {
        const wrapper = createWrapper();

        let stars = wrapper.findAll(".star");

        stars.at(1).trigger("mousemove");
        expect(wrapper.vm.currentRating).toEqual(2.0);
        expect(wrapper.emitted()).toEqual({});

        stars.at(2).trigger("mousemove");
        expect(wrapper.vm.currentRating).toEqual(3.0);
        expect(wrapper.emitted()).toEqual({});

        stars.at(2).trigger("click");
        expect(wrapper.vm.currentRating).toEqual(3.0);
        expect(wrapper.emitted('ratingchanged')).toEqual([[3.0]]);

        stars.at(0).trigger("mousemove");
        expect(wrapper.vm.currentRating).toEqual(1.0);
        expect(wrapper.emitted('ratingchanged')).toHaveLength(1);

        stars.at(0).trigger("click");
        expect(wrapper.vm.currentRating).toEqual(1.0);
        expect(wrapper.emitted('ratingchanged')).toHaveLength(2);
        expect(wrapper.emitted('ratingchanged')[1]).toEqual([1.0]);
    });
});