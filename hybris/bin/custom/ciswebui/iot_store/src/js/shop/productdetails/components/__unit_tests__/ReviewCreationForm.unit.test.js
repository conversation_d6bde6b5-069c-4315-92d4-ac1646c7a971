import axios from 'axios';
import {mount, shallowMount} from '@vue/test-utils';
import ReviewCreationForm from 'shop/productdetails/components/ReviewCreationForm';
import flushPromises from 'flush-promises';
import util from 'common/util';

jest.mock('common/util');
jest.mock('axios');

const createWrapper = () => {
    return shallowMount(ReviewCreationForm, {
        mocks: {
                $t: () => {
            }
        },
        propsData: {
            postUrl: '/post/url'
        }
    });
};

function waitForMs(delay) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve();
        }, delay);
    });
}

const createMountedWrapper = () => {
    return mount(ReviewCreationForm, {
        mocks: {
            $t: () => {}
        },
        propsData: {
            postUrl: '/post/url'
        }
    });
};

describe('Check Axios post', () => {

    beforeEach(() => {
        util.axios.post.mockReset();
        util.axios.post.mockImplementation(() =>
            Promise.resolve({
                status: 204,
                statusText: 'OK',
            })
        );
    });

    it('Axios post is done with correct data and endpoint.',  async () => {
        const wrapper = createWrapper();

        wrapper.vm.reviewData.comment = 'testComment';
        wrapper.vm.reviewData.headline = 'testHeadline';
        wrapper.vm.reviewData.rating = 4.2;

        await wrapper.vm.submitReview();

        await flushPromises();

        expect(util.axios.post).toBeCalledWith('/post/url', {
            "alias": "",
            "comment": "testComment",
            "headline": "testHeadline",
            "rating": 4.2,
            "showCompany": false,
            "showName": false,
            "company": "",
            "date": "",
            "id": "",
            "principal": {
                "name": "",
                "uid": ""
            }
        });
    });
});

describe('Check rating propagation', () => {

    it('Rating is changed when event is fired.', () => {
        const wrapper = createMountedWrapper();

        let stars = wrapper.findAll(".star");
        stars.at(2).trigger("click");

        expect(wrapper.vm.reviewData.rating).toEqual(3);
    });
});

describe('Check validation', () => {
    const wrapper = createMountedWrapper();

    it('Validation if rating exists works.', async () => {
        wrapper.vm.reviewData.rating = 0;
        wrapper.vm.validateMandatoryFields();

        await flushPromises();

        const submitButton = wrapper.find('[data-id="button-review-submit"]');
        expect(submitButton.attributes('disabled')).toBeTruthy();

        let stars = wrapper.findAll(".star");
        stars.at(3).trigger("mousemove");
        stars.at(2).trigger("click");
        expect(wrapper.vm.reviewData.rating).toEqual(3);

        await waitForMs(1000);

        expect(submitButton.attributes('disabled')).toBeFalsy();
    });
});