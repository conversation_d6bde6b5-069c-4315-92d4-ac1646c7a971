<template>
    <v-container v-bind:class="{
          'bank-transfer' : getPaymentType(PaymentMethodType.SEPA_CREDIT) || getPaymentType(PaymentMethodType.ACH_INTERNATIONAL) || getPaymentType(PaymentMethodType.SEPA_DIRECTDEBIT),
          'payment-method-entry-container': getPaymentType(PaymentMethodType.CREDIT_CARD),
        }">

        <v-row v-if="newEntry && newEntryType === PaymentMethodType.CREDIT_CARD">
          <v-col cols="4" sm="3" md="2">
            <payment-method-icons class="mt-n2" paymentType="card"></payment-method-icons>
          </v-col>
          <v-col cols="8" md="6" class="new">
              <p class="mv-0 mr-3">{{$t('shop.checkout.newCard')}}</p>
          </v-col>
          <v-col cols="12" md="4" class="actions text-right">
            <payment-method-icons class="mt-n2" v-bind:all-cards="true"></payment-method-icons>
            <slot name="newcc"></slot>
          </v-col>
        </v-row>

        <v-row v-else-if="newEntry && getPaymentType(PaymentMethodType.SEPA_DIRECTDEBIT)">
          <v-col cols="12" sm="3" md="2">
            <payment-method-icons class="mt-n2" :paymentType="PaymentMethodType.SEPA_DIRECTDEBIT"></payment-method-icons>
          </v-col>
          <v-col cols="12" sm="7" md="8" data-id="sepa-mandate-info-new">
            <p class="mb-0">{{$t('shop.payment.sepaMandateNew')}}</p>
          </v-col>
        </v-row>

        <v-row v-else-if="getPaymentType(PaymentMethodType.SEPA_DIRECTDEBIT)">
          <v-col cols="12" sm="3" md="2">
            <payment-method-icons class="mt-n2" :paymentType="PaymentMethodType.SEPA_DIRECTDEBIT"></payment-method-icons>
          </v-col>
          <v-col cols="12" sm="7" md="8" data-id="sepa-mandate-info">
            <v-row>
              <v-col>
                <v-row>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="8">
                        <p class="mb-0">{{$t('shop.payment.sepaMandateForAccount')}} {{sepaDirectDebit.iban}}</p>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-row>
                          <v-col cols="5">
                            <span class="ml-8 ml-md-4 ml-sm-2" v-if="sepaDirectDebit.companyScope">
                              <v-tooltip bottom   max-width="400">
                                  <template v-slot:activator="{ on, attrs }">
                                      <CDIcon v-bind="attrs" v-on="on" dense >$company</CDIcon>
                                  </template>
                                  <span>{{ $t('shop.payment.tooltip.companyScope') }}</span>
                              </v-tooltip>
                            </span>
                          </v-col>
                          <v-col cols="7">
                            <status-tag v-bind:text="$t('default')"
                                        v-if="sepaDirectDebit.defaultPaymentInfo"
                                        class="ml-md-4 ml-sm-2 align-start justify-start">
                            </status-tag>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="ml-0" v-if="shouldShowDetails(sepaDirectDebit.id)">
                <v-row no-gutters v-if="sepaDirectDebit.iban">
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.iban')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small :data-id="`text-sepa-dd-payment-info-iban-${sepaDirectDebit.id}`">
                        {{sepaDirectDebit.iban}}
                      </small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters v-if="sepaDirectDebit.accountHolderName">
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.accountName')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small :data-id="`text-sepa-dd-payment-info-name-${sepaDirectDebit.id}`">
                        {{sepaDirectDebit.accountHolderName}}
                      </small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters v-if="sepaDirectDebit.mandateReference">
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.mandateReference')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small :data-id="`text-sepa-dd-payment-info-mandate-reference-${sepaDirectDebit.id}`">
                        {{sepaDirectDebit.mandateReference}}
                      </small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters v-if="sepaDirectDebit.dateOfSignature">
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.dateOfSignature')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small :data-id="`text-sepa-dd-payment-info-mandate-date-${sepaDirectDebit.id}`">
                        {{ $d(new Date(sepaDirectDebit.dateOfSignature), 'short') }}
                      </small>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" sm="2">
              <sepa-direct-debit-actions v-if="actions"
                                         data-id="`sepa-direct-debit-actions`"
                                         :sepaDirectDebit="sepaDirectDebit"
                                         :is-sepa-mandate-enabled="isSepaMandateEnabled"
                                         @payment-info-deleted="onPaymentInfoDeleted"
                                         @default-payment-info-set="onDefaultPaymentInfoSet"
                                         @default-payment-info-unset="onDefaultPaymentInfoRemoved"
                                         @payment-info-viewed="onPaymentInfoViewed">
              </sepa-direct-debit-actions>

          </v-col>
        </v-row>

        <v-row v-else-if="getPaymentType(PaymentMethodType.CREDIT_CARD)">
          <v-col cols="12" sm="3" md="2">
            <payment-method-icons class="mt-n2" v-if="creditCard.cardType" v-bind:paymentType="creditCard.cardType"></payment-method-icons>
            <payment-method-icons class="mt-n2" v-else paymentType="card"></payment-method-icons>
          </v-col>
          <v-col cols="12" sm="7" md="9" class="creditCard">
            <v-row class="justify-center justify-md-start">
              <v-col class="d-flex">
                  <span v-if="creditCard.cardTypeData">{{creditCard.cardTypeData.name}}</span>
                  <span class="">{{creditCard.cardNumber}}</span>
                  <status-tag class=""
                              v-bind:text="$t('default')"
                              v-if="creditCard.defaultPaymentInfo">
                  </status-tag>
              </v-col>
              <v-col>
                <p class="">{{creditCard.accountHolderName}}</p>
              </v-col>
              <v-col>
                <p class="">{{creditCard.expiryMonth}}/{{creditCard.expiryYear}}</p>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" sm="1" class="actions text-right" v-if="actions">
            <slot name="creditcard"></slot>
            <CDIcon class="delete-action" v-on:click="$emit('delete')" icon="$trash"></CDIcon>
          </v-col>
        </v-row>

        <v-row v-else-if="getPaymentType(PaymentMethodType.SEPA_CREDIT)">
          <v-col cols="12" sm="3" md="2">
            <payment-method-icons class="mt-n2" paymentType="bank"></payment-method-icons>
          </v-col>
          <v-col cols="12" sm="7" md="8" data-id="bank-info">
            <v-row>
              <v-col class="d-flex">
                <p class="mb-0">{{$t('shop.payment.sepaTransfer')}}</p>
                <status-tag v-bind:text="$t('default')"
                          v-if="sepaCredit.enabled && sepaCredit.defaultPaymentInfo"
                          class="ml-md-4 ml-sm-2 align-start justify-start">
                </status-tag>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="ml-0" v-if="shouldShowDetails(sepaCredit.id)">
                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.transferTo')}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters v-if="sepaCredit.accountHolder">
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.accountName')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{sepaCredit.accountHolder}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.bankName')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{sepaCredit.bankName}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.iban')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{sepaCredit.iban}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.bicSwift')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{sepaCredit.bic}}</small>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
        </v-row>

        <v-row v-else-if="getPaymentType(PaymentMethodType.ACH_INTERNATIONAL)">
          <v-col cols="12" sm="3" md="2">
            <payment-method-icons class="mt-n2" paymentType="bank"></payment-method-icons>
          </v-col>
          <v-col cols="12" sm="7" md="8" class="bank-infos">
            <v-row>
              <v-col class="d-flex">
                <p class="d-inline-block mb-0">{{$t('shop.payment.achInternational')}}</p>
                <status-tag v-bind:text="$t('default')"
                        v-if="achCredit.enabled && achCredit.defaultPaymentInfo"
                        class="ml-md-4 ml-sm-2 align-start justify-start">
                </status-tag>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="ml-0" v-if="shouldShowDetails(achCredit.id)">
                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.transferTo')}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters v-if="achCredit.accountHolder">
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.account')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{achCredit.accountHolder}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.bankName')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{achCredit.bankName}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.routing')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{achCredit.routingNumber}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.accountNumber')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{achCredit.accountNumber}}</small>
                    </v-card>
                  </v-col>
                </v-row>

                <v-row no-gutters>
                  <v-col cols="5" md="3" >
                    <v-card class="px-1" elevation="0">
                      <small>{{$t('shop.payment.bicSwift')}}</small>
                    </v-card>
                  </v-col>
                  <v-col cols="7" md="9">
                    <v-card class="px-1" elevation="0">
                      <small>{{achCredit.bic}}</small>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {
	AchCreditTransferPaymentInfoData,
	CreditCardPaymentInfoData, PaymentInfoData,
	PaymentMethodType,
	SepaCreditTransferPaymentInfoData,
	SepaMandatePaymentInfoData,
} from 'common/generated-types/types';
import {PaymentMethodIcons, StatusTag, Tooltip} from 'common/components';
import RemovePaymentOptionDialog from 'shop/account/payment/components/RemovePaymentOptionDialog.vue';
import SepaDirectDebitActions from 'shop/account/payment/components/SepaDirectDebitActions.vue';

@Component({
  computed: {
    PaymentMethodType() {
      return PaymentMethodType;
    }
  },
  components: {
    SepaDirectDebitActions,
    RemovePaymentOptionDialog,
    PaymentMethodIcons,
    StatusTag,
    Tooltip
  }
})
export default class PaymentMethodEntry extends Vue {
  @Prop() paymentData?: PaymentInfoData;
  @Prop({default: false}) actions!: boolean;
  @Prop({default: false}) newEntry!: boolean;
  @Prop() newEntryType?: PaymentMethodType;
  @Prop() currentPaymentMethodId!: string;
  @Prop({ default: false }) isSepaMandateEnabled!: boolean;

  getPaymentType(method: PaymentMethodType): boolean {
    if (this.newEntry) {
      return this.newEntryType === method;
    }
    return this.paymentData?.paymentMethod === method;
  }

  get creditCard() : CreditCardPaymentInfoData {
    return this.paymentData as CreditCardPaymentInfoData;
  }

  get sepaCredit() : SepaCreditTransferPaymentInfoData {
    return this.paymentData as SepaCreditTransferPaymentInfoData;
  }

  get achCredit() : AchCreditTransferPaymentInfoData {
    return this.paymentData as AchCreditTransferPaymentInfoData;
  }

  get sepaDirectDebit() : SepaMandatePaymentInfoData {
    return this.paymentData as SepaMandatePaymentInfoData;
  }

  shouldShowDetails(id: string): boolean {
    if (!this.currentPaymentMethodId) {
      return true;
    } else {
      return this.currentPaymentMethodId === id;
    }
  }

  onPaymentInfoDeleted(paymentInfoId: string): void {
    this.$emit('payment-info-deleted', paymentInfoId);
  }

  onDefaultPaymentInfoSet(paymentInfoId: string): void {
    this.$emit('default-payment-info-set', paymentInfoId);
  }

  onDefaultPaymentInfoRemoved(paymentInfoId: string): void {
    this.$emit('default-payment-info-unset', paymentInfoId);
  }

  onPaymentInfoViewed(paymentInfoId: string): void {
    if (this.isSepaMandateEnabled) {
      this.$emit('payment-info-viewed', paymentInfoId);
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
