import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import PortalVue from 'portal-vue';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import GallerySection from 'shop/productdetails/components/GallerySection';
import GallerySlideshow from 'shop/productdetails/components/GallerySlideshow';

Vue.use(VueI18n);
Vue.use(PortalVue);

new Vue({
   el: '#vue-app',
   i18n,
   vuetify,
   components: {
      TestPageRoot,
      GallerySection,
      GallerySlideshow
   },
   data: {
      zoomVisible: false,
      galleryItems: [{
         key: 0,
         url: '/sample-data/sample-screenshot.jpeg'
      }, {
         key: 1,
         url: '/sample-data/sample-screenshot-2.jpeg'
      }, {
         key: 2,
         url: '/sample-data/sample-screenshot.jpeg'
      }, {
         key: 3,
         url: '/sample-data/sample-screenshot-2.jpeg'
      }, {
         key: 4,
         url: '/sample-data/sample-screenshot.jpeg'
      }, {
         key: 5,
         url: '/sample-data/sample-screenshot-2.jpeg'
      }, {
         key: 6,
         url: '/sample-data/sample-screenshot.jpeg'
      }, {
         key: 7,
         url: '/sample-data/sample-screenshot-2.jpeg'
      }],
   },
   methods: {
      blurBackground(hasContent) {
         if (hasContent) {
            this.$refs.mainwrap.style.filter = 'blur(3px)';
         } else {
            this.$refs.mainwrap.style.filter = 'blur(0)';
         }
      }
   },
   template: `
    <test-page-root>
      <div ref="mainwrap">
       <div style="padding: 60px; max-width: 700px">
         <gallery-section v-bind:gallery-images="galleryItems"></gallery-section>
         <portal to="portal-store">
            <gallery-slideshow v-bind:gallery-images="galleryImages" v-on:closeZoom="closeZoom" v-bind:index="index" v-if="zoomVisible"></gallery-slideshow>
          </portal>
        </div>
      </div>
      <portal-target name="portal-store" v-on:change="blurBackground"></portal-target>
    </test-page-root>
  `
});
