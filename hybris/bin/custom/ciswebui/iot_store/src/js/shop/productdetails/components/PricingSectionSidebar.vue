<template>
    <div id="private-offer-request-container" v-if="privateOfferRequestAllowed">
        <div>
            <h2 class="mb-2 text-h3">{{$t('shop.productDetails.sidebar.privateOffer.title')}}</h2>
            <p>{{$t('shop.productDetails.sidebar.privateOffer.description')}}</p>
            <CDButton id="privateOfferRequest"
                      data-id="button-create-private-offer-request"
                      v-on:click="contactSeller()">
                {{$t('shop.productDetails.sidebar.privateOffer.button')}}
            </CDButton>
        </div>
        <private-offer-dialog
                v-bind:show="showModal"
                v-bind:product="product"
                v-on:modal-closed="showModal = false">
        </private-offer-dialog>
    </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DetailProductData} from 'common/generated-types/types';
import {productDataService} from 'common/services';
import PrivateOfferDialog from 'shop/productdetails/components/PrivateOfferDialog.vue';

@Component({
    components: {PrivateOfferDialog}
})
export default class PricingSectionSidebar extends Vue {
    @Prop() product!: DetailProductData;

    showModal = false;

    contactSeller(): void {
        this.showModal = true;
    }

    get privateOfferRequestAllowed(): boolean {
        return !productDataService.isToolApp(this.product);
    }
}
</script>
