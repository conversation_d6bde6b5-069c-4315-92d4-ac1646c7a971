import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';
import appCompanyProfileResource from 'shop/resources/appCompanyProfileResource';
import companyProfileDataEmpty from 'common/testtools/scenariosstore/companyProfileDataEmpty.json';
import companyProfileData from 'common/testtools/scenariosstore/companyProfileData.json';
import CompanyProfileSection from 'shop/productdetails/components/CompanyProfileSection';

jest.mock('common/util');
jest.mock('shop/resources/appCompanyProfileResource');

Vue.use(VueI18n);

const appCode = 'A_10112233';
const sellerProductId = '1 687 P15 100';
const mountCompanyProfileSection = () => mount(CompanyProfileSection, {
    mocks: {
        $t: () => {
        },
    },
    i18n,
    vuetify,
    propsData: {
        appCode: appCode,
        sellerProductId: sellerProductId
    }
});

describe('CompanyProfileSection', () => {

    it('company profile section is not displayed when no company profile info', async () => {
        appCompanyProfileResource.getCompanyProfile.mockImplementation(() => Promise.resolve({
            status: 200,
            data: companyProfileDataEmpty
        }));
        const wrapper = mountCompanyProfileSection();
        await wrapper.vm.$nextTick();

        expect(wrapper.find('[data-id="container-company-profile"]').exists()).toBeFalsy();

        expect(appCompanyProfileResource.getCompanyProfile).toHaveBeenCalledWith(appCode);
    });
    it('company profile section is displayed when apps company has  company profile info', async () => {
        appCompanyProfileResource.getCompanyProfile.mockImplementation(() => Promise.resolve({
            status: 200,
            data: companyProfileData
        }));
        const wrapper = mountCompanyProfileSection();
        await wrapper.vm.$nextTick();

        expect(wrapper.find('[data-id="container-company-profile"]').exists()).toBeTruthy();

        expect(appCompanyProfileResource.getCompanyProfile).toHaveBeenCalledWith(appCode);
    });

});
