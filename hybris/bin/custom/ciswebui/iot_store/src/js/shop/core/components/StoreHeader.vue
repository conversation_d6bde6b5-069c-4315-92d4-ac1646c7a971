<template>
    <div class="shop">

        <CDNavbar app :title="pageLabel" :home-link="baseUrl">

            <template v-slot:content-center>
              <CDNavbarLink v-if="hasAppsLink" :href="appsLink.url" :isActive="appsActive" data-id="link-category-apps">
                {{translateNavigation(appsLink)}}
              </CDNavbarLink>
              <CDNavbarLink v-if="hasToolsLink" :href="toolsLink.url" :isActive="toolsActive" data-id="link-category-tools">
                {{translateNavigation(toolsLink)}}
              </CDNavbarLink>
            </template>

            <template v-slot:content-end>
                <template v-if="userPermissionService.isAnonymous()">
                    <div v-if="loginEntry">
                        <CDButtonText id="login-header-link" :href="loginEntry.url" class="mr-2">{{ translateNavigation(loginEntry) }}</CDButtonText>
                        <CDButtonText :href="registerEntry.url" class="mr-4">{{ translateNavigation(registerEntry) }}</CDButtonText>
                        <help-flyout></help-flyout>
                    </div>
                </template>

                <template v-else>
                    <CDHomeSwitcher
                        :v-if="dmtNavigationItem"
                        :is-developer="false"
                        :is-integrator="true"
                        :dropdown-links="homeSwitcher"
                        :z-index="200">
                    </CDHomeSwitcher>
                    <CDSeparator></CDSeparator>
                    <a :href="cartPage" class="mr-2 icon-cart" data-id="link-cart-page">
                        <CDIcon icon="$cart"
                                color="black"
                                data-id="icon-cart"
                                :badge-content="totalQuantity > 0 ? totalQuantity : false"
                                :badge-value="totalQuantity > 0 ? true : false"
                                :badge-dot="totalQuantity > 0 ? false : true">
                        </CDIcon>
                    </a>
                    <help-flyout></help-flyout>
                    <CDFlyoutMenu data-id="flyout" class="icon-user" :header="CDFlyoutHeader" :z-index="200" nudge-bottom="8" nudge-right="38">
                        <CDFlyoutMenuItem v-for="item in authenticatedDesktopMenu" :key="item.id"
                            :title="item.text"
                            :href="item.href"
                            :target="item.target"
                            :icon="item.icon"
                            :has-divider="false">
                        </CDFlyoutMenuItem>
                    </CDFlyoutMenu>
                </template>
            </template>

            <template v-slot:content-end-mobile>
                <template v-if="userPermissionService.isAnonymous()">
                    <CDFlyoutMenu
                        v-bind="FlyoutMenuFormatting"
                        :hasIcons="false"
                        menuIcon="$user">
                        <CDFlyoutMenuItem v-for="item in anonymousMobileMenu" :key="item.text"
                                          :title="item.text"
                                          :href="item.href"
                                          :has-divider="item.text === 'Apps' || item.id === 'storeHelpAndResources'">
                        </CDFlyoutMenuItem>
                    </CDFlyoutMenu>
                </template>

                <template v-else>
                    <CDButtonIcon :v-if="dmtNavigationItem" icon="$home" :href="dmtNavigationItem.url" color="black"></CDButtonIcon>
                    <CDSeparator></CDSeparator>
                    <a :href="cartPage" class="mr-4">
                        <CDIcon icon="$cart"
                                color="black"
                                :badge-content="totalQuantity > 0 ? totalQuantity : false"
                                :badge-value="totalQuantity > 0 ? true : false"
                                :badge-dot="totalQuantity > 0 ? false : true">
                        </CDIcon>
                    </a>
                    <CDFlyoutMenu v-bind="FlyoutMenuFormatting" :header="CDFlyoutHeader" :z-index="200">
                        <CDFlyoutMenuItem v-if="hasAppsLink" :href="appsLink.url" :title="appsLink.text"></CDFlyoutMenuItem>
                        <CDFlyoutMenuItem v-if="hasToolsLink" :href="toolsLink.url" :title="toolsLink.text"></CDFlyoutMenuItem>
                        <CDFlyoutMenuItem v-for="item in authenticatedMobileMenu" :key="item.id"
                                          :title="item.text"
                                          :href="item.href"
                                          :target="item.target"
                                          :icon="item.icon"
                                          :has-divider="item.text === 'My Profile' ">
                        </CDFlyoutMenuItem>
                    </CDFlyoutMenu>
                </template>
            </template>

        </CDNavbar>

    </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {FadeTransition} from 'common/components';
import {NavigationItemData, NavigationItemGroup, NavigationItemType} from 'common/generated-types/types';
import {CDNavigationItem, HomeSwitcherItems} from 'common/types';
import {navigationService, userPermissionService} from 'common/services';
import {isNil, sortBy} from 'lodash';
import {CategoriesHeader} from 'shop/core/components/productcategories';
import HelpFlyout from 'shop/core/components/helpflyout/HelpFlyout.vue';
import {useCartStore} from 'shop/store/cart';

    @Component({
        components: {
            HelpFlyout,
            FadeTransition,
            CategoriesHeader
        }
    })
    export default class StoreHeader extends Vue {
        @Prop() camerasUrl!: string;
        @Prop() myAppsUrl!: string;
        @Prop() baseUrl!: string;
        @Prop() userName!: string;
        @Prop() numberOfCartItems!: number;
        @Prop() pageTitle!: string;
        @Prop() homeTitle!: string;

        cartStore = useCartStore();

        pageLabel = '';
        homeLabel = '';
        devconLabel = '';
        userPermissionService = userPermissionService;
        navigationService = navigationService;

        mounted(): void {
            this.pageLabel = this.pageTitle || this.$t('navigation.storeTitle') as string;
            this.homeLabel = this.homeTitle || this.$t('navigation.deviceManagementPortal') as string;
            this.devconLabel = this.devconLabel || this.$t('navigation.devconTitle') as string;
            this.cartStore.incrementQuantity(this.numberOfCartItems);
        }

        CDFlyoutHeader = {
            text: this.userName
        };

        FlyoutMenuFormatting = {
            left: false,
            nudgeTop: -8,
            zIndex: 200
        };

        translateNavigation(navigationItem : NavigationItemData) : string {
            return navigationService.translate(this.$i18n, navigationItem);
        }

        get homeSwitcher (): HomeSwitcherItems {
            return {
                devcon: {
                    title: this.devconLabel,
                    href: this.camerasUrl,
                    key: 'devcon'
                },
                dmt: {
                    title: this.dmtNavigationItem.text,
                    href: this.dmtNavigationItem.url,
                    key: 'dmt'
                }
            };
        }

        get headerEntries(): CDNavigationItem[] {
            return sortBy(this.navigationService.byGroup(NavigationItemGroup.HEADER), 'index')
                .map(item => ({
                    id: item.id,
                    text: item.text,
                    icon: this.getIconForGlobalNavItem(item),
                    href: item.url,
                    target: item.target,
                    highlight: false,
                    index: item.index
                }));
        }

      /**
       * Ensures that Global Navigation Items icon names are prefixed with the '$' sign.
       * Global Navigation Items are used by the Store, Devcon and KPI.
       * Both the Devcon and KPI still use the io-components flyout, which expects io-components compatible icon names.
       *
       * @param navigationItem
       */
        getIconForGlobalNavItem(navigationItem: NavigationItemData): string {
            if (navigationItem.type !== NavigationItemType.GLOBAL) {
              return navigationItem.icon;
            }
            // Cd-system icon names are prefixed with a '$' sign.
            if (!navigationItem.icon?.startsWith('$')) {
              return '$' + navigationItem.icon;
            }
            return navigationItem.icon;
        }

        get productCategories(): CDNavigationItem[] {
            return sortBy(this.navigationService.byGroup(NavigationItemGroup.PRODUCT_CATEGORIES),'index')
                .filter(item => !isNil(item))
                .map(item => ({
                    id: item.id,
                    text: item.text,
                    href: item.url,
                    target: item.target,
                    highlight: false,
                    index: item.index
                }));
        }

        get mobileMenuHelpItem(): CDNavigationItem {
          const item = this.navigationService.byItemCode('storeHelpAndResources');
          return item && {
            id: item.id,
            text: item.text,
            icon: item.icon,
            href: item.url,
            target: item.target,
            highlight: false,
            index: item.index
          };
        }

        get cartPage(): string {
            return this.baseUrl + 'cart';
        }

        get loginEntry(): NavigationItemData { return this.navigationService.byItemCode('storeLogin'); }

        get registerEntry(): NavigationItemData { return this.navigationService.byItemCode('storeRegister'); }

        get dmtNavigationItem(): NavigationItemData { return this.navigationService.byItemCode('deviceManagement'); }

        get appsLink(): NavigationItemData { return this.navigationService.byItemCode('appProductCategory'); }

        get toolsLink(): NavigationItemData { return this.navigationService.byItemCode('toolProductCategory'); }

        get appsActive(): boolean {
            return this.navigationService.appsActive(window.location.href) || this.$route.meta?.appsActive === true;
        }

        get toolsActive(): boolean {
            return this.navigationService.toolsActive(window.location.href) || this.$route.meta?.toolsActive === true;
        }

        get hasAppsLink(): boolean {
          return !!this.appsLink?.url && !!this.appsLink?.text;
        }

        get hasToolsLink(): boolean {
          return !!this.toolsLink?.url && !!this.toolsLink?.text;
        }

        get anonymousMobileMenu(): CDNavigationItem[] {
          const headerEntries = this.headerEntries.filter(item => item.id !== 'storeHelpAndResources');
            return [
                ...headerEntries,
                ...this.productCategories,
                this.mobileMenuHelpItem
            ];
        }

        get authenticatedMobileMenu(): CDNavigationItem[] {
          const authenticatedHeaderEntries = this.headerEntries.filter(item => item.id !== 'globalSupport');
          return sortBy(authenticatedHeaderEntries, 'index');
        }

        get authenticatedDesktopMenu(): CDNavigationItem[] {
          const authenticatedHeaderEntries = this.headerEntries.filter(item => item.id !== 'storeHelpAndResources');
          return sortBy(authenticatedHeaderEntries, 'index');
        }

        get totalQuantity() : number {
          return this.cartStore.totalQuantity;
        }
    }
</script>

<style lang="scss" scoped>
    @import 'cd-system/tokens/scss/variables';
    :deep(.home-switcher-label) {
        font-family: $body-font-family !important;
    }
</style>
