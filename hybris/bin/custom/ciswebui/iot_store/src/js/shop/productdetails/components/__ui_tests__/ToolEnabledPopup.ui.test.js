describe("ToolEnabledPopup", () => {
    it("looks as expected", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-ToolEnabledPopup');
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: 1440, height: 700});
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("XL");

        await page.setViewport({width: 576, height: 700});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("popup is hidden on mobile devices (blank screenshot)");
    });
});