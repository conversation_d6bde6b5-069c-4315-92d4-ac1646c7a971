import {mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import CompanyInfo from 'shop/productdetails/components/companyprofile/CompanyInfo';
import 'common/test-directive';
import {cloneDeep} from 'lodash';

jest.mock('common/util');

const refPropsData = {
    headerData: {
        tagLine: 'Making the best apps since 2010',
        showFounded: true,
        foundedIn: 2010,
        companyWebsite: 'https://www.testcompany.com',
        linkedInProfileUrl: null,
        showCompanySize: true,
        companySize: {
            code: 'SMALL',
            value: '51-200 employees',
        },
        country: 'Germany',
        city: 'Munich'
    },
    companyName: 'Test Company'
};

const mountCompanyInfo = (props) => mount(CompanyInfo, {
    mocks: {
        $t: () => {},
    },
    propsData: {
        headerData: props.headerData,
        companyName: props.companyName,
        profileUrl: props.profileUrl
    }
});

describe(' CompanyInfo of Company Profile Section', () => {

    let propsData = {};
    beforeEach(() => {
        propsData = cloneDeep(refPropsData);
    });

    it('displays founded data', async () => {
        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.showFoundedIn).toBeTruthy();
        expect(wrapper.find('[data-id="container-company-founded"]').exists()).toBeTruthy();
    });

    it('does not display founded data when showFounded is false', async () => {
        propsData.headerData.showFounded = false;

        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.showFoundedIn).toBeFalsy();
        expect(wrapper.find('[data-id="container-company-founded"]').exists()).toBeFalsy();
    });

    it('does not display founded data when not available', async () => {
        propsData.headerData.foundedIn = null;

        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.showFoundedIn).toBeFalsy();
        expect(wrapper.find('[data-id="container-company-founded"]').exists()).toBeFalsy();
    });

    it('displays company size', async () => {
        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.showCompanySize).toBeTruthy();
        expect(wrapper.find('[data-id="container-company-size"]').exists()).toBeTruthy();
    });

    it('does not display company size when showCompanySize is false', async () => {
        propsData.headerData.showCompanySize = false;

        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.showCompanySize).toBeFalsy();
        expect(wrapper.find('[data-id="container-company-size"]').exists()).toBeFalsy();
    });

    it('does not display company size when  not available', async () => {
        propsData.headerData.companySize = null;

        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.showCompanySize).toBeFalsy();
        expect(wrapper.find('[data-id="container-company-size"]').exists()).toBeFalsy();
    });

    it('creates headquarters with separator between city and country when both available', async () => {
        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.headquarters).toEqual('Munich, Germany');
    });

    it('does not add separator to headquarters when only country is available', async () => {
        propsData.headerData.city = null;
        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.headquarters).toEqual('Germany');
    });

    it('does not add separator to headquarters when only city is available', async () => {
        propsData.headerData.country = null;
        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.vm.headquarters).toEqual('Munich');
    });

    it('does not show visit profile button when profile url is not available', async () => {
        propsData.profileUrl = null;
        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.find('[data-id="button-profile-url"]').exists()).toBeFalsy();
    });

    it('shows visit profile button when profile url is given', async () => {
        propsData.profileUrl = '/company-profile/1232232323';
        const wrapper = mountCompanyInfo(propsData);

        expect(wrapper.find('[data-id="button-profile-url"]').exists()).toBeTruthy();
    });
});
