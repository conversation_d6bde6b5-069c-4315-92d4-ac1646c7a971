import {acceptCookieBanner, createInlineUiTestPage} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';

describe('PrivateOfferDialog', () => {

    it('Open Dialog', async () => {
        await createInlineUiTestPage(testMap.ProductDetails);
        await acceptCookieBanner();
        await page.waitFor(800);
        await page.click('[data-id="button-create-private-offer-request"]');
        await page.waitFor(1000);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent'
        });
    });

    it('Open Dialog with registration data', async () => {
        await createInlineUiTestPage(testMap.ProductDetails);
        await acceptCookieBanner();
        await page.waitFor(800);
        await page.click('[data-id="button-create-private-offer-request"]');
        await page.waitFor(1000);
        await page.click('[data-id="checkbox-register-project"]');
        await page.waitFor(1000);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot('with registration data');
    });

});
