<template>
  <div>
    <company-profile-header :logo-url="logoUrl"></company-profile-header>
    <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="profile-container">
      <v-row>
        <v-col cols="12" xl="9">
          <company-info :header-data="pageData.headerData"
                        :about-data="pageData.aboutData"
                        :company-name="companyName"></company-info>
        </v-col>
        <v-col cols="12" class="d-xl-none">
          <v-divider></v-divider>
        </v-col>
        <v-col cols="12" xl="9" order-xl="last">
          <company-apps :company-uid="companyUid"></company-apps>
        </v-col>
        <v-col cols="12" class="d-xl-none">
          <v-divider></v-divider>
        </v-col>
        <v-col cols="12" xl="3">
          <contact-info :contact-data="pageData.contactData"></contact-info>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CompanyProfileData} from 'common/generated-types/types';
import CompanyApps from 'shop/companyprofile/components/CompanyApps.vue';
import CompanyInfo from 'shop/companyprofile/components/CompanyInfo.vue';
import CompanyProfileHeader from 'shop/companyprofile/components/CompanyProfileHeader.vue';
import ContactInfo from 'shop/companyprofile/components/ContactInfo.vue';

@Component({
  components: {
    ContactInfo,
    CompanyProfileHeader,
    CompanyInfo,
    CompanyApps,
  }
})
export default class CompanyProfile extends Vue {
  @Prop() pageData!: CompanyProfileData;

  created(): void {
    this.setPageTitle();
  }

  private setPageTitle(): void {
    document.title = `${this.companyName} – ${this.$t('navigation.storePageTitle') as string}`;
  }

  get companyName(): string {
    const companyName: string = this.pageData.companyData.name;
    const companyFriendlyName: string = this.pageData.companyData.friendlyName;
    return companyFriendlyName || companyName;
  }

  get companyUid(): string {
    return this.pageData.companyData.companyUid;
  }

  get logoUrl(): string {
    return this.pageData.headerData.logoUrl;
  }
}
</script>
