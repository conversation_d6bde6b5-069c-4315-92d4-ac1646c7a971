<template>
    <div class="scale-prices-container" v-if="!isEmpty(license.scalePrices)">
        <p class="callout">
            <CDIcon left>$discount</CDIcon>
            {{ $t('shop.productDetails.pricingSection.volumeDiscounts.callout', {num: maxDiscount}) }}</p>
        <table class="scale-prices">
            <thead>
            <tr>
                <td class="range">
                    <span>{{ $t('shop.productDetails.pricingSection.volumeDiscounts.table.headings.quantity') }}</span>
                </td>
                <td class="discount">
                    <span>{{ $t('shop.productDetails.pricingSection.volumeDiscounts.table.headings.discount') }}</span>
                </td>
                <td class="price">
                    <span>{{ $t('shop.productDetails.pricingSection.volumeDiscounts.table.headings.pricePerApp') }}</span>
                </td>
            </tr>
            </thead>
            <tbody>

            <tr v-for="(entry, index) in scalePrices" v-bind:key="entry.minQuantity">
                <td class="range">
                    <span>
                        <template v-if="index === scalePrices.length - 1">
                            {{entry.minQuantity}}+
                        </template>
                        <template v-else>
                            {{entry.minQuantity}} - {{scalePrices[index + 1].minQuantity - 1}}
                        </template>
                    </span>
                </td>
                <td class="discount">
                    <span>{{entry.discountPercent}}%</span>
                </td>
                <td class="price">
                    <span>{{ entry.currencyIsoCode }} {{ $n(entry.discountPrice, 'price', localeForFormats) }}</span>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {isEmpty} from 'lodash';
import {ScalePrice} from 'common/generated-types/types';
import {PurchasableLicenseData} from 'common/types';
import {i18nService} from '../../../../common/services';

@Component
export default class ScalePrices extends Vue {
    @Prop() license!: PurchasableLicenseData;
    isEmpty = isEmpty;

  localeForFormats = '';

    mounted(): void {
      this.localeForFormats = i18nService.getLocaleForFormats();
    }

    get scalePrices(): Array<ScalePrice> {
        return this.license.scalePrices ? this.license.scalePrices.filter(value => value.discountPercent > 0) : [];
    }

    get maxDiscount(): number {
        return this.scalePrices.map(scalePrice => scalePrice.discountPercent).sort((a, b) => b - a)[0];
    }

    get i18nService() {
      return i18nService;
    }

}
</script>

<style lang="scss" scoped>
@import "shop/core/constants";

.callout {
    display: grid;
    grid-template-columns: min-content auto;
    align-items: center;
    font-weight: bold;
    font-size: 12px;
    margin-bottom: 8px;
}

table {
  width: 100%;
  font-size: 12px;
  border-radius: 6px;

  .price {
    text-align: right;
  }

  tr td {
    &:first-of-type span {
      margin-left: $spacing-s;
      padding-right: $spacing-s;
    }

    &:last-of-type span {
      margin-right: $spacing-s;
    }
  }

  thead {
    tr td {
      font-size: 12px;
      font-weight: bold;
      padding-top: $spacing-s;

      span {
        margin-bottom: $spacing-xs;
        padding-bottom: $spacing-xs;
        display: block;
        border-bottom: 1px solid var(--v-grey-lighten2);
      }
    }
  }

  tbody {
    tr td {
      padding-bottom: $spacing-s;
    }
  }
}
</style>
