import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import {IntegrationsSection} from 'shop/productdetails/components';
import {AppIntegrationType, StandardAppIntegrationType} from '../../../../common/generated-types/types';

Vue.use(VueI18n);

const appIntegrations = [
    {
        displayName: 'IoT Gateway',
        externalDescription: 'This is a fairly long text for the IoT Gateway, explaining what it does and why ' +
            'it\'s there. Sometimes the explanation can be fairly long, but it shouldn\'t be excessive',
        integrationType: StandardAppIntegrationType.GATEWAY,
        type: AppIntegrationType.STANDARD,
        documentation: null
    },
    {
        displayName: 'Custom IoT Gateway',
        externalDescription: null,
        integrationType: null,
        type: AppIntegrationType.CUSTOM,
        documentation: {
            url: 'irrelevant',
            displayName: 'does not matter',
            size: 9938
        }
    },
    {
        displayName: 'ONVIF Standard',
        externalDescription: 'Integration with the ONVIF standard',
        integrationType: StandardAppIntegrationType.STANDARD,
        type: AppIntegrationType.STANDARD
    },
    {
        displayName: 'Custom Other integration',
        integrationType: null,
        type: AppIntegrationType.CUSTOM,
        documentation: {
            url: 'irrelevant',
            displayName: 'custom-integration.pdf',
            size: 9999938
        }
    },
    {
        displayName: 'Another Custom Integration',
        externalDescription: null,
        integrationType: null,
        type: AppIntegrationType.CUSTOM,
        documentation: {
            url: 'irrelevant',
            displayName: 'standard-integration.pdf',
            size: 200000
        }
    }
];

const appContactInfo = {
    emailAddress: '<EMAIL>'
};

new Vue({
    el: '#vue-app',
    vuetify,
    i18n,
    data: {
        appIntegrations,
        appContactInfo
    },
    components: {
        TestPageRoot,
        IntegrationsSection
    },
    methods: {},
    template: `
      <test-page-root>
        <div style="padding: 60px;">
          <integrations-section
              v-bind:app-integrations="appIntegrations"
              v-bind:app-contact-info="appContactInfo">
          </integrations-section>
        </div>
      </test-page-root>
    `
});
