import {mount} from '@vue/test-utils';
import Vue from 'vue';
import DescriptionSection from '../DescriptionSection';
import {TransformExternalLinks} from '../../../../common/directives';

Vue.directive('transformExternalLinks', TransformExternalLinks);

describe('DescriptionSection', () => {

    const wrapper = mount(DescriptionSection, {
        mocks: {
            $t: () => {
            },
            $d: () => {
            }
        },
        propsData: {
            productName: 'Product Name',
            productDescription: '<p>This is the description textt. This link is <a href="http://store.dev.local">internal</a>, and this link is <a href="https://vue-test-utils.vuejs.org/guides/#getting-started"></a></p>'
        }
    });

    it('simple existence test', () => {
        expect(wrapper.exists()).toBe(true)
    });

    it('should add target blank attributes to anchor tags with outgoing links', () => {

        const localUrl = "http://store.dev.local/";
        Object.defineProperty(window, "location", {
            value: new URL(localUrl)
        });

        const anchors = wrapper.findAll('.rich-text a');
        const internalLink = wrapper.find('a:first-of-type');
        const externalLink = wrapper.find('a:last-of-type');

        expect(anchors.length).toBe(2);
        expect(internalLink.exists()).toBe(true);
        expect(externalLink.exists()).toBe(true);

        expect(internalLink.attributes('target')).toBe(undefined);
        expect(externalLink.attributes('target')).toBe('_blank');

    });

});
