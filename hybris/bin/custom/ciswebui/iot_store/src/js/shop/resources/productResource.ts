import util from 'common/util';
import {AxiosResponse} from 'axios';
import {CategoryPageData, SimpleProductData} from 'common/generated-types/types';
import {SearchQueryParams} from 'common/types';

let axios = util.axios;
const basePath = '/shop/api/products';

let productResource = {
    getProducts(queryParams: SearchQueryParams): Promise<AxiosResponse<CategoryPageData>> {
        return axios.get(`${basePath}?${toQueryString(queryParams)}`)
    },
    getProduct(productCode: string): Promise<AxiosResponse<SimpleProductData>> {
        return axios.get(`${basePath}/${productCode}`)
    },
};

function toQueryString(params: SearchQueryParams) {
    return Object.entries(params).map(kv => kv.map(encodeURIComponent).join("=")).join("&");
}

export default productResource;
