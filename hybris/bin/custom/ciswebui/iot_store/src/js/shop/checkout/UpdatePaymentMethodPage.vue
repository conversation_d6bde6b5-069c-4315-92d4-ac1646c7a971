<template>
  <div class="update-payment-page">
    <checkout-responsive-grid-layout v-bind:header-title="checkoutPaymentMethodHeader">
      <template v-slot:main-content-1>
        <p class="mt-n8 mb-8">{{ $t('shop.updatePayment.subtitle') }}</p>
        <CDNotificationPersistent
            class="alert-info mb-8"
            :dismissible="true"
            data-id="payment-method-problem-text"
            type="warning">
          {{$t('shop.updatePayment.problemWithPayment', {'ccEnding': cardEnding()})}}
        </CDNotificationPersistent>
        <v-radio-group name="paymentMethod" v-model="currentPaymentMethodId">
          <h3 class="mb-5 mt-0">{{ $t('shop.updatePayment.newCard') }}</h3>
          <div class="card-entry new" v-bind:class="{'hide-payment-form':!isNewPaymentMethod()}">
            <CDRadio :value="'NEW'"
                     v-on:change="enableCreationOfNewPaymentMethod()"
                     class="py-6 pr-6"
                     data-id="new-card-option">
              <template v-slot:label>
                <payment-method-entry v-bind:new-entry="true"
                                      v-bind:new-entry-type="PaymentMethodType.CREDIT_CARD"
                                      class="mt-n3">
                </payment-method-entry>
              </template>
            </CDRadio>
            <p class="overwrite-hint pt-0 pr-6 pb-0 pl-16 body-2" v-if="paymentNotSaved && isNewPaymentMethod()">
              {{ $t('shop.updatePayment.paymentMethodOverwriteHint') }}
            </p>
            <div class="payment-form pa-6 pl-0" ref="paymentForm">
              <v-container>
                <v-row no-gutters>
                  <v-col cols="12" md="5" lg="4">
                      <div id="card-number-container" class="payment-provider-element-container">
                        <div id="card-number" class="payment-provider-element">
                          <input type="text"
                                 placeholder="Card number"
                                 style="border: 1px solid #979EA4; width: 100%; border-radius: 8px; padding: 13px 10px 11px 16px;"
                          />
                        </div>
                        <small class="payment-provider-field-error">{{ cardNumberError }}</small>
                      </div>
                  </v-col>
                  <v-col cols="12" md="7" lg="6">
                    <v-card class="ml-0 ml-md-6" elevation="0">
                      <div id="card-holder-container" class="payment-provider-element-container">
                        <div class="payment-provider-element" id="card-holder">
                          <input type="text"
                                 placeholder="Name on card"
                                 style="border: 1px solid #979EA4; width: 100%; border-radius: 8px; padding: 13px 10px 11px 16px;"
                          />
                        </div>
                      </div>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row no-gutters>
                  <v-col cols="7" md="4" lg="3">
                    <div id="card-expiry-container" class="payment-provider-element-container">
                      <div id="card-expiry" class="payment-provider-element">
                        <input type="text"
                               placeholder="Expiry date (mm/yy)"
                               style="border: 1px solid #979EA4; width: 100%; border-radius: 8px; padding: 13px 10px 11px 16px;"
                        />
                      </div>
                      <small class="payment-provider-field-error">{{ cardExpiryError }}</small>
                    </div>
                  </v-col>
                  <v-col cols="6" md="4" lg="3">
                    <v-card class="ml-0 ml-md-6" elevation="0">
                      <div id="card-cvc-container" class="payment-provider-element-container">
                        <div id="card-cvc" class="payment-provider-element">
                          <input type="text"
                                 placeholder="Security code"
                                 style="border: 1px solid #979EA4; width: 100%; border-radius: 8px; padding: 13px 10px 11px 16px;"
                          />
                        </div>
                        <small class="payment-provider-field-error">{{ cardCvcError }}</small>
                      </div>
                    </v-card>
                  </v-col>
                </v-row>
              </v-container>
            </div>
          </div>
          <div class="card-entry saved" v-bind:key="item.id" v-for="item in paymentInfos">
            <CDRadio :value="item.id"
                     v-on:change="setPaymentMethod(item)"
                     class="py-6 pr-6"
                     data-id="saved-card-option">
              <template v-slot:label>
                <payment-method-entry v-bind:payment-data="item"
                                      v-on:delete="confirmCardDelete(item)"
                                      class="mt-n3"
                                      v-bind:actions="true">
                  <template v-slot:creditcard></template>
                </payment-method-entry>
              </template>
            </CDRadio>
            <p class="payment-method-hint pt-0 pr-5 pb-3 pl-16 body-2" v-if="!item?.saved">
              {{ $t('shop.updatePayment.paymentMethodNotSavedHint') }}</p>
          </div>
        </v-radio-group>
        <div class="card-save">
          <CDCheckbox
              v-on:change="cardSaved = $event"
              data-id="save-card-option"
              color="primary">
            <template v-slot:label>
              <p class="mb-0">{{ $t('shop.updatePayment.saveCard') }}</p>
            </template>
          </CDCheckbox>
        </div>
      </template>
      <template v-slot:main-content-2>
        <mini-summary v-if="hasOrderPaymentUpdateData"
                      v-bind:payment-address="orderPaymentUpdateDataComputed?.order.paymentAddress"
                      v-bind:price="orderPaymentUpdateDataComputed?.order.totalPrice"
                      v-bind:payment-method="orderPaymentUpdateDataComputed?.order.paymentMethod"
                      v-bind:display-tax-note="true">
          <CDButton class="primary save-button"
                    large
                    color="primary"
                    :disabled="buttonDisabled && !isPaymentFormComplete"
                    data-id="pay-now-btn"
                    v-on:click="payNow">
            {{ $t('payNow') }}
          </CDButton>
        </mini-summary>
      </template>
    </checkout-responsive-grid-layout>
    <confirmation-dialog v-bind:confirmation="confirmationDialog"
                         v-if="showConfirmCard"
                         v-on:cancel="showConfirmCard = false"
                         v-on:confirm="onConfirm">
    </confirmation-dialog>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {
  AchCreditTransferPaymentInfoData,
  CheckoutInfoData,
  CreditCardPaymentInfoData,
  InvoicePaymentInfoData,
  OrderPaymentUpdate,
  OrderPaymentUpdateData,
  PaymentInfoData,
  PaymentMethodType,
  PaymentProvider,
  SepaCreditTransferPaymentInfoData,
  SepaMandatePaymentInfoData,
  ShopCoreData
} from 'common/generated-types/types';
import {CheckoutResponsiveGridLayout, MiniSummary, PaymentMethodEntry} from 'shop/checkout/components';
import {pageSpinner} from 'common/components/spinner';
import {messageService} from 'common/services';
import {ConfirmationDialogType} from 'common/types';
import {find, isEmpty, reject} from 'lodash';
import anime from 'animejs';
import def from 'common/defaults';
import {paymentResource} from 'shop/resources';
import ConfirmationDialog from 'common/components/popups/ConfirmationDialog.vue';
import {PaymentMethodIcons, StatusTag} from 'common/components';
import Tooltip from 'common/components/Tooltip.vue';
import util from 'common/util';
import {
  CreditCardOptionsName,
  FormEvent,
  IntegrationCreateTokenError,
  IntegrationCreateTokenResponse,
  prepareOptions,
  validationChangePayload
} from 'common/dpg';
import PaymentUpdateResource from 'shop/resources/paymentUpdateResources';
import {ShopRoute} from 'common/constants';

@Component({
  computed: {
    PaymentMethodType() {
      return PaymentMethodType;
    }
  },
  components: {
    PaymentMethodEntry,
    CheckoutResponsiveGridLayout,
    MiniSummary,
    ConfirmationDialog,
    PaymentMethodIcons,
    StatusTag,
    Tooltip
  }
})
export default class UpdatePaymentMethodPage extends Vue {
  @Prop() coreData!: ShopCoreData;
  @Prop() pageData!: string;

  readonly ORDER_CODE = this.pageData;
  readonly NEW_PAYMENT_METHOD = 'NEW';

  readonly SOURCE_ORDER = 'order';
  readonly NO_PAYMENT_METHOD = '';

  orderPaymentUpdateData!: OrderPaymentUpdateData;
  hasOrderPaymentUpdateData = false;

  PaymentProvider = PaymentProvider;

  paymentMethod!: PaymentMethodType;
  checkoutInfos!: Map<PaymentMethodType, CheckoutInfoData>;
  orderPaymentInfo!: PaymentInfoData | undefined;
  paymentInfos: CreditCardPaymentInfoData[] = [];
  invoicePaymentInfos!: InvoicePaymentInfoData[];
  buttonDisabled = true;
  currentPaymentMethodId = '';
  paymentUpdateResource = new PaymentUpdateResource();

  isForIndividualOrder!: boolean;

  renderDummy = 0;

  showConfirmCard = false;
  confirmCard: PaymentInfoData | undefined = undefined;

  cardSaved = false;

  cardHolderIsCompleted = false;

  cardNumberError = '';
  cardNumberIsCompleted = false;

  cardExpiryError = '';
  cardExpiryIsCompleted = false;

  cardCvcError = '';
  cardCvcIsCompleted = false;

  /* eslint-disable @typescript-eslint/no-explicit-any */
  dpgInstance!: any;
  dpgElements!: any;

  $refs!: {
    paymentForm: HTMLElement
  };

  created(): void {
    this.setPageTitle();
  };

  async mounted(): Promise<void> {
    const source = this.$route.query.source as string;
    if (source) {
      this.isForIndividualOrder = source === this.SOURCE_ORDER;
    }

    pageSpinner.start();
    messageService.clearAll();
    const result = await this.paymentUpdateResource.getPaymentInfoData(this.ORDER_CODE)
        .catch((error) => this.handleCheckoutCreationError(error))
        .finally(() => pageSpinner.stop());

    if (!result) return;

    this.orderPaymentUpdateDataComputed = result.data;
    this.checkoutInfos = this.checkoutInfosAsMap;
    this.paymentInfos = this.creditCardCheckoutPaymentInfos;
    this.invoicePaymentInfos = this.mergedBankTransferInfos;
    this.paymentMethod = this.orderPaymentUpdateDataComputed.order.paymentInfo.paymentMethod;
    this.orderPaymentInfo = this.orderPaymentUpdateData?.order?.paymentInfo;

    this.currentPaymentMethodId = this.currentPaymentMethod;

    if (!this.isNewPaymentMethod()) {
      this.buttonDisabled = false;
    } else {
      this.showPaymentForm();
    }

    if (this.hasDpgCreditCardForm) {
      const dpgjsUrl = this.creditCardCheckout?.userActionParameters.dpgjsUrl;
      const sessionId = this.creditCardCheckout?.userActionParameters.sessionId;
      const sessionConfig = this.creditCardCheckout?.userActionParameters.sessionConfig;

      if (dpgjsUrl && sessionId && sessionConfig) {
        await util.scriptLoader(dpgjsUrl, PaymentProvider.DPG);
        this.dpgInstance = (window as any).integration;
        await this.initDpgComponents(sessionId, sessionConfig);
      } else {
        messageService.error(this.$t('shop.checkout.error') as string);
      }
    }
  }

  get orderPaymentUpdateDataComputed(): OrderPaymentUpdateData {
      return this.orderPaymentUpdateData;
  }

  set orderPaymentUpdateDataComputed(paymentUpdateData: OrderPaymentUpdateData) {
      this.orderPaymentUpdateData = paymentUpdateData;
      this.hasOrderPaymentUpdateData = true;
  }

  get checkoutInfosAsMap() : Map<PaymentMethodType, CheckoutInfoData> {
    return new Map(this.orderPaymentUpdateData.checkoutInfo.map(checkoutInfoData => [checkoutInfoData.paymentMethod, checkoutInfoData!]));
  }

  get creditCardCheckout() : CheckoutInfoData | undefined {
    return this.checkoutInfos.get(PaymentMethodType.CREDIT_CARD);
  }

  get creditCardCheckoutPaymentInfos() : CreditCardPaymentInfoData[] {
    return (this.creditCardCheckout ? this.creditCardCheckout!.paymentInfos : []) as CreditCardPaymentInfoData[];
  }

  get sepaBanktransferCheckout() : CheckoutInfoData | undefined {
    return this.checkoutInfos.get(PaymentMethodType.SEPA_CREDIT);
  }

  get sepaBanktransferCheckoutPaymentInfos() : SepaCreditTransferPaymentInfoData[] {
    return (this.sepaBanktransferCheckout ? this.sepaBanktransferCheckout!.paymentInfos : []) as SepaCreditTransferPaymentInfoData[];
  }

  get achBanktransferCheckout() : CheckoutInfoData | undefined {
    return this.checkoutInfos.get(PaymentMethodType.ACH_INTERNATIONAL);
  }

  get achBanktransferCheckoutPaymentInfos() : AchCreditTransferPaymentInfoData[] {
    return (this.achBanktransferCheckout ? this.achBanktransferCheckout!.paymentInfos : []) as AchCreditTransferPaymentInfoData[];
  }

  get sepaDirectDebitCheckout() : CheckoutInfoData | undefined {
    return this.checkoutInfos.get(PaymentMethodType.SEPA_DIRECTDEBIT);
  }

  get sepaDirectDebitCheckoutPaymentInfos() : SepaMandatePaymentInfoData[] {
    return (this.sepaDirectDebitCheckout ? this.sepaDirectDebitCheckout!.paymentInfos : []) as SepaMandatePaymentInfoData[];
  }

  get mergedBankTransferInfos() : InvoicePaymentInfoData[] {
    const sepaInfos : InvoicePaymentInfoData[] = this.sepaBanktransferCheckoutPaymentInfos as InvoicePaymentInfoData[];
    const achInfos : InvoicePaymentInfoData[] = this.achBanktransferCheckoutPaymentInfos as InvoicePaymentInfoData[];

    return sepaInfos.concat(achInfos);
  }

  get hasDpgCreditCardForm() : boolean | undefined {
    return this.creditCardCheckout && this.creditCardCheckout.paymentProvider === PaymentProvider.DPG && this.creditCardCheckout.userCreatable;
  }

  get isPaymentFormComplete(): boolean {
    return this.cardHolderIsCompleted && this.cardNumberIsCompleted && this.cardExpiryIsCompleted && this.cardCvcIsCompleted;
  }

  get checkoutPaymentMethodHeader(): string {
    return this.$t('shop.updatePayment.header') as string;
  }

  async initDpgComponents(sessionId : string, sessionConfig : string): Promise<void> {
    const options = prepareOptions(sessionId, sessionConfig);

    try {
      this.dpgElements = await this.dpgInstance.initialize(options);

      this.dpgElements.on(FormEvent.VALIDATION_CHANGE, this.validationHandler);
    } catch (er) {
      this.handleCaptureResponseError(er as IntegrationCreateTokenError);
    }
  }

  private validationHandler(event: validationChangePayload): void {
    switch (event.field.name) {
      case CreditCardOptionsName.CODE:
        if (!event.validation.valid) {
          this.cardCvcError = this.$t('shop.checkout.wrongValue') as string;
        } else {
          this.cardCvcError = '';
        }
        this.cardCvcIsCompleted = event.validation.completed;
        break;
      case CreditCardOptionsName.EXPIRY:
        if (!event.validation.valid) {
          this.cardExpiryError = this.$t('shop.checkout.wrongValue') as string;
        } else {
          this.cardExpiryError = '';
        }
        this.cardExpiryIsCompleted = event.validation.completed;
        break;
      case CreditCardOptionsName.NUMBER:
        if (!event.validation.valid) {
          this.cardNumberError = this.$t('shop.checkout.wrongValue') as string;
        } else {
          this.cardNumberError = '';
        }
        this.cardNumberIsCompleted = event.validation.completed;
        break;
    }
    this.cardHolderIsCompleted = true;
  }

  get hasInvoiceInfos(): boolean {
    return this.invoicePaymentInfos?.length > 0;
  }

  get currentPaymentMethod(): string {
    if (!this.paymentMethod) {
      return this.NEW_PAYMENT_METHOD;
    }

    if (this.paymentMethod === PaymentMethodType.CREDIT_CARD && this.paymentInfos?.length > 0) {
      const defaultCreditCardPaymentInfoData: CreditCardPaymentInfoData | undefined = this.paymentInfos
          .filter((creditCardPaymentInfoData: CreditCardPaymentInfoData) => creditCardPaymentInfoData.defaultPaymentInfo)
          .map((creditCardPaymentInfoData: CreditCardPaymentInfoData) => {
            return creditCardPaymentInfoData;
          }).find((creditCardPaymentInfoData: CreditCardPaymentInfoData | undefined) => creditCardPaymentInfoData);

      if (defaultCreditCardPaymentInfoData) {
        return defaultCreditCardPaymentInfoData.id;
      }
    } else if (this.hasInvoiceInfos) {
      if (this.invoicePaymentInfos?.length > 0 && !!this.invoicePaymentInfos[0]) {
        return this.invoicePaymentInfos[0].id;
      }
    }
    return this.NEW_PAYMENT_METHOD;
  }

  private setPageTitle(): void {
    document.title = this.$t('shop.checkout.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
  }

  private isNewPaymentMethod(): boolean {
    return this.currentPaymentMethodId === this.NEW_PAYMENT_METHOD;
  }

  cardEnding(): string {
    if (this.orderPaymentInfo?.paymentMethod === PaymentMethodType.CREDIT_CARD) {
      const ccInfoData = this.orderPaymentInfo as CreditCardPaymentInfoData;
      return (this.orderPaymentInfo as CreditCardPaymentInfoData).cardNumber.length >= 4 ? ccInfoData.cardNumber.slice(-4) : '';
    }
    return '';
  }

  async payNow(): Promise<void> {
    pageSpinner.start();
    messageService.clearAll();
    if (!this.isNewPaymentMethod()) {
      const orderPaymentUpdate: OrderPaymentUpdate = {
        paymentInfoId: this.currentPaymentMethodId
      } as OrderPaymentUpdate;

      await this.paymentUpdateResource.setDefaultPaymentInfo(this.ORDER_CODE, orderPaymentUpdate)
          .then(() => this.handleUpdatePaymentSuccess())
          .catch((error) => this.handleUpdatePaymentError(error))
          .finally(() => pageSpinner.stop());
    } else {
      try {
        const response = await this.dpgElements.createToken();
        const createTokenResponse = response as IntegrationCreateTokenResponse;
        if (createTokenResponse.info.creditCard) {
          const [expiryMonth, expiryYear] = createTokenResponse.info.creditCard.expiry.split('/');
          const paymentInfo: CreditCardPaymentInfoData = {
            paymentMethod: this.paymentMethod,
            paymentProvider: PaymentProvider.DPG,
            reusable: this.cardSaved,
            accountHolderName: createTokenResponse.info.creditCard.holder,
            cardNumber: createTokenResponse.info.creditCard.paddedPan,
            expiryMonth,
            expiryYear,
            token: createTokenResponse.token
          } as CreditCardPaymentInfoData;

          await this.paymentUpdateResource.createAndSetDefaultPaymentInfo(this.ORDER_CODE, paymentInfo)
              .then(() => this.handleUpdatePaymentSuccess())
              .catch((error) => this.handleUpdatePaymentError(error));
        }
      } catch (error) {
        const createTokenErr = error as IntegrationCreateTokenError;
        this.handleCaptureResponseError(createTokenErr);
      } finally {
        pageSpinner.stop();
      }
    }
  }

  private handleUpdatePaymentSuccess(): void {
    if (this.isForIndividualOrder) {
      window.location.assign(this.orderDetailsUrl);
    }
    window.location.assign(this.orderHistoryUrl);
  }

  private handleCheckoutCreationError(error: any) {
      const response = error.response;
      if (response?.data?.message) {
          messageService.error(response.data.message);
      } else {
          messageService.error(this.$t('backendError') as string);
      }
      window.location.assign(this.orderHistoryUrl);
  }

  private handleUpdatePaymentError(error: any) {
    const response = error.response;
    if (response?.data?.message) {
      messageService.error(response.data.message);
    } else {
      messageService.error(this.$t('backendError') as string);
    }
  }

  private handleCaptureResponseError(error: IntegrationCreateTokenError): void {
    if (error) {
      if (error.detail) {
        messageService.error(error.detail as string);
      } else {
        messageService.error(this.$t('shop.checkout.error') as string);
      }
    }
  }

  confirmCardDelete(card: PaymentInfoData): void {
    this.confirmCard = card;
    this.showConfirmCard = true;
  }

  get orderDetailsUrl(): string {
    return `${ShopRoute.ORDER_HISTORY}/${this.ORDER_CODE}?paid-orders=${this.ORDER_CODE}`;
  }

  get orderHistoryUrl(): string {
    return `${ShopRoute.ORDER_HISTORY}?paidOrders=${this.ORDER_CODE}`;
  }

  get confirmationDialog(): ConfirmationDialogType {
    return {
      header: this.$t('shop.checkout.deletePaymentMethod').toString(),
      buttonLabel: this.$t('delete').toString()
    };
  }

  onConfirm(): void {
    if (this.confirmCard) {
      const card = this.confirmCard;
      pageSpinner.start();
      paymentResource.deletePaymentMethod(card.id)
          .then(() => {
            if (!this.paymentInfos) return;
            this.paymentInfos = reject(this.paymentInfos, ['id', card.id]);

            if (this.currentPaymentMethodId === card.id) {
              this.currentPaymentMethodId = this.NO_PAYMENT_METHOD;
              this.buttonDisabled = true;
            }

            messageService.success(this.$t('shop.payment.removed') as string);
          }).catch((error) => {
          const response = error.response;

          if (response && response.data) {
            messageService.errorResponse(error.response?.data, this.$i18n);
          } else {
            messageService.error(this.$t('backendError') as string);
          }
      }).finally(() => {
        pageSpinner.stop();
        this.showConfirmCard = false;
      });
    }
  }

  showPaymentForm(): void {
    if (!this.$refs.paymentForm) return;

    this.$refs.paymentForm.style.display = 'flex';
    this.renderDummy = this.$refs.paymentForm.offsetHeight;
    this.buttonDisabled = true;
    anime({
      targets: this.$refs.paymentForm,
      duration: 150,
      height: 'unset',
      easing: def.animation.easeout
    });
  }

  get paymentNotSaved(): boolean {
    return !isEmpty(this.paymentInfos) && !find(this.paymentInfos, 'saved');
  }

  hidePaymentForm(): void {
    if (!this.$refs.paymentForm) return;

    anime({
      targets: this.$refs.paymentForm,
      duration: 150,
      height: 'inherit',
      easing: def.animation.easeout,
      complete: () => {
        this.$refs.paymentForm.style.display = 'none';
        this.buttonDisabled = false;
      }
    });
  }

  setPaymentMethod(selection: PaymentInfoData): void {
    messageService.clearAll();
    this.currentPaymentMethodId = selection.id;
    this.orderPaymentUpdateData.order.paymentMethod = selection.paymentMethod;
    this.hidePaymentForm();
  }

  enableCreationOfNewPaymentMethod(): void {
    messageService.clearAll();
    this.showPaymentForm();
    this.currentPaymentMethodId = this.NEW_PAYMENT_METHOD;
  }
}
</script>

<style scoped lang="scss">
@import "common/design";
@import "shop/core/constants";
@import "shop/pages/checkout";

.update-payment-page {

  .payment-method-hint {
    color: var(--v-grey-darken1);
  }

  .overwrite-hint {
    color: var(--v-grey-darken1);
  }

  .v-input--radio-group {
    display: flex;
    flex-direction: column;

    .v-radio {
      align-items: flex-start;
      margin-bottom: 0;
    }
  }

  .card-entry {
    width: 100%;
    position: relative;

    .payment-form {
      display: none;

      .payment-provider-element-container {
        min-height: 55px;
        display: flex;
        flex-direction: column;

        .payment-provider-field-error {
          color: var(--v-error-base);
        }
      }
    }

    .delete-action {
      height: 24px;
      position: absolute;
      right: 20px;
      top: 22px;

      &:hover :deep(.path-normal) {
        stroke: var(--v-primary-base);
      }

      &:hover :deep(.path-light) {
        stroke: var(--v-primary-lighten4);
      }
    }
  }

  a {
    &:not(.v-btn):not(.v-card) {
      text-decoration: none !important;
    }
  }
}
</style>
