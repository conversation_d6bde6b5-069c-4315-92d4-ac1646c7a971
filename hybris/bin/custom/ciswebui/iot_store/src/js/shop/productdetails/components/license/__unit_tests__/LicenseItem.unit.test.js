import {createLocalVue, mount} from '@vue/test-utils';
import {LicenseType} from "common/types";
import 'common/test-directive';
import 'common/testtools/unit_tests_mock';
import LicenseItem from '../LicenseItem';
import {LicensePurchasability} from "common/generated-types/types";
import { i18n } from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

jest.mock('common/util');

const localVue = createLocalVue();
localVue.use(i18n);

const scalePricesBasic = [
    {
        minQuantity: 5,
        discountPercent: 5,
        discountPrice: 45,
        currencyIsoCode: 'USD'
    }
];

const scalePricesGroup = [
    {
        minQuantity: 5,
        discountPercent: 5,
        discountPrice: 47.5,
        currencyIsoCode: 'USD'
    },
    {
        minQuantity: 10,
        discountPercent: 10,
        discountPrice: 45,
        currencyIsoCode: 'USD'
    },
    {
        minQuantity: 20,
        discountPercent: 20,
        discountPrice: 40,
        currencyIsoCode: 'USD'
    }
];

const mountLicenseItem = (licenseType, purchasability, scalePrices) => mount(LicenseItem, {
    mocks: {
        $te: () => {
        },
        $n: () => {
        }
    },
    i18n,
    vuetify,
    localVue,
    stubs: ['info-icon'],
    propsData: {
        maxAllowedQuantity: 1000,
        company: {
            friendlyName: null,
            name: 'Bosch Digital Commerce'
        },

        license: {
            code: licenseType,
            name: 'Purchase',
            currencyIsocode: 'USD',
            price: 50,
            licenseType: {
                code: licenseType,
                type: licenseType
            },
            scalePrices: scalePrices,
            purchasability: purchasability
        },
    }

});

describe('License Item', () => {

    beforeEach(() => {
        window.frontendData = {
            coreData: {
                currentCompany: {
                    companyApproved: true,
                },
                allowedToBuy: true
            }
        };
    });

    function verifyElementPresence(wrapper, allowPurchase) {
        expect(wrapper.find('.license-item .cell-quantity').exists()).toBe(allowPurchase);
        expect(wrapper.find('.license-item .cell-cart-action').exists()).toBe(allowPurchase);
    }

    it('User have purchase permission and company is approved', () => {
        const wrapper = mountLicenseItem(LicenseType.EVALUATION, LicensePurchasability.PURCHASABLE, null);
        verifyElementPresence(wrapper, true);
    });

    it('User have purchase permission but company is not approved', () => {
        window.frontendData.coreData.currentCompany.companyApproved = false;
        const wrapper = mountLicenseItem(LicenseType.FULL, LicensePurchasability.UNPAYABLE, null);
        verifyElementPresence(wrapper, false);
    });

    it('User does not have purchase permission but company is approved allows purchasing eval license only', () => {
        window.frontendData.coreData.allowedToBuy = false;
        const wrapper = mountLicenseItem(LicenseType.FULL, LicensePurchasability.PURCHASABLE, null);
        verifyElementPresence(wrapper, false);
    });

    it('subscription shows info text if not enabled', () => {
        const wrapper = mountLicenseItem(LicenseType.SUBSCRIPTION, LicensePurchasability.UNPAYABLE, null);

        expect(wrapper.find('.cell-quantity').exists()).toBeFalsy();
        expect(wrapper.find('.cell-cart-action').exists()).toBeFalsy();
        expect(wrapper.find('.permission-info-container').exists()).toBeTruthy();
        expect(wrapper.find('.permission-info-container').text()).toEqual('The subscriptions can currently only be ordered with the bank transfer payment method. For details, please contact our customer support.');
        expect(wrapper.find('.cell-license-details').exists()).toBeFalsy();
    });

    it('displays a table of scale prices', () => {
        const wrapper = mountLicenseItem(LicenseType.FULL, LicensePurchasability.PURCHASABLE, scalePricesBasic);
        expect(wrapper.find('.scale-prices').exists()).toBe(true);
    });

    it('calculates and renders scale price ranges for a single scale price entry', () => {
        const wrapper = mountLicenseItem(LicenseType.FULL, LicensePurchasability.PURCHASABLE, scalePricesBasic);
        expect(wrapper.find('table.scale-prices tbody tr:nth-child(1) td.range').text()).toEqual('5+');
    });

    it('calculates and renders scale price ranges for multiple scale price entries', () => {
        const wrapper = mountLicenseItem(LicenseType.FULL, LicensePurchasability.PURCHASABLE, scalePricesGroup);
        expect(wrapper.find('table.scale-prices tbody tr:nth-child(1) td.range').text()).toEqual('5 - 9');
        expect(wrapper.find('table.scale-prices tbody tr:nth-child(2) td.range').text()).toEqual('10 - 19');
        expect(wrapper.find('table.scale-prices tbody tr:nth-child(3) td.range').text()).toEqual('20+');
        expect(wrapper.find('table.scale-prices tbody tr:nth-child(4)').exists()).toBeFalsy();
    });

    describe('License quantity validation', () => {
        const maxAllowedFull = 10;
        const maxAllowedSubs = 100;
        window.frontendData.coreData = {
            ...window.frontendData.coreData,
            allowedMaxQuantityLineItemFullLicense: maxAllowedFull,
            allowedMaxQuantityLineItemSubsLicense: maxAllowedSubs
        };

        const wrapper = mountLicenseItem(LicenseType.FULL, LicensePurchasability.PURCHASABLE, scalePricesGroup);

        it('is valid for Full license when quantity lower than max allowed', () => {

            wrapper.vm.updateQuantityIfValid({}, maxAllowedFull, LicenseType.FULL);
            expect(wrapper.vm.quantityValidationMessage).toEqual('');
        });

        it('is not valid for Full license when quantity exceeds max allowed', () => {

            wrapper.vm.updateQuantityIfValid({}, maxAllowedFull + 1, LicenseType.FULL);
            expect(wrapper.vm.quantityValidationMessage).toEqual('The maximum order quantity of a individual app is 10.');
        });

        it('is valid for Subscription license when quantity lower than max allowed', () => {

            wrapper.vm.updateQuantityIfValid({}, maxAllowedSubs, LicenseType.SUBSCRIPTION);
            expect(wrapper.vm.quantityValidationMessage).toEqual('');
        });

        it('is not valid for Subscription license when quantity exceeds max allowed', () => {

            wrapper.vm.updateQuantityIfValid({}, maxAllowedSubs + 1, LicenseType.SUBSCRIPTION);
            expect(wrapper.vm.quantityValidationMessage).toEqual('The maximum order quantity of a individual app is 100.');
        });
    });
});
