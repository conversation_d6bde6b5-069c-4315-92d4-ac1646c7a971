<template>
    <div class="marketing-header-body">
        <div class="marketing-header">
            <a v-for="navigationItem in marketingHeaders"
               class="marketing-header-link black--text"
               v-bind:target="navigationItem.target"
               v-bind:key="navigationItem.text"
               v-bind:href="navigationItem.url">
                {{ navigationItem.text }}
            </a>
        </div>
    </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {navigationService, userPermissionService} from 'common/services';

@Component
export default class MarketingHeader extends Vue {
    @Prop() marketingHeaders!: [];
    navigationService = navigationService;
    userPermissionService = userPermissionService;

}
</script>

<style scoped lang="scss">
@import "shop/core/constants";
@import "common/breakpoints";

.marketing-header {
    display: none;
    @include respond-to('M') {
        display: block;
    }
    background: var(--v-grey-lighten4);
    position: relative;
    width: 100%;
    padding: $spacing-xs;

    a {
        margin: 0 10px;
        color: var(--v-grey-darken2);
        &:after {
            display: none !important;
        }
        &:hover {
            color: var(--v-primary-base);
        }
    }
}

</style>
