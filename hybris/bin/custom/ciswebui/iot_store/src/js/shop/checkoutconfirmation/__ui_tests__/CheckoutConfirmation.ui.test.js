import {acceptCookieBanner, createInlineUiTestPage} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import {cloneDeep} from "lodash";
import * as Sizes from "../../../common/testtools/testScreenSizes";

describe("CheckoutConfirmation test", () => {

    it("responsiveness test", async () => {
        const data = cloneDeep(testMap.CheckoutConfirmation);
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(1000);

        await Sizes.testAllScreenSizes(1400);
    });

    it("confirmation page with own app", async () => {
        const data = cloneDeep(testMap.OwnAppCheckoutConfirmation);
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(1000);

        await Sizes.testAllScreenSizes(1400);
    });

    it("confirmation page with trial app purchased", async () => {
        const data = cloneDeep(testMap.TrialCheckoutConfirmation);
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(1000);

        await Sizes.testAllScreenSizes(1400);
    });
});
