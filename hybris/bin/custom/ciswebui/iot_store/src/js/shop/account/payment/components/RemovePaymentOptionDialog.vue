<template>
    <v-dialog v-model="dialogIsOpen"
              width="500"
              :fullscreen="$vuetify.breakpoint.smAndDown"
              content-class="white pa-8"
              @input="handleDialogInput">

      <div data-id="container-remove-payment-option-dialog" >
          <h3 data-id="text-remove-payment-dialog-header" class="mb-8 text-capitalize">
            {{ $t('shop.payment.removePaymentOption') }}
          </h3>
        <div data-id="container-remove-payment-option-dialog-body">
          <v-row class="mb-8" dense>
            <v-col cols="12">
              <span data-id="text-remove-pa">
                {{ $t('shop.payment.removePaymentOptionInfo') }}
              </span>
            </v-col>
          </v-row>
        </div>

        <div data-id="container-remove-payment-option-dialog-footer">
          <v-row>
              <v-col cols="12" class="text-right">
                  <CDButton data-id="remove-payment-option-dialog-footer-cancel" class="mr-2" @click="onCancel">{{ $t('cancel') }}</CDButton>
                  <CDButton data-id="remove-payment-option-dialog-footer-remove" color="primary" @click="onConfirm">{{ $t('remove') }}</CDButton>
              </v-col>
          </v-row>
        </div>
      </div>
    </v-dialog>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';

@Component
export default class RemovePaymentOptionDialog extends Vue {
	@Prop({ type: Boolean, required: true }) isOpen!: boolean;

	dialogIsOpen: boolean = this.isOpen;

	@Watch('isOpen')
	onIsOpenChange(newVal: boolean) {
		this.dialogIsOpen = newVal;
	}

	onConfirm() {
		this.$emit('confirm');
    }

	onCancel(): void {
		this.doCancelAction(false);
	}

    handleDialogInput(value: boolean) {
	    this.doCancelAction(value);
	}

	private doCancelAction(value: boolean) {
		this.dialogIsOpen = value;
		this.$emit('cancelAction');
	}
}
</script>
<style scoped lang="scss">
.v-dialog__content:has(.v-dialog--persistent) {
  backdrop-filter: blur(6px);
}
</style>
