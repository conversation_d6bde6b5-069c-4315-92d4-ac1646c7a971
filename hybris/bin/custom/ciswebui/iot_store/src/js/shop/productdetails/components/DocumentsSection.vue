<template>
    <div class="documents-section">
        <v-container :fluid="true" class="pa-0">
            <v-container :fluid="$vuetify.breakpoint.lgAndDown">
                <v-row>
                    <v-col cols="12">
                        <h2 class="label">
                            {{$t('shop.productDetails.documentsSection.title')}}
                        </h2>
                    </v-col>
                </v-row>
                <v-row class="pdf-document" v-for="pdfData in pdfDataList" v-bind:key="pdfData.code">
                    <v-col cols="12" lg="9" class="d-flex">
                        <CDIcon color="black">$sheet</CDIcon>
                        <p class="ml-4 mr-2">{{pdfData.displayName}}</p>
                    </v-col>
                    <v-col cols="12" lg="3" class="d-flex justify-lg-end pt-0 pt-lg-2 pl-2">
                        <CDButtonTextIcon icon="$download" color="primary" :href="pdfData.url" download>
                            <span class="download-text">Download</span>
                        </CDButtonTextIcon>
                    </v-col>
                </v-row>
            </v-container>
        </v-container>
    </div>
</template>

<script lang="ts">
    import {Component, Vue, Prop} from 'vue-property-decorator';
    import {PdfData} from 'common/generated-types/types';
    import DownloadIcon from 'common/images/boxed/icon-16-download-a.svg';

    @Component({components: {DownloadIcon}})
    export default class DocumentsSection extends Vue {
        @Prop() pdfDataList!: Array<PdfData>;
    }
</script>
