import CompanyInfo from 'shop/companyprofile/components/CompanyInfo';
import {cloneDeep} from 'lodash';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';

const draftPropsData = {
    headerData: {
        tagLine: 'Making the best apps since 2010',
        showFounded: true,
        foundedIn: 2010,
        companyWebsite: 'https://www.testcompany.com',
        linkedInProfileUrl: null,
        showCompanySize: true,
        companySize: {
            code: 'SMALL',
            value: '51-200 employees',
        },
        country: 'Germany',
        city: 'Munich'
    },
    aboutData: {
        description: 'Company description'
    },
    companyName: 'Test Company'
};

describe('CompanyInfo', () => {

    let propsData = {};
    beforeEach(() => {
        propsData = cloneDeep(draftPropsData);
    });

    it('displays founded data', () => {
        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.showFoundedIn).toBeTruthy();
        expect(wrapper.find('[data-id="container-company-founded"]').exists()).toBeTruthy();
    });

    it('does not display founded data when showFounded is false', () => {
        propsData.headerData.showFounded = false;

        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.showFoundedIn).toBeFalsy();
        expect(wrapper.find('[data-id="container-company-founded"]').exists()).toBeFalsy();
    });

    it('does not display founded data when not available', () => {
        propsData.headerData.foundedIn = null;

        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.showFoundedIn).toBeFalsy();
        expect(wrapper.find('[data-id="container-company-founded"]').exists()).toBeFalsy();
    });

    it('displays company size', () => {
        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.showCompanySize).toBeTruthy();
        expect(wrapper.find('[data-id="container-company-size"]').exists()).toBeTruthy();
    });

    it('does not display company size when showCompanySize is false', () => {
        propsData.headerData.showCompanySize = false;

        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.showCompanySize).toBeFalsy();
        expect(wrapper.find('[data-id="container-company-size"]').exists()).toBeFalsy();
    });

    it('does not display company size when  not available', () => {
        propsData.headerData.companySize = null;

        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.showCompanySize).toBeFalsy();
        expect(wrapper.find('[data-id="container-company-size"]').exists()).toBeFalsy();
    });

    it('creates headquarters with separator between city and country when both available', () => {
        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.headquarters).toEqual('Munich, Germany');
    });

    it('does not add separator to headquarters when only country is available', () => {
        propsData.headerData.city = null;
        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.headquarters).toEqual('Germany');
    });

    it('does not add separator to headquarters when only city is available', () => {
        propsData.headerData.country = null;
        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.vm.headquarters).toEqual('Munich');
    });

    it('retains description formatting', () => {
        const multiLineDescription = 'Line 1. \n Line 2.';
        propsData.aboutData.description = multiLineDescription;
        const wrapper = wrapperComponentFactory(CompanyInfo, {
            props: { ...propsData }
        });

        expect(wrapper.find('[data-id="text-company-description"]').text()).toEqual(multiLineDescription);
    });
});
