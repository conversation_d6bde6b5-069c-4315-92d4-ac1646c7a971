<template>
    <div>
        <h2 class="label mb-4">
            {{$t('shop.productDetails.gallerySection.title')}}
        </h2>
        <div class="detail-gallery">
            <img v-for="(image, index) in galleryImages" class="image"
                v-bind:src="image.url"
                v-on:click="openZoom(index)"/>
        </div>

        <portal to="portal-store">
            <gallery-slideshow v-bind:gallery-images="galleryImages" v-on:closeZoom="closeZoom" v-bind:index="index" v-if="zoomVisible"></gallery-slideshow>
        </portal>

    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {GalleryItemData} from 'common/generated-types/types';
    import {breakpoints} from 'common/breakpoints';
    import GallerySlideshow from './GallerySlideshow.vue';

    @Component({
        components: {
            GallerySlideshow,
        }
    })
    export default class GallerySection extends Vue {
        @Prop() galleryImages!: GalleryItemData[];

        zoomVisible = false;
        index = 0;
        isTabletOrDesktop = window.matchMedia(`(min-width: ${breakpoints.M}px)`);

        openZoom(index: number): void {
            if(this.isTabletOrDesktop.matches) {
                this.index = index;
                this.zoomVisible = true;
            }
        }

        closeZoom(): void {
            this.zoomVisible = false;
        }

    }
</script>

<style scoped lang="scss">
    @import "common/layout";

    .detail-gallery {
        display: grid;
        grid-gap: 24px;

        @include respond-to('M') {
            grid-template-columns: repeat(3, 1fr);
        }

        .image {
            max-width: 100%;
            max-height: 100%;
            box-shadow: 0 2px 48px 0 rgba(176, 192, 237, 0.16);
            cursor: pointer;

            &:hover {
                box-shadow: 0 2px 48px 0 rgba(176, 192, 237, 0.6);
            }
        }
    }
</style>
