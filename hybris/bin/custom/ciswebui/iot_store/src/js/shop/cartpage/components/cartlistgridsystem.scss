@import "node_modules/vuetify/src/styles/styles.sass"; // for $spacers
@import "node_modules/cd-system/tokens/scss/variables";// for cd-system $grid-breakpoints
@import "shop/core/logo";

.logo {
  @extend %-logo-mini-outer;
  overflow: hidden;

  div {
    @extend %-logo-mini-inner;
    border: 1px solid var(--v-grey-lighten3);
    border-radius: 8px;
  }
}

.product {
  margin-bottom: map-get($spacers, 4);
  .quantity, .discount-hint {
    @media screen and (min-width: map-get($grid-breakpoints, 'lg')) {
      margin-left: 106px;
    }
  }
  .quantity {
    @media screen and (max-width: map-get($grid-breakpoints, 'lg')) {
      margin-top: map-get($spacers, 4);
    }
  }
  .product-info-row {
    margin-top: -5px;
  }
}
