import util from 'common/util';

import {AxiosResponse} from "axios";
import {CreditCardPaymentInfoData, OrderPaymentUpdate, OrderPaymentUpdateData} from "common/generated-types/types";
let axios = util.axios;

const PAYMENT_UPDATE_INFOS_RESOURCE: (arg: string) => string =
    (orderCode: string) => `/shop/api/orders/${orderCode}/payment`;

export default class PaymentUpdateResource {
    getPaymentInfoData(orderCode: string): Promise<AxiosResponse<OrderPaymentUpdateData>> {
        const path = PAYMENT_UPDATE_INFOS_RESOURCE(orderCode);
        return axios.post<OrderPaymentUpdateData>(path);
    }

    createAndSetDefaultPaymentInfo(orderCode: string, paymentInfo: CreditCardPaymentInfoData): Promise<AxiosResponse<void>> {
        const path = `${PAYMENT_UPDATE_INFOS_RESOURCE(orderCode)}/create-card-and-set`;
        return axios.post(path, paymentInfo);
    }

    setDefaultPaymentInfo(orderCode: string, orderPaymentUpdate: OrderPaymentUpdate): Promise<AxiosResponse<void>> {
        const path = `${PAYMENT_UPDATE_INFOS_RESOURCE(orderCode)}/set`;
        return axios.post(path, orderPaymentUpdate);
    }
};
