import {AxiosResponse} from 'axios';
import {ShopRoute} from 'common/constants';
import axios from 'axios';

let customerStoreSelectionResource = {

    goToSelectedMarketPlace(selectedCountry: string, selectedLanguage: string): Promise<AxiosResponse<void>> {
        const requestPayload = {
            'countryIsocode': selectedCountry,
            'languageIsocode': selectedLanguage
        };
        return axios.post(ShopRoute.COUNTRY_STORE_API_RESOURCE, requestPayload);
    },
    goToCompanyDefaultMarketPlace(): Promise<AxiosResponse<void>> {
        return axios.post(ShopRoute.RETURN_TO_COMPANY_STORE_API_RESOURCE);
    }
};

export default customerStoreSelectionResource;
