import 'common/testtools/unit_tests_mock'
import Products from 'shop/products/Products'
import 'common/test-directive'
import productResource from 'shop/resources/productResource'
import productsDataTemplate from 'common/testtools/scenariosstore/productsData.json'
import coreDataDefault from 'common/testtools/scenariosstore/coreDataDefault.json'
import { cloneDeep } from 'lodash'
import { BaseStores, ShopRoute } from 'common/constants'
import { FacetGroup } from '../types'
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils'

jest.mock('common/util');
jest.mock('shop/resources/productResource');
jest.mock('common/components/spinner');

const $route = {
    path: '/path/to/products'
};

const $router = {
    push: jest.fn(() => Promise.resolve({data: {}}))
};

const mountProducts = (productsData) => wrapperComponentFactory(Products, {
    props: { productsData },
    mocks: { $route, $router },
    shallow: false
})

let coreData = {};
let productsData = {};

beforeEach(() => {
    coreData = cloneDeep(coreDataDefault);
    coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
    window.frontendData.coreData = coreData;

    productsData = cloneDeep(productsDataTemplate);
});

describe('Products App cards', () => {
    it('should have company name subs as text when company profile is not published', async () => {
        productsData.products.map(product => {
            product.company.hasPublishedProfile = false;
        });
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: productsData
        }));

        const wrapper = mountProducts(productsData);
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
        wrapper.vm.formattedProducts.map(app => expect(app.subAsLink).toBeFalsy());
    });

    it('should have company name subs as links when company profile is published', async () => {
        productsData.products.map(product => {
            product.company.hasPublishedProfile = true;
            product.company.profileUrl = '/profile-url/uid';
        });
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: productsData
        }));

        const wrapper = mountProducts(productsData);
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
        wrapper.vm.formattedProducts.map(app => expect(app.subAsLink).toBeTruthy());
    });

    it('should have company name subs as text when company profile is published and feature toggle disabled', async () => {
        productsData.products.map(product => {
            product.company.hasPublishedProfile = true;
            product.company.profileUrl = '/profile-url/uid';
        });
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: productsData
        }));
        coreData.moduleConfig.ENABLE_COMPANY_PROFILE = false;

        const wrapper = mountProducts(productsData);
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
        wrapper.vm.formattedProducts.map(app => expect(app.subAsLink).toBeFalsy());
    });

    describe('Products App cards chips', () => {
        beforeEach(() => {
            coreData.basestore = BaseStores.AA;
            productsData.products.map(product => {
                product.licenseRuntimes = ['runtime_subs_unlimited', 'runtime_full_unlimited'];
            });
            productResource.getProducts.mockImplementation(() => Promise.resolve({
                status: 200,
                data: productsData
            }));
        });

        it('to contain license runtimes chips when direct sales feature toggle is enabled', async () => {
            coreData.moduleConfig.DIRECT_SALES = true;

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            wrapper.vm.formattedProducts.map(
                app => expect(app.chips).toEqual(
                    expect.arrayContaining([
                        expect.objectContaining({text: 'shop.runtime.runtime_subs_unlimited'}),
                        expect.objectContaining({text: 'shop.runtime.runtime_full_unlimited'})
                    ])
                )
            );
        });

        it('to not contain license runtimes chips when direct sales feature toggle is disabled', async () => {
            coreData.moduleConfig.DIRECT_SALES = false;

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            wrapper.vm.formattedProducts.map(
                app => expect(app.chips).toEqual(
                    expect.not.arrayContaining([
                        expect.objectContaining({text: 'shop.runtime.runtime_subs_unlimited'}),
                        expect.objectContaining({text: 'shop.runtime.runtime_full_unlimited'})
                    ])
                )
            );
        });

        it('to not contain license runtimes chips when store is not AA', async () => {
            coreData.moduleConfig.DIRECT_SALES = true;
            coreData.basestore = BaseStores.AZENA;

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            wrapper.vm.formattedProducts.map(
                app => expect(app.chips).toEqual(
                    expect.not.arrayContaining([
                        expect.objectContaining({text: 'Subscription'}),
                        expect.objectContaining({text: 'One-time purchase'})
                    ])
                )
            );
        });
    });

    describe('Products App cards CTA url', () => {
        beforeEach(() => {
            coreData.basestore = BaseStores.AA;
            productResource.getProducts.mockImplementation(() => Promise.resolve({
                status: 200,
                data: productsData
            }));
        });

        it('to link to Product Selection page when direct sales feature toggle is enabled', async () => {
            coreData.moduleConfig.DIRECT_SALES = true;

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            wrapper.vm.formattedProducts.map(
                app => expect(app.url).toContain(ShopRoute.PRODUCT_SELECTION())
            );
        });

        it('to link to PDP when direct sales feature toggle is disabled', async () => {
            coreData.moduleConfig.DIRECT_SALES = false;

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            wrapper.vm.formattedProducts.map(
                app => expect(app.url).toContain("/shop/p/")
            );
        });

        it('to link to PDP when store is not AA', async () => {
            coreData.basestore = BaseStores.AZENA;
            coreData.moduleConfig.DIRECT_SALES = true;

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            wrapper.vm.formattedProducts.map(
                app => expect(app.url).toContain("/shop/p/")
            );
        });

        it('to link to login with redirect for anonymous users when direct sales feature toggle is enabled', async () => {
            coreData.moduleConfig.DIRECT_SALES = true;
            coreData.userName = null;

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            wrapper.vm.formattedProducts.map(
                app => expect(app.url).toContain('login?redirect')
            );
        });

        it('to not link to login with redirect for logged in users when direct sales feature toggle is enabled', async () => {
            coreData.moduleConfig.DIRECT_SALES = true;

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            wrapper.vm.formattedProducts.map(
                app => expect(app.url).not.toContain('login?redirect')
            );
        });

    });

    describe('Products App cards sorting', () => {
        it('AA products should be sorted by acquisition number', async () => {
            coreData.basestore = BaseStores.AA;
            productResource.getProducts.mockImplementation(() => Promise.resolve({
                status: 200,
                data: productsData
            }));

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            expect(wrapper.vm.lastExecutedQuery.sortBy).toEqual("acquisitionCount")
        });

        it('AZENA products should be sorted by publish date', async () => {
            coreData.basestore = BaseStores.AZENA;
            productResource.getProducts.mockImplementation(() => Promise.resolve({
                status: 200,
                data: productsData
            }));

            const wrapper = await mountProducts(productsData);
            await new Promise(process.nextTick);

            expect(wrapper.vm.formattedProducts).not.toHaveLength(0);
            expect(wrapper.vm.lastExecutedQuery.sortBy).toEqual("publishDate")
        });
    });

});

describe('Load more button', () => {
    it('products should be loaded if are available', async () => {

        productsData.totalNumberOfResults = 20;
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: productsData
        }));

        const wrapper = mountProducts(productsData);
        await wrapper.vm.$nextTick();

        wrapper.vm.appsLoading = false
        wrapper.vm.pageSpinner.isActive = false

        const getProducts = jest.spyOn(wrapper.vm, 'getProducts');
        wrapper.vm.loadProducts();

        expect(getProducts).toBeCalled();
    });

    it('is displayed when appsLoading is false and products are available', async () => {
        productsData.totalNumberOfResults = 100;
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: productsData
        }));

        const wrapper = mountProducts(productsData);
        await wrapper.vm.$nextTick();

        wrapper.vm.appsLoading = false;
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.displayLoadMoreButton).toBeTruthy();
        expect(wrapper.find('[data-id="button-load-more"]').exists()).toBeTruthy();
    });

    it('is not displayed if data is being loaded', async () => {
        productsData.totalNumberOfResults = 100;
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: productsData
        }));

        const wrapper = mountProducts(productsData);
        await wrapper.vm.$nextTick();

        wrapper.vm.appsLoading = true;
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.displayLoadMoreButton).toBeFalsy();
        expect(wrapper.find('[data-id="button-load-more"]').exists()).toBeFalsy();
    });

    it('is not displayed if all results have been loaded', async () => {
        productResource.getProducts.mockImplementation(() => Promise.resolve({
            status: 200,
            data: productsData
        }));

        const wrapper = mountProducts(productsData);
        await wrapper.vm.$nextTick();

        wrapper.vm.appsLoading = false;
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.displayLoadMoreButton).toBeFalsy();
        expect(wrapper.find('[data-id="button-load-more"]').exists()).toBeFalsy();
    });

    it('shows a button for filters on mobile breakpoint',async () => {
        const wrapper = mountProducts(productsData);
        wrapper.vm.$vuetify.breakpoint.smAndDown = true;
        await wrapper.vm.$nextTick();
        expect(wrapper.find('[data-id="button-toggle-filters"]').exists()).toBeTruthy();
    });

    it('shows a v-dialog of filters once the trigger button is clicked',async () => {
        const wrapper = mountProducts(productsData);
        wrapper.vm.$vuetify.breakpoint.smAndDown = true;
        await wrapper.vm.$nextTick();
        const mobileButton = wrapper.find('[data-id="button-toggle-filters"]');
        const mobileDialog = wrapper.find('[data-id="dialog-mobile-filters"]');
        await mobileButton.trigger('click');
        expect(mobileDialog.exists()).toBeTruthy();
    });

});

describe('getFacetItemLabel', () => {
    it('given translatable facet item then return translated label', () => {
        const wrapper = mountProducts(productsData)

        const facetItemLabel = wrapper.vm.getFacetItemLabel({
              name: 'runtime_subs_unlimited',
              facetIndex: FacetGroup.LICENSE_RUNTIMES.toString()
          }
        )

        expect(facetItemLabel).toBe('Subscription')
    })

    it('given non translatable facet then return label', () => {
        const wrapper = mountProducts(productsData)

        const facetItemLabel = wrapper.vm.getFacetItemLabel({
              name: 'Some name',
              facetIndex: FacetGroup.VEHICLE_TYPES.toString()
          }
        )

        expect(facetItemLabel).toBe('Some name')
    })
})
