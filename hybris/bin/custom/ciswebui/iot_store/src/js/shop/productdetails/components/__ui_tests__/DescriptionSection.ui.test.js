describe("DescriptionSection", () => {

    it("should break words and wrap dirty content with lots of &nbsp; characters", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-DescriptionSection');
        await page.setViewport({width: 1000, height: 400});
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot("DocumentsSection with wrapper dirty content");
    });

})
