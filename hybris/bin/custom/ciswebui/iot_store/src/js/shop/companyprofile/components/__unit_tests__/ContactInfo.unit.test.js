import ContactInfo from 'shop/companyprofile/components/ContactInfo';
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';

describe('ContactInfo', () => {
    it('renders support page url with target blank', async () => {
        const wrapper = wrapperComponentFactory(ContactInfo, {
            props: {
                contactData: {
                    supportPageUrl: 'https://support.com'
                }
            }
        });
        expect(wrapper.find('[data-id="link-support-page-url"]').element.getAttribute('target')).toBe('_blank');
    });
});
