import {mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import {GetOwnAppSection} from 'shop/productdetails/components';
import vuetify from 'common/plugins/brands/azena/vuetify';
import {LicenseType} from 'common/types';
import 'common/test-directive';

jest.mock('common/util');
jest.mock('shop/resources/licenseActivationResource');

const thirdPartyLicense = [
    {
        code: LicenseType.EVALUATION,
        currencyIsocode: 'USD',
        price: 0,
        licenseType: {
            code: LicenseType.EVALUATION,
            type: LicenseType.EVALUATION
        }
    },
    {
        code: LicenseType.FULL,
        currencyIsocode: 'USD',
        price: 50,
        licenseType: {
            code: LicenseType.FULL,
            type: LicenseType.FULL
        }
    }
];

const mountGetOwnAppSection = (licenses, downloadOwnAppsEnabled) => mount(GetOwnAppSection, {
    mocks: {
        $t: () => {
        },
        $te: () => {
        },
        $n: () => {
        }
    },
    data() {
        return {showChild: true}
    },
    vuetify,
    propsData: {
        product: {
            licenses: licenses,
            company: {
                friendlyName: null,
                name: 'Bosch Digital Commerce'
            },
            ownCompanyAppAndCanBuy: downloadOwnAppsEnabled
        }
    }
});

describe('Get own app section: ', () => {
    function verifyElementPresence(wrapper, presence) {
        expect(wrapper.find('.get-own-app-section').exists()).toBe(presence);
        expect(wrapper.find('.label').exists()).toBe(presence);
        expect(wrapper.find('.description').exists()).toBe(presence);
        expect(wrapper.find('.section-quantity').exists()).toBe(presence);
    }

    it('User has a permission to download own apps', () => {
        const wrapper = mountGetOwnAppSection(thirdPartyLicense, true);
        verifyElementPresence(wrapper, true);
    });

    it('User has no purchase permission to download own apps', () => {
        const wrapper = mountGetOwnAppSection(thirdPartyLicense, false);
        verifyElementPresence(wrapper, false);
    });
});
