<template>
    <div class="star-rating">
        <div v-for="n in 5"
             v-bind:key="n"
             class="star"
             v-bind:class="getClassForStar(n)">
        </div>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';

    @Component
    export default class StarRating extends Vue {
        @Prop() rating!: number;

        getClassForStar(n: number): string {
            const roundedRating = Math.round(this.rating * 2) / 2;
            if (roundedRating >= n) {
                return 'filled';
            }
            if (roundedRating >= n - 0.5) {
                return 'semi-filled';
            }
            return '';
        }
    };
</script>

<style scoped lang="scss" src="./starRating.scss"></style>
