import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import ReviewCreationForm from 'shop/productdetails/components/ReviewCreationForm';
import StarRatingSelection from 'shop/core/components/starrating/StarRatingSelection';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        ReviewCreationForm,
        StarRatingSelection
    },
    methods: {},
    template: `
      <test-page-root>
        <div style="padding: 60px;">
          <review-creation-form post-url="postUrl">
          </review-creation-form>
        </div>
      </test-page-root>
    `
});
