<template>
  <div v-if="isThirdPartyApp" class="requirements">
    <v-container :fluid="true" class="pa-0">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <v-row>
          <v-col cols="12">
            <h2 class="label">
              {{$t('shop.productDetails.appRequirementsSection.title')}}
            </h2>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" lg="4">
            <div class="requirement-item" data-id="text-requiredversion-app">
              <div class="item-title d-flex">
                <h4>{{$t('shop.productDetails.appRequirementsSection.requiredVersion')}}</h4>
                <tooltip class="info-hint ml-2"
                         v-bind:text="$t('shop.productDetails.appRequirementsSection.requiredVersionHint')">
                  <info-icon></info-icon>
                </tooltip>
              </div>
              <div class="item-content">
                <div class="requiredVersion">{{osCompatibility.minOsVersion}}</div>
                <span v-if="showText">{{$t('shop.productDetails.appRequirementsSection.orHigher')}}</span>
              </div>
            </div>
          </v-col>
          <v-col cols="12" lg="4">
            <div class="requirement-item" data-id="text-devicecapabilities-app">
              <div class="item-title d-flex">
                <h4>{{$t('shop.productDetails.appRequirementsSection.deviceCapabilities')}}</h4>
                <tooltip class="info-hint ml-2"
                         v-bind:text="$t('shop.productDetails.appRequirementsSection.deviceCapabilitiesHint')">
                  <info-icon></info-icon>
                </tooltip>
              </div>
              <div class="item-content">
                <div v-if="deviceCapabilities && deviceCapabilities.length">
                  <ul class="device-capabilties">
                    <li v-for="capability in deviceCapabilities" v-bind:key="capability.code">
                      <span>{{prepareName(capability.code)}}</span>
                    </li>
                  </ul>
                </div>
                <div v-else>
                  <p>{{$t('shop.productDetails.appRequirementsSection.noDeviceCapabilities')}}</p>
                </div>
              </div>
            </div>
          </v-col>
          <v-col cols="12" lg="4">
            <div class="requirement-item" data-id="text-permissions-app">
              <div class="item-title d-flex">
                <h4>{{ $t('shop.productDetails.appRequirementsSection.permissions') }}</h4>
                <tooltip class="info-hint ml-2"
                         v-bind:text="$t('shop.productDetails.appRequirementsSection.permissionsHint')">
                  <info-icon></info-icon>
                </tooltip>
              </div>
              <div class="item-content">
                <div v-if="permissions && permissions.length">
                  <ul class="product-permission">
                    <li v-for="permission in permissions" v-bind:key="permission.name">
                      <span>{{prepareName(permission.name)}}</span>
                    </li>
                  </ul>
                </div>
                <div v-else>
                  <p>{{$t('shop.productDetails.appRequirementsSection.noPermissionsRequired')}}</p>
                </div>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-container>
  </div>
  <div v-else class="requirements">
    <v-container :fluid="true" class="pa-0">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <v-row>
          <v-col cols="12">
            <h2 class="label">
              {{$t('shop.productDetails.toolRequirementsSection.title')}}
            </h2>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" lg="4">
            <div class="requirement-item" data-id="text-requiredversion-tool">
              <div class="item-title d-flex">
                <h4>{{$t('shop.productDetails.toolRequirementsSection.requiredVersion')}}</h4>
                <tooltip class="info-hint ml-2"
                         v-bind:text="$t('shop.productDetails.toolRequirementsSection.requiredVersionHint')">
                  <info-icon></info-icon>
                </tooltip>
              </div>
              <div class="item-content">
                <div class="requiredVersion">{{osCompatibility.minOsVersion}}</div>
                <span v-if="showText">{{$t('shop.productDetails.toolRequirementsSection.orHigher')}}</span>
              </div>
            </div>
          </v-col>
          <v-col cols="12" lg="4">
            <div class="requirement-item" data-id="text-devicecapabilities-tool">
              <div class="item-title d-flex">
                <h4>{{$t('shop.productDetails.toolRequirementsSection.deviceCapabilities')}}</h4>
                <tooltip class="info-hint ml-2"
                         v-bind:text="$t('shop.productDetails.toolRequirementsSection.deviceCapabilitiesHint')">
                  <info-icon></info-icon>
                </tooltip>
              </div>
              <div class="item-content">
                <div v-if="deviceCapabilities && deviceCapabilities.length">
                  <ul class="device-capabilties">
                    <li v-for="capability in deviceCapabilities" v-bind:key="capability.code">
                      <span>{{prepareName(capability.code)}}</span>
                    </li>
                  </ul>
                </div>
                <div v-else>
                  <p>{{$t('shop.productDetails.toolRequirementsSection.noDeviceCapabilities')}}</p>
                </div>
              </div>
            </div>
          </v-col>
          <v-col cols="12" lg="4">
            <div class="requirement-item" data-id="text-requiredversion-tool">
              <div class="item-title d-flex">
                <h4>{{ $t('shop.productDetails.toolRequirementsSection.permissions') }}</h4>
                <tooltip class="info-hint ml-2"
                         v-bind:text="$t('shop.productDetails.toolRequirementsSection.permissionsHint')">
                  <info-icon></info-icon>
                </tooltip>
              </div>
              <div class="item-content">
                <div v-if="permissions && permissions.length">
                  <ul class="product-permission">
                    <li v-for="permission in permissions" v-bind:key="permission.name">
                      <span>{{prepareName(permission.name)}}</span>
                    </li>
                  </ul>
                </div>
                <div v-else>
                  <p>{{$t('shop.productDetails.toolRequirementsSection.noPermissionsRequired')}}</p>
                </div>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeviceCapabilityData, OsCompatibilityData, PermissionData} from 'common/generated-types/types';
import InfoIcon from 'common/images/boxed/icon-24-info-b.svg';
import Tooltip from 'common/components/Tooltip.vue';

@Component({
  components: {
    Tooltip,
    InfoIcon
  }
})
export default class RequirementsSection extends Vue {
  @Prop() osCompatibility!: OsCompatibilityData;
  @Prop() permissions!: PermissionData[];
  @Prop() deviceCapabilities!: DeviceCapabilityData[];
  @Prop() isThirdPartyApp!: boolean;

  showText = false;

  mounted(): void {
    this.showText = this.osCompatibility.minOsVersion !== this.osCompatibility.maxOsVersion;
  }

  prepareName(name: string): string | undefined {
    if (name.length < 32) {
      return name;
    }
    return [...name.split('.')].pop();
  }
}
</script>

<style lang="scss" scoped>
  .requiredVersion {
    font-size: 56px !important;
    font-weight: 400;
    line-height: 1.3;
  }

  ul {
    padding-left: 0;
    list-style: none;
  }
</style>

