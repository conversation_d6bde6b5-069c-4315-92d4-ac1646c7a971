import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import OrderInvoices from 'shop/account/orderdetails/components/OrderInvoices';
import 'common/test-directive';
import orderDetailsWithInvoicesData from 'common/testtools/scenariosstore/orderDetailsWithInvoicesData.json';
import {cloneDeep} from 'lodash';
import en from 'common/i18n/en.json';
import {PaymentMethodType} from 'common/generated-types/types';

Vue.use(VueI18n)

const messages = {
    en: en
}
const i18n = {
    locale: 'en',
    messages
};

const mountOrderInvoices = (props) => mount(OrderInvoices, {
    i18n,
    propsData: {
        orderDetailsData: props.orderDetailsData,
    }
});

describe('OrderInvoices', () => {
    let propsData = {};
    beforeEach(() => {
        propsData = cloneDeep({
            orderDetailsData: orderDetailsWithInvoicesData
        });
    });

    it('displays order invoices', async () => {
        const wrapper = mountOrderInvoices(propsData);
        await wrapper.vm.$nextTick();

        const orderInvoicesTable = wrapper.find('[data-id="table-order-invoices"]');
        expect(orderInvoicesTable.exists()).toBeTruthy();
    });

    describe('Table title', () => {
        it('is Invoices by default', async () => {
            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.title).toEqual('Invoices');
        });

        it('is Documents when payment method is Invoice By Seller', async () => {
            propsData.orderDetailsData.paymentMethod = PaymentMethodType.INVOICE_BY_SELLER;

            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.title).toEqual('Documents');
        });

        it('is Documents when own apps order', async () => {
            propsData.orderDetailsData.ownAppsPurchase = true;

            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.title).toEqual('Documents');
        });
    });

    describe('Invoice document label', () => {
        it('is Invoice by default', async () => {
            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.invoiceDocumentLabel).toEqual('Invoice');
        });

        it('is Transaction Report when payment method is Invoice By Seller', async () => {
            propsData.orderDetailsData.paymentMethod = PaymentMethodType.INVOICE_BY_SELLER;

            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.invoiceDocumentLabel).toEqual('Transaction report');
        });

        it('is Transaction Report when own apps order', async () => {
            propsData.orderDetailsData.ownAppsPurchase = true;

            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.invoiceDocumentLabel).toEqual('Transaction report');
        });
    });

    describe('Total amount label', () => {
        it('is Total amount by default', async () => {
            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.totalAmountLabel).toEqual('Total amount');
        });

        it('is Net total amount when payment method is Invoice By Seller', async () => {
            propsData.orderDetailsData.paymentMethod = PaymentMethodType.INVOICE_BY_SELLER;

            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.totalAmountLabel).toEqual('Total net amount');
        });

        it('is Net total amount when own apps order', async () => {
            propsData.orderDetailsData.ownAppsPurchase = true;

            const wrapper = mountOrderInvoices(propsData);
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.totalAmountLabel).toEqual('Total net amount');
        });
    });

});
