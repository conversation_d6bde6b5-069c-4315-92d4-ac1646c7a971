<template>
  <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="help-container mt-16">
        <v-row>
          <v-col cols="12">
            <p class="text-h1">
              {{ $t('navigation.helpAndResources') }}
            </p>
          </v-col>
          <v-col cols="12">
            <CDButton target="_blank" v-if="globalHelpSupport" :href="globalHelpSupport.url" class="ma-2">
              {{ translateNavigation(globalHelpSupport) }}
            </CDButton>
            <CDButton target="_blank" v-if="globalHelpContact" :href="globalHelpContact.url" class="ma-2">
              {{ translateNavigation(globalHelpContact) }}
            </CDButton>
          </v-col>
          <v-col cols="12">
            <v-list>
              <v-list-item
                  class="mb-4"
                  v-for="item in helpEntries"
                  link
                  :key="item.id"
                  :href="item.url"
                  :target="item.target">

                <v-list-item-icon>
                  <img :src="item.icon"/>
                </v-list-item-icon>

                <v-list-item-content>
                  <v-list-item-title class="text-h3">{{ item.text }}</v-list-item-title>
                  <v-list-item-action-text class="text-body-1">{{ item.description }}</v-list-item-action-text>
                </v-list-item-content>

              </v-list-item>
            </v-list>
          </v-col>
        </v-row>
      </v-container>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {NavigationItemData} from 'common/generated-types/types';
import CompanyApps from 'shop/companyprofile/components/CompanyApps.vue';
import CompanyInfo from 'shop/companyprofile/components/CompanyInfo.vue';
import CompanyProfileHeader from 'shop/companyprofile/components/CompanyProfileHeader.vue';
import ContactInfo from 'shop/companyprofile/components/ContactInfo.vue';
import {navigationService} from 'common/services/navigationService';

@Component({
  components: {
    ContactInfo,
    CompanyProfileHeader,
    CompanyInfo,
    CompanyApps,
  }
})
export default class HelpAndResources extends Vue {
  navigationService = navigationService;

  translateNavigation(navigationItem : NavigationItemData) : string {
    return navigationService.translate(this.$i18n, navigationItem);
  }

  get globalHelpSupport() {
    return navigationService.byId('globalHelpSupport');
  }

  get globalHelpContact() {
    return navigationService.globalHelpContactItem();
  }

  get helpEntries(): NavigationItemData[] {
    return navigationService.helpItems();
  }
}
</script>
<style scoped lang="scss">
.v-list-item__action-text {
  color: var(--v-grey-darken3) !important;
}
.v-list-item {
  &:hover {
    background-color: var(--v-grey-lighten3);
  }
}
</style>
