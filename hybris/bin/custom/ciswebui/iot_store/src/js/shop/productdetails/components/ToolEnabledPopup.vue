<template>

  <div id="tool-enabled-popup" v-if="isOpen" v-on:click="close" v-on:keyup.esc="close" class="hidden-sm-and-down">
      <transition name="modal">
        <div class="modal-mask">
          <div class="modal-wrapper">
            <div class="modal-container" v-on:click.stop>
              <div class="modal-header">
                <h2>{{$t('shop.productDetails.pricingSection.license.tool.confirmationPopup.header')}}</h2>
              </div>

              <div class="modal-body">
                <div class="modal-body-item">
                  <p>{{$t('shop.productDetails.pricingSection.license.tool.confirmationPopup.contentLine1', { toolName: productName })}}</p>
                </div>

                <div class="modal-body-item">
                  <i18n path="shop.productDetails.pricingSection.license.tool.confirmationPopup.installInstructions" tag="p">
                    <template v-slot:linkToDMP>
                      <a :href="`${dmpLink}`" target="_blank" class="primary--text">{{ $t("shop.productDetails.pricingSection.license.tool.confirmationPopup.deviceManagementPortal") }}</a>
                    </template>
                  </i18n>
                  <i18n path="shop.productDetails.pricingSection.license.tool.confirmationPopup.learnMore" tag="p">
                    <template v-slot:linkToGuide>
                      <a :href="`${supportOnlineLink}`" target="_blank" class="primary--text">{{ $t("shop.productDetails.pricingSection.license.tool.confirmationPopup.guide") }}</a>
                    </template>
                  </i18n>
                </div>

                <div class="modal-body-item">
                  <i18n path="shop.productDetails.pricingSection.license.tool.confirmationPopup.offlineInstallInstructions" tag="p">
                    <template v-slot:linkToDMT>
                      <a :href="`${dmtLink}`" target="_blank" class="primary--text">{{ $t("shop.productDetails.pricingSection.license.tool.confirmationPopup.deviceManagementTool") }}</a>
                    </template>
                  </i18n>
                  <i18n path="shop.productDetails.pricingSection.license.tool.confirmationPopup.learnMore" tag="p">
                    <template v-slot:linkToGuide>
                      <a :href="`${supportOfflineLink}`" target="_blank" class="primary--text">{{ $t("shop.productDetails.pricingSection.license.tool.confirmationPopup.guide") }}</a>
                    </template>
                  </i18n>
                </div>

              </div>

              <div class="modal-footer">
                <CDButton
                    @click="close"
                    color="primary">
                  {{$t('shop.productDetails.pricingSection.license.tool.confirmationPopup.doneButton')}}
                </CDButton>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>

</template>


<script lang="ts">
import {Component, Emit, Prop, Vue, Watch} from 'vue-property-decorator';
import {navigationService} from 'common/services';
import {shopCoreDataProvider} from 'common/provider';
import {SupportUrlPaths} from 'common/constants';

@Component
export default class ToolEnabledPopup extends Vue {

  @Prop() show!: boolean;
  @Prop() productName!: string;

  isOpen = this.show;
  supportUrl = '';
  dmpUrl = '';

  mounted(): void {
    this.supportUrl = navigationService.byItemCode('globalSupport').url;
    this.dmpUrl = shopCoreDataProvider.data.camerasUrl;
  }

  get dmpLink(): string {
    return this.dmpUrl;
  }

  get dmtLink(): string {
    return `${this.dmpUrl}/toolDownload`;
  }

  get supportOnlineLink(): string {
    return `${this.supportUrl}${SupportUrlPaths.INSTALL_APPS_ONLINE}`;
  }

  get supportOfflineLink(): string {
    return `${this.supportUrl}${SupportUrlPaths.INSTALL_APPS_OFFLINE}`;
  }

  @Emit('modal-closed')
  close(): void {
    this.isOpen = false;
    document.removeEventListener('keyup', this.resolveKey);
  }

  @Watch('show')
  async onShowChanged(val: boolean): Promise<void> {
    if (val) {
      this.isOpen = true;
      document.addEventListener('keyup', this.resolveKey);
    }
  }

  resolveKey(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.close();
    }
  }
}
</script>

<style scoped lang="scss">
@import 'common/simpleDialog';
@import 'common/layout';
@import 'shop/core/common';

.v-application {
  #tool-enabled-popup {
    .modal-container {
      max-width: 640px;
      padding: 35px 32px;

      .modal-header {
        margin-bottom: 25px;

        h2 {
          font-size: 24px;
          font-weight: 600;
        }
      }

      .modal-body {
        .modal-body-item {
          margin-bottom: 25px;

          p {
            line-height: 1.5;
          }
        }
      }
    }
  }
}
</style>
