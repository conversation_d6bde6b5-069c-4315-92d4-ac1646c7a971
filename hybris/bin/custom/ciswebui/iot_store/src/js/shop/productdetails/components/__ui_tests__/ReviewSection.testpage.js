import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import {ReviewCreationForm, ReviewItem, ReviewSection} from 'shop/productdetails/components';
import StarRatingSelection from 'shop/core/components/starrating/StarRatingSelection';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    components: {
        TestPageRoot,
        ReviewCreationForm,
        ReviewSection,
        ReviewItem,
        StarRatingSelection
    },
    data: {
        reviews: [
            {
                'id': 1,
                'rating': 2.5,
                'showCompany': true,
                'showName': true,
                'alias': 'testName',
                'company': 'smart iot',
                'comment': '',
                'headline': 'a review',
                'date': '2020-04-01T15:14:13.664Z'
            }]
    },
    methods: {}
    ,
    template: `
      <test-page-root>
        <div style="padding: 60px;">
          <review-section reviews=""
                          post-review-url="postReviewUrl"
                          review-status="READY_FOR_REVIEW">
          </review-section>
            
          <review-section reviews=""
                          post-review-url="postReviewUrl"
                          review-status="NOT_BOUGHT_APP">
          </review-section>
            
          <review-section v-bind:reviews="reviews"
                          post-review-url="postReviewUrl"
                          review-status="REVIEW_IS_ALREADY_DONE">
          </review-section>
        </div>
      </test-page-root>
    `
});
