<template>
  <div class="app-facets">
    <v-dialog fullscreen scrollable v-model="mobileFilters" data-id="dialog-mobile-filters">
      <v-card>
        <v-card-title>
          <CDButtonTextIcon @click="mobileFilters = false" icon="$close">
            {{ $t('shop.products.facets.filters') }}
          </CDButtonTextIcon>
        </v-card-title>
        <v-card-text>
          <portal-target name="mobile-filter-portal"></portal-target>
        </v-card-text>
        <v-card-actions class="justify-end py-4">
          <CDButton @click="mobileFilters = false" class="mr-4">{{ $t('cancel') }}</CDButton>
          <CDButton color="primary" @click="mobileFilters = false">{{ $t('confirm') }}</CDButton>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <portal to="mobile-filter-portal" :disabled="!mobileFilters">
      <div v-for="(facet, i) in preparedFacets" class="facet-group mb-4" :key="i">
        <template v-if="facet.items.length > 0">
          <p class="text-h4 mb-1 ml-6"
             :data-id="`text-${facet.group}`">
            {{ getGroupLabel(facet.group) }}
          </p>
          <ul :class="facet.group" class="pl-0" v-if="activeGroups.includes(facet.group)">
            <li class="facet-item"
                v-for="(item, j) in facet.items"
                :class="{'active': isFacetSelected(item)}"
                v-on:click="onFacetSelection(item)" :key="j">
              <CDIcon dense color="primary">$complete</CDIcon>
              <span>{{ getValueLabel(item.name, facet.group) }} ({{ item.count }})</span>
            </li>
          </ul>
          <ul :class="facet.group" class="pl-0" v-else>
            <li class="facet-item"
                v-for="(item, k) in facet.items.slice(0, facetSize[facet.group])"
                :class="{'active': isFacetSelected(item)}"
                v-on:click="onFacetSelection(item)" :key="k">
              <CDIcon dense color="primary">$complete</CDIcon>
              <span>{{ getValueLabel(item.name, facet.group) }} ({{ item.count }})</span>
            </li>
            <a class="ml-6"
               v-if="showMoreVisibility(facet)  && !isHardwareRequirementsFacet(facet)"
               v-on:click="showMoreFacets(facet)"
               :data-id="`link-show-more-${facet.group}`">
              {{ $t('shop.products.facets.showMore') }}
            </a>
            <a class="ml-6"
               v-if="showMoreHardwareRequirementsVisibility && isHardwareRequirementsFacet(facet)"
               @click="hardwareDialog = true"
               :data-id="`link-show-more-${facet.group}`">
              {{ $t('shop.products.facets.showMore') }}
              <hardware-compatibility-dialog
                  v-model="hardwareDialog"
                  v-bind:selected-facet="hardwareRequirementsSelectedFacet"
                  v-bind:facet="facet"
                  v-on:facetSelection="onFacetSelection">
              </hardware-compatibility-dialog>
            </a>
          </ul>
        </template>
      </div>
    </portal>
  </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
    import {commercefacades, FacetData, FacetItem} from 'common/generated-types/types';
    import HeaderImage from 'common/images/boxed/application-store-header.svg';
    import {assignInWith, find, findIndex, orderBy, partition, slice} from 'lodash';
    import {LicenseType} from 'common/types';
    import {FacetGroup} from 'shop/products/types';
    import CountryData = commercefacades.CountryData;
    import {shopCoreDataProvider} from 'common/provider';
    import HardwareCompatibilityDialog from 'aa/shop/products/components/HardwareCompatibilityDialog.vue';

    @Component({
        components: {
            HardwareCompatibilityDialog,
            HeaderImage
        }
    })
    export default class ProductFilter extends Vue {
        @Prop() facets!: FacetData[];
        @Prop() currentCountry!: CountryData;
        @Prop() userCompanyId!: string;
        @Prop() initFacets!: FacetItem[];

        facetSize:{[key: string]: number} = {};
        facetSelection: FacetItem[] = [];
        availabilityFacetGroups = [FacetGroup.COUNTRY.toString(), FacetGroup.LICENSE_TYPE.toString(), FacetGroup.PRIVATE_APPS.toString()];
        slice = slice;
        isDirectSalesEnabled = Boolean(shopCoreDataProvider.data.moduleConfig.DIRECT_SALES);
        defaultGroupItems = this.isDirectSalesEnabled ? 3 : 8;
        hardwareDialog = false;
        mobileFilters = false;

        beforeMount(): void {
            this.facetSelection = this.initFacets;
        }

        @Watch('facets')
        onFacetsChange(): void {
            this.facets.forEach(facet => {
                if (this.availabilityFacetGroups.indexOf(facet.group) < 0) {
                    assignInWith(this.facetSize, {[facet.group]: this.defaultGroupItems});
                }
            });
        }

        onFacetSelection(selectedFacet: FacetItem): void {
            if (this.isFacetSelected(selectedFacet)) {
                this.facetSelection = this.facetSelection.filter(facet => facet.facetValue !== selectedFacet.facetValue);
            } else {
                if (this.availabilityFacetGroups.indexOf(selectedFacet.facetIndex) < 0) {
                    this.facetSelection = this.facetSelection.filter(facet => facet.facetIndex !== selectedFacet.facetIndex);
                }
                this.facetSelection.push(selectedFacet);
            }
            this.parent.$emit('on-facet-selection', this.facetSelection);
        }

        isFacetSelected(facet: FacetItem): boolean {
            return findIndex(this.facetSelection, {facetIndex: facet.facetIndex, facetValue: facet.facetValue}) >= 0;
        }

        get preparedFacets(): FacetData[] {
            const filterFacets = this.facets.filter(facet => this.filterFacetGroups.includes(facet.group));
            const partitionedFacet = partition(filterFacets, (facet) => {
                return this.availabilityFacetGroups.indexOf(facet.group) >= 0;
            });
            const availabilityFacet = this.getAvailabilityFacet(partitionedFacet[0]);
            const preparedFacets: FacetData[] = partitionedFacet[1];

            preparedFacets.push(availabilityFacet);

            return orderBy(preparedFacets, 'orderIndex');
        }

        // The Search result contains a list of all available facets, but in the Product Filter only a subset of these filters should be available
        get filterFacetGroups(): string[] {
          return [...this.availabilityFacetGroups, FacetGroup.INDUSTRIES.toString(),
            FacetGroup.USE_CASES.toString(),
            FacetGroup.HARDWARE_REQUIREMENTS.toString(),
            FacetGroup.VEHICLE_TYPES.toString(),
            ...this.isDirectSalesEnabled ? [FacetGroup.FIRST_LEVEL_CATEGORIES.toString(), FacetGroup.LICENSE_RUNTIMES.toString()]
                : [FacetGroup.PACKAGES.toString(), FacetGroup.LICENSES.toString()]
          ];
        }

      showMoreVisibility(facet: FacetData): boolean {
            return facet.items.length > this.defaultGroupItems && facet.items.length !== this.facetSize[facet.group];
        }

      getGroupLabel(group: string): string {
        const i18nKey = `shop.products.facets.group.${group.toLowerCase()}.name`;
        return this.$t(i18nKey).toString();
      }

      getValueLabel(value: string, group: string): string {
        const i18nKey = `shop.products.facets.group.${group.toLowerCase()}.values.${value.toLowerCase()}`;
        if (this.$te(i18nKey) || this.$te(i18nKey, 'en')) {
          return this.$t(i18nKey).toString();
        } else {
          return value;
        }
      }

      showMoreFacets(facet: FacetData): void {
            this.$nextTick(() => {
                this.facetSize[facet.group] = facet.items.length;
                this.$forceUpdate();
            });
        }

        get activeGroups(): string[] {
            return this.initFacets.map(facet => facet.facetIndex);
        }

        get hardwareRequirementsFacet(): FacetData {
            return this.preparedFacets.filter(facet => this.isHardwareRequirementsFacet(facet))[0];
        }

        isHardwareRequirementsFacet(facet: FacetData): boolean {
            return FacetGroup.HARDWARE_REQUIREMENTS === facet.group;
        }

        showMoreHardwareRequirementsVisibility(facet: FacetData): boolean {
            return this.hardwareRequirementsFacet.items.length > 3
                && this.hardwareRequirementsFacet.items.length !== this.facetSize[this.hardwareRequirementsFacet.group]
                && FacetGroup.HARDWARE_REQUIREMENTS === facet.group;
        }

        get hardwareRequirementsSelectedFacet(): FacetItem {
              return this.facetSelection.filter(facetItem => FacetGroup.HARDWARE_REQUIREMENTS === facetItem.facetIndex)[0];
        }

        get parent(): Vue {
            // TSC gives an errors: property '$emit' does not exist on type 'never'.
            // A workaround would be to make an extension type for this property.
            type VueExt = Vue;
            return this.$parent as VueExt;
        }


      private getAvailabilityFacet(facets: FacetData[]): FacetData {
            const availabilityFacet: FacetData = {
                group: this.$t('shop.products.facets.group.availability.name').toString(),
                orderIndex: 1,
                items: []
            };

            if (this.currentCountry) {
                const countryFacet = find(facets, {group: FacetGroup.COUNTRY});
                if (countryFacet) {
                    const appsInCountry = find(countryFacet.items, {name: this.currentCountry.isocode});
                    if (appsInCountry) {
                        appsInCountry.name = this.$t('shop.products.facets.availability.country').toString();
                        availabilityFacet.items.push(appsInCountry);
                    }
                }

                const licenseFacet = find(facets, {group: FacetGroup.LICENSE_TYPE});
                if (licenseFacet) {
                    const trialApps = find(licenseFacet.items, {name: LicenseType.EVALUATION});
                    if (trialApps) {
                        trialApps.name = this.$t('shop.products.facets.availability.trial').toString();
                        availabilityFacet.items.push(trialApps);
                    }
                }

                const privateAppFacet = find(facets, {group: FacetGroup.PRIVATE_APPS});
                if (privateAppFacet) {
                  const privateApps = find(privateAppFacet.items, {name: this.userCompanyId});
                  if (privateApps) {
                    privateApps.name = this.$t('shop.products.facets.availability.privateapps').toString();
                    availabilityFacet.items.push(privateApps);
                  }
                }
            }

            return availabilityFacet;
        }
    }
</script>

<style scoped lang="scss">
@import "shop/core/constants";
@import "common/utilities";
@import "aa/shop/core/dialogs";

.facet-group {
  label {
    cursor: pointer;
  }
}

.v-card__text {
  color: inherit !important;
}

ul {
  li {
    display: flex;
    align-content: center;
    font-weight: normal;
    text-transform: none;
    user-select: none;
    cursor: pointer;
    align-items: center;

    :deep(.cd-icon-complete) {
      visibility: hidden;
    }

    &.active {
      font-weight: 500;
      color: var(--v-primary-base);
      :deep(.cd-icon-complete) {
        visibility: visible;
      }
    }
  }
}
</style>
