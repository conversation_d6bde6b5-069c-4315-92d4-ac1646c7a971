import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {createPinia, setActivePinia} from 'pinia';
import {useProductSelectionStore} from '../index';
import productResource from 'shop/resources/productResource';

jest.mock('common/util');
jest.mock('shop/resources/productResource');


describe('Product Selection Store', () => {
    let product = {
        code: "product_code"
    };
    beforeEach(() => {
        setActivePinia(createPinia());
        productResource.getProduct.mockImplementation(() => Promise.resolve({
            status: 200,
            data: product
        }));
    })

    it('fetch product action invokes product resource and sets result to state', async () => {
        const productSelectionStore = useProductSelectionStore();

        await productSelectionStore.fetchProduct(product.code);

        expect(productResource.getProduct).toHaveBeenCalledWith(product.code);
        expect(productSelectionStore.product).toBe(product);
    });

});
