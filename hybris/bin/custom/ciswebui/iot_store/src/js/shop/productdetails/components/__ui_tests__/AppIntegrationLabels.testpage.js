import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import {AppIntegrationLabels} from 'shop/productdetails/components';
import {AppIntegrationType, StandardAppIntegrationType} from '../../../../common/generated-types/types';

Vue.use(VueI18n);

const appIntegrations = [
    {
        displayName: 'IoT Gateway',
        externalDescription: 'Shouldn\'t be displayed',
        integrationType: StandardAppIntegrationType.GATEWAY,
        type: AppIntegrationType.STANDARD,
        documentation: null
    },
    {
        displayName: 'IoT Gateway',
        externalDescription: 'Shouldn\'t be displayed',
        integrationType: null,
        type: AppIntegrationType.CUSTOM,
        documentation: {
            url: 'irrelevant',
            displayName: 'does not matter',
            size: 9938
        }
    },
    {
        displayName: 'ONVIF Standard',
        externalDescription: 'Shouldn\'t be displayed',
        integrationType: StandardAppIntegrationType.STANDARD,
        type: AppIntegrationType.STANDARD
    }];

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    data: {
        appIntegrations: appIntegrations
    },
    components: {
        TestPageRoot,
        AppIntegrationLabels
    },
    methods: {},
    template: `
      <test-page-root>
        <div style="padding: 60px;">
          <app-integration-labels
              v-bind:app-integrations="appIntegrations">
          </app-integration-labels>
        </div>
      </test-page-root>
    `
});
