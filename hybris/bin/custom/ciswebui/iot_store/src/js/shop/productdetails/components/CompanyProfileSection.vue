<template>
     <v-container :fluid="true" data-id="container-company-profile" class="pa-0" v-if="showCompanyProfile">
            <v-container :fluid="$vuetify.breakpoint.lgAndDown">
                <v-row>
                    <v-col class="mt-16" cols="12">
                        <h2 class="label">{{$t('shop.productDetails.companyProfileSection.title')}}</h2>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" xl="9">
                        <company-info :header-data="companyProfileData.headerData" :company-name="companyName" :profile-url="profileUrl">
                        </company-info>
                    </v-col>
                </v-row>

            </v-container>
        </v-container>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {CompanyProfileData} from 'common/generated-types/types';
    import {CompanyInfo} from 'shop/productdetails/components/companyprofile';
    import {appCompanyProfileResource} from 'shop/resources';
    import {messageService} from 'common/services';
    import {isEmpty} from 'lodash';

    @Component({
        components: {
            CompanyInfo
        }
    })
    export default class CompanyProfileSection extends Vue {
        @Prop() appCode!: string;
        @Prop() profileUrl!: string;

        companyProfileData = {} as CompanyProfileData ;
        async mounted(): Promise<void> {
            await this.getCompanyProfile();
        }

        get companyName(): string {
            const company = this.companyProfileData.companyData;
            return company?.friendlyName || company?.name;
        }

        getCompanyProfile(): Promise<void> {
            return appCompanyProfileResource.getCompanyProfile(this.appCode).then(response => {
                this.companyProfileData = response.data;
            }).catch(error => {
                messageService.errorResponse(error.response?.data, this.$i18n);
            });
        }
        get showCompanyProfile(): boolean {
            return !isEmpty(this.companyProfileData!.companyData) && !isEmpty(this.companyProfileData!.headerData);
        }
    }
</script>
