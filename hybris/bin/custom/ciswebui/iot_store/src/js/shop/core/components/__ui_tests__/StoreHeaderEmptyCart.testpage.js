import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueRouter from 'vue-router';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import StoreHeader from 'shop/core/components/StoreHeader';
import {storeNavigations} from 'common/testtools/testPageCreator.ts';
import vuetify from 'common/plugins/brands/azena/vuetify';
import pinia from 'shop/store';
import router from 'shop/core/routing';

Vue.use(VueRouter);

window.frontendData.coreData.baseUrl = '/shop/';
window.frontendData.coreData.navigationItems = storeNavigations;

new Vue({
    el: '#vue-app',
    i18n: {
        locale: 'en'
    },
    vuetify,
    router,
    components: {
        TestPageRoot,
        StoreHeader,
    },
    pinia,
    template: `
      <test-page-root>
        <store-header v-bind:cameras-url="'myCamerasUrl'"
            v-bind:my-apps-url="'myAppsUrl'"
            base-url="/"
            v-bind:user-name="'someUser'"
            v-bind:show-cart-icon="true"
            v-bind:page-title="'Application Store'"
            v-bind:home-title="'Device Management Portal'" 
            v-bind:number-of-cart-items="0">
        </store-header>
      </test-page-root>
    `
});
