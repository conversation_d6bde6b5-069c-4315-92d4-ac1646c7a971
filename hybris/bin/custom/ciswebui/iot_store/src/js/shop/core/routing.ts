import VueRouter from 'vue-router';
import {ShopRoute} from 'common/constants';
import {StoreDataProvider, type StoreFrontendData} from 'shop/resources';

import Products from 'shop/products/Products.vue';
import Tools from 'shop/tools/Tools.vue';
import ProductDetails from 'shop/productdetails/ProductDetails.vue';
import UpdatePaymentMethodPage from 'shop/checkout/UpdatePaymentMethodPage.vue';
import CheckoutPaymentPage from 'shop/checkout/CheckoutPaymentPage.vue';
import ExportInformationOverview from 'shop/exportinformation/ExportInformationOverview.vue';
import OrderHistory from 'shop/account/orderhistory/OrderHistory.vue';
import OrderDetails from 'shop/account/orderdetails/OrderDetails.vue';
import PaymentDetails from 'shop/account/payment/PaymentDetails.vue';
import CheckoutConfirmation from 'shop/checkoutconfirmation/CheckoutConfirmation.vue';
import CartPage from 'shop/cartpage/CartPage.vue';
import CompanyProfile from 'shop/companyprofile/CompanyProfile.vue';
import HelpAndResources from 'shop/helpandresources/HelpAndResources.vue';

const props = (): StoreFrontendData => ({ ...StoreDataProvider.getMergedFrontendData() });

const router = new VueRouter({
    mode: 'history',
    routes: [{
        path: ShopRoute.CART,
        component: CartPage,
        props
    },{
        path: ShopRoute.PRODUCTS,
        component: Products,
        meta: {
            appsActive: true
        },
        props
    },{
        path: ShopRoute.TOOLS,
        component: Tools,
        meta: {
            toolsActive: true
        },
        props
    },{
        path: ShopRoute.PRODUCT_DETAIL,
        name: ShopRoute.PRODUCT_DETAIL_NAME,
        component: ProductDetails,
        props
    }, {
        path: ShopRoute.UPDATE_PAYMENT_METHOD,
        component: UpdatePaymentMethodPage,
        props
    }, {
        path: ShopRoute.CHECKOUT_PAYMENT,
        component: CheckoutPaymentPage,
        props
    },{
        path: ShopRoute.EXPORT_INFORMATION_OVERVIEW,
        component: ExportInformationOverview,
        props
    }, {
        path: ShopRoute.ORDER_HISTORY,
        component: OrderHistory,
        props
    }, {
        path: ShopRoute.ORDER_DETAILS,
        component: OrderDetails,
        props
    }, {
        path: ShopRoute.PAYMENT_DETAILS,
        component: PaymentDetails,
        props
    }, {
        path: ShopRoute.CHECKOUT_CONFIRMATION,
        component: CheckoutConfirmation,
        props
    }, {
        path: ShopRoute.COMPANY_PROFILE,
        component: CompanyProfile,
        props
    }, {
        path: ShopRoute.HELP_AND_RESOURCES,
        component: HelpAndResources,
        props
    }]
});

export default router;
