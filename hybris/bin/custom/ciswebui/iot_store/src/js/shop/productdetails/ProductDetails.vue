<template>
  <div class="product-details">

    <template v-if="isMaintenanceOn">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="header-notification">
        <v-row>
          <v-col cols="12">
            <CDNotificationPersistent type="info" class="alert-info mb-0 mt-4" :dismissible="true">
              <p class="mb-0"><strong>{{$t('shop.productDetails.productHeader.maintenance.title')}}</strong></p>
              <p class="mb-0">{{$t('shop.productDetails.productHeader.maintenance.subtitle')}}</p>
            </CDNotificationPersistent>
          </v-col>
        </v-row>
      </v-container>
    </template>

    <template v-if="!userPermissionService.isAnonymous() && !isMaintenanceOn">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="header-notification">
        <v-row v-if="readOnly">
          <v-col cols="12">
            <CDNotificationPersistent type="info" class="alert-info mb-0 mt-4">
              <p class="mb-0">{{$t('shop.productDetails.productHeader.noLicense')}}</p>
            </CDNotificationPersistent>
          </v-col>
        </v-row>
        <v-row v-else-if="displayCountryPermissionAlert">
          <v-col>
            <CDNotificationPersistent type="info" class="alert-info mb-0 mt-4">
              <p class="mb-0">{{$t('shop.productDetails.pricingSection.countryPermissionText')}}</p>
              <template v-slot:append>
                <a target="_blank" rel="noopener noreferrer" v-bind:href="supportUrl + '/en-us/articles/************'">{{$t('shop.productDetails.pricingSection.learnMore')}}</a>
              </template>
            </CDNotificationPersistent>
          </v-col>
        </v-row>
        <v-row v-else-if="userCountryBlockedInDeveloperCountry">
          <v-col>
            <CDNotificationPersistent type="info" class="alert-info mb-0 mt-4">
              <p class="mb-0">{{$t('shop.productDetails.pricingSection.integratorCountryBlockedText')}}</p>
              <template v-slot:append>
                <a target="_blank" rel="noopener noreferrer" v-bind:href="supportUrl">{{$t('shop.contactCustomerSupport')}}</a>
              </template>
            </CDNotificationPersistent>
          </v-col>
        </v-row>
      </v-container>
    </template>

    <product-header class="product-details-header product-details-component py-2 py-sm-6"
                    :product-name="pageData.name"
                    :company="pageData.company"
                    :logo-url="pageData.logoUrl"
                    v-on:licenseButtonClicked="onPurchaseButtonClicked"
                    :licenses="pageData.licenses"
                    :average-rating="pageData.averageRating"
                    :number-of-reviews="pageData.numberOfReviews"
                    :number-of-installs="pageData.acquisitionCount"
                    :read-only="readOnly"
                    :store-availability-mode="pageData.storeAvailabilityMode"
                    :code="pageData.code"
                    :app-integrations="pageData.appIntegrations"
                    :available-in-user-country="availableInUserCountry"
                    :isThirdPartyApp="isThirdPartyApp"
                    :short-description="pageData.shortDescription"
                    :video="pageData.video">
    </product-header>

    <v-container :fluid="true" class="product-details-component">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <v-row>
          <v-col cols="12">
            <div class="product-details-title">
              <h2 class="label mb-4 mt-0">{{$t('shop.productDetails.descriptionSection.label')}} {{pageData.name}}</h2>
            </div>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" xl="8">
            <description-section class="product-details-description"
                                 :product-description="pageData.description"
                                 :product-name="pageData.name">
            </description-section>

            <gallery-section class="product-details-gallery mb-16 mt-16"
                             :gallery-images="pageData.galleryItems">
            </gallery-section>

            <changelog-section class="product-details-changelog mb-16"
                               :changelogs="pageData.changelogs">
            </changelog-section>
          </v-col>

          <v-col cols="12" xl="3" offset-xl="1">
            <side-bar class="product-details-sidebar"
                     :app-code="pageData.code"
                     :version-name="pageData.versionName"
                     :company="pageData.company"
                     :enabled-countries="pageData.enabledCountries"
                     :app-contact-info="pageData.appContactInfo"
                     :is-third-party-app="isThirdPartyApp"
                     :is-private-app="isPrivateApp">
              <follow-app-changes-section class="product-details-follow-app"
                                          v-if="!userPermissionService.isAnonymous()">
              </follow-app-changes-section>
            </side-bar>
          </v-col>
        </v-row>
      </v-container>
    </v-container>

    <integrations-section id="product-details-integrations"
                          class="product-details-component product-details-integrations"
                          v-if="hasAppIntegrations"
                          :corporate-home="coreData.corporateHome"
                          :app-integrations="pageData.appIntegrations"
                          :app-contact-info="pageData.appContactInfo">
    </integrations-section>

    <documents-section class="product-details-component product-details-documents"
                       v-if="documentsPresent"
                       :pdf-data-list="pageData.pdfDocuments">
    </documents-section>

    <requirements-section class="product-details-requirements product-details-component"
                          :permissions="pageData.permissions"
                          :os-compatibility="pageData.osCompatibility"
                          :device-capabilities="pageData.deviceCapabilities"
                          :is-third-party-app="isThirdPartyApp">
    </requirements-section>

    <pricing-section class="product-details-component product-details-pricing"
                     ref="pricingSelection"
                     :product="pageData"
                     :not-available-in-country="!readOnly && !availableInUserCountry"
                     :display-licenses="displayLicenses"
                     :is-trial="isTrial">
    </pricing-section>

    <get-own-app-section class="product-details-component product-details-get-own-app"
                      ref="getOwnAppSection"
                      :product="pageData">
    </get-own-app-section>

    <review-section class="product-details-reviews product-details-component"
                    :reviews="pageData.reviews"
                    :post-review-url="pageData.postReviewUrl"
                    :review-status="pageData.reviewStatus"
                    :full-license-disabled="fullLicenseDisabled"
                    v-if="isThirdPartyApp">
    </review-section>

    <dual-use-section class="product-details-component product-details-dual-use"
                      v-if="pageData.dualUse"
                      :info-text="pageData.dualUseInfoText">
    </dual-use-section>

    <support-section class="product-details-component product-details-support"
                     v-if="!userPermissionService.isAnonymous()"
                     :support-data="pageData.appContactInfo">
    </support-section>
    <company-profile-section
        v-if="displayCompanyProfileSection"
        :app-code="pageData.code"
        :profile-url="pageData.company.profileUrl">
    </company-profile-section>

    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {DetailProductData, ShopCoreData} from 'common/generated-types/types';
    import {AlertStaticImportant, MissingPermissionsHint} from 'common/components';
    import {YoutubePlayer} from 'common/components/videoplayers';
    import {LicenseType} from 'common/types';
    import {
      ChangelogSection,
      CompanyProfileSection,
      DescriptionSection,
      IntegrationsSection,
      DocumentsSection,
      DualUseSection,
      GallerySection,
      PricingSection,
      ProductHeader,
      RequirementsSection,
      ReviewSection,
      SideBar,
      SupportSection,
      FollowAppChangesSection,
      GetOwnAppSection
    } from 'shop/productdetails/components';
    import {productDataService, userPermissionService} from 'common/services';
    import {isEmpty} from 'lodash';

    @Component({
        components: {
            CompanyProfileSection,
            ProductHeader,
            GallerySection,
            ChangelogSection,
            PricingSection,
            GetOwnAppSection,
            DescriptionSection,
	          IntegrationsSection,
            DocumentsSection,
            ReviewSection,
            RequirementsSection,
            SupportSection,
            DualUseSection,
            FollowAppChangesSection,
            SideBar,
            MissingPermissionsHint,
            AlertStaticImportant,
            YoutubePlayer
        }
    })
    export default class ProductDetails extends Vue {
        @Prop() pageData!: DetailProductData;
        @Prop() coreData!: ShopCoreData;

        LicenseType = LicenseType;
        userPermissionService = userPermissionService;

        isMaintenanceOn = Boolean(this.coreData.moduleConfig.STORE_MAINTENANCE_BANNER);
        isCompanyProfileFeatureOn = Boolean(this.coreData.moduleConfig.ENABLE_COMPANY_PROFILE);


        $refs!: {
            pricingSelection: PricingSection;
        };

        created(): void {
            this.setPageTitle();
        }

        private setPageTitle(): void {
            document.title = this.pageData.name + ' – ' + this.$t('navigation.storePageTitle') as string;
        }

        onPurchaseButtonClicked(licenseType: LicenseType): void {
            if(userPermissionService.isAnonymous()) {
                window.location.assign(this.coreData.baseUrl + 'login');
            } else {
                this.$refs.pricingSelection.onPurchaseButtonClicked(licenseType);
            }
        }

        get fullLicenseDisabled(): boolean {
            return productDataService.fullLicenseDisabled(this.pageData);
        }

        get documentsPresent(): boolean {
            return !isEmpty(this.pageData.pdfDocuments);
        }

        get readOnly(): boolean {
            return productDataService.readOnly(this.pageData);
        }

        get availableInUserCountry(): boolean {
            return productDataService.availableInCountry(this.pageData);
        }

        get userCountryBlockedInDeveloperCountry(): boolean {
            return this.pageData.integratorCountryBlockedInDeveloperCountry;
        }

        get displayLicenses(): boolean {
            return productDataService.availableInCountry(this.pageData) && !isEmpty(this.pageData.licenses);
        }

        get displayCountryPermissionAlert(): boolean {
            return !this.availableInUserCountry;
        }

        get isTrial(): boolean {
            return productDataService.trialOnly(this.pageData);
        }

        get isThirdPartyApp(): boolean {
            return !productDataService.isToolApp(this.pageData);
        }

        get isPrivateApp(): boolean {
          return productDataService.isPrivate(this.pageData);
        }

        get hasAppIntegrations(): boolean {
            return !isEmpty(this.pageData.appIntegrations);
        }

        get supportUrl(): string {
          return this.coreData.supportUrl;
        }

        get displayCompanyProfileSection(): boolean {
            return this.isCompanyProfileFeatureOn && this.pageData.company?.hasPublishedProfile;
        }
};
</script>

<style lang="scss">
@import "common/vuetify";
.product-details-component {
  padding: 40px 0;
  @include isDesktop() {
    padding: 70px 0 110px 0;
  }
  &:nth-child(odd) {
    background-color: var(--v-grey-lighten4);
  }
}
.header-notification {
  &:empty {
    display: none;
  }
}
</style>
