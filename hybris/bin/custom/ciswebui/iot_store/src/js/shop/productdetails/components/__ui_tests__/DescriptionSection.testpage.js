import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import { DescriptionSection } from 'shop/productdetails/components'

new Vue({
    el: '#vue-app',
    vuetify,
    data: {
        productName: 'Mask Detection',
        productDescription: '<div data-v-a3858b1c="" class="rich-text"><p>Under&nbsp;the&nbsp;severe&nbsp;situation&nbsp;of&nbsp;COVID-19,&nbsp;it’s&nbsp;important&nbsp;to&nbsp;take&nbsp;pandemic&nbsp;prevention&nbsp;measures&nbsp;in&nbsp;the&nbsp;high&nbsp;traffic&nbsp;density&nbsp;sites.&nbsp;We&nbsp;use&nbsp;AI&nbsp;algorithms&nbsp;to&nbsp;do&nbsp;Face&nbsp;Mask&nbsp;Detection&nbsp;and&nbsp;People&nbsp;Counting&nbsp;in&nbsp;real&nbsp;time&nbsp;with&nbsp;high&nbsp;accuracy.</p><p>It&nbsp;can&nbsp;be&nbsp;used&nbsp;at&nbsp;transport&nbsp;terminals,&nbsp;retail&nbsp;stores,&nbsp;commercial&nbsp;buildings,&nbsp;stadium&nbsp;and&nbsp;entertainment.&nbsp;With&nbsp;the&nbsp;Face&nbsp;Mask&nbsp;wearing&nbsp;status,&nbsp;real&nbsp;time&nbsp;display&nbsp;of&nbsp;people&nbsp;counting,&nbsp;the&nbsp;site&nbsp;supervisor&nbsp;can&nbsp;prevent&nbsp;pandemic&nbsp;by&nbsp;the&nbsp;taking&nbsp;measures&nbsp;such&nbsp;as&nbsp;caution&nbsp;announcements.</p></div>'
    },
    components: {
        TestPageRoot,
        DescriptionSection
    },
    template: `
      <test-page-root>
        <description-section
            v-bind:product-name="productName"
            v-bind:product-description="productDescription">
        </description-section>
      </test-page-root>
    `
})
