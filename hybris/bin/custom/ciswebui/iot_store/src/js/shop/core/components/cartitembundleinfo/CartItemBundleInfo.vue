<template>
  <span v-if="bundleAvailable">
    <span class="text-h4"
          data-id="text-bundle-info">
      {{ bundleLabel }}
    </span>
    <span>
      –
    </span>
  </span>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {BundleInfoData} from 'common/generated-types/types';
import {bundleInfoService} from 'common/services';

@Component({
  components: {}
})
export default class CartItemBundleInfo extends Vue {
  @Prop() bundleInfo!: BundleInfoData;

  get bundleAvailable(): boolean {
    return !!this.bundleInfo?.code;
  }

  get bundleLabel(): string {
    return bundleInfoService.getBundleLabel(this.bundleInfo, this.$i18n);
  }

}
</script>

