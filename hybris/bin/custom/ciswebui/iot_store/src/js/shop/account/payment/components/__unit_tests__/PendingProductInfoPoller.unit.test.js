import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils'
import PendingProductInfoDraftPoller from 'shop/account/payment/components/PendingProductInfoDraftPoller.vue'
import {paymentResource} from 'shop/resources'

jest.mock('common/util');
jest.mock('shop/resources/paymentResource');

const mountComponent = (paymentInfoList) => wrapperComponentFactory(PendingProductInfoDraftPoller, {
    props: {
        pendingPaymentInfoDrafts: paymentInfoList
    },
    shallow: false,
    mocks: {
        $t: () => {
        }
    }
});

describe('PendingProductInfoDraftPoller', () => {

    beforeEach(() => {
        jest.useFakeTimers();
        jest.spyOn(global, 'setTimeout');
    });

    afterEach(() => {
        jest.useRealTimers()
        jest.clearAllMocks();
    });

    it('should schedule request to finalize payment info draft if pending payment info drafts', () => {
        const pendingPaymentInfoCode = 'pending_payment_info_list'
        const pendingPaymentInfoDrafts = [{code: pendingPaymentInfoCode}]
        const methodMock = paymentResource.finalizePendingPaymentInfosCreation.mockImplementation(() => Promise.resolve({}));

        mountComponent(pendingPaymentInfoDrafts);

        expect(setTimeout).toHaveBeenCalledTimes(1);
        expect(setTimeout).toHaveBeenCalledWith(expect.any(Function), 5000);

        jest.runOnlyPendingTimers();
        expect(methodMock).toHaveBeenCalledTimes(1);
        expect(methodMock).toHaveBeenCalledWith([pendingPaymentInfoCode]);
    });

    it('should not schedule request to finalize payment info draft if no pending payment info drafts', () => {
        mountComponent([]);

        expect(setTimeout).toHaveBeenCalledTimes(0);
    });
});

