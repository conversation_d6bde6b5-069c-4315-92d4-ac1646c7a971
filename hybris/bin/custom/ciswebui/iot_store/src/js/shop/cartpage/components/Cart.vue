<template>
    <div class="root" v-if="cartsInternal.length > 0">
        <checkout-responsive-grid-layout>
            <template v-slot:main-content-1>
                <h2 class="mb-6">
                    {{$t('shop.cart.orderFrom')}} {{companyName}} {{subscriptionTitle}}
                    <span v-if="isOwnAppPurchase">- {{ $t('shop.cartItem.ownApp.title') }}</span>
                </h2>
                <div class="cart-list mb-4 mt-xl-10">
                    <cart-item v-for="item in cartsInternal"
                               v-bind:key="item.productCode"
                               v-bind:item="item"
                               v-bind:cart-code="cartCode"
                               v-bind:is-own-app-purchase="isOwnAppPurchase"
                               v-bind:max-quantity="getMaxAllowedQuantity(item)"
                               v-on:cart-data-changed="onCartDataChanged">
                    </cart-item>
                </div>
            </template>
            <template v-slot:main-content-2>
                <mini-summary v-bind:price="totalPrice"
                              v-bind:company-name="companyName"
                              v-bind:display-tax-note="false"
                              v-bind:is-own-app-purchase="isOwnAppPurchase">
                    <div class="button-wrapper">
                        <div class="button checkout">
                            <CDButton id="cart-checkout"
                                      :href="checkoutUrl"
                                      color="primary"
                                      data-id="button-checkout"
                                      large block class="cart-checkout">
                                {{$t('shop.cart.checkout')}}
                            </CDButton>
                        </div>
                    </div>
                </mini-summary>
            </template>
        </checkout-responsive-grid-layout>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {CartItemData, CartPageData, PriceData} from 'common/generated-types/types';
    import {CartItem} from 'shop/cartpage/components';
    import {CheckoutResponsiveGridLayout, MiniSummary} from 'shop/checkout/components';
    import {ShopRoute} from 'common/constants';
    import {LicenseType} from 'common/types';
    import {shopCoreDataProvider} from 'common/provider';

    @Component({
        components: {
            CartItem,
            MiniSummary,
            CheckoutResponsiveGridLayout
        }
    })
    export default class Cart extends Vue {
        @Prop() cartCode!: string;
        @Prop() cartItems!: Array<CartItemData>;
        @Prop() companyName!: string;
        @Prop() totalPrice!: PriceData;
        @Prop() isOwnAppPurchase!: boolean;

        $refs!: {
            tooltip: HTMLElement;
        };

        MAX_ALLOWED_UNLIMITED = -1;
        cartsInternal:Array<CartItemData> = [...this.cartItems];
        checkoutUrl = ShopRoute.CART_CHECKOUT(this.cartCode);

        onCartDataChanged(data: CartPageData): void {
            this.cartsInternal = data.cartItems;
            this.$emit('cart-data-changed', data);
        }

        get isSubscription(): boolean {
            return this.cartsInternal.some(cart => cart.licenseType === LicenseType.SUBSCRIPTION);
        }

      getMaxAllowedQuantity(item: CartItemData): number {
          if (item.licenseType === LicenseType.SUBSCRIPTION) {
            return shopCoreDataProvider.data.allowedMaxQuantityLineItemSubsLicense;
          }
          if (item.licenseType === LicenseType.FULL) {
            return shopCoreDataProvider.data.allowedMaxQuantityLineItemFullLicense;
          }
          return this.MAX_ALLOWED_UNLIMITED;
        }

        get subscriptionTitle(): string {
            return this.isSubscription ? ` - ${this.$t('shop.cart.subscriptionTitle')}` : '';
        }
    }
</script>
