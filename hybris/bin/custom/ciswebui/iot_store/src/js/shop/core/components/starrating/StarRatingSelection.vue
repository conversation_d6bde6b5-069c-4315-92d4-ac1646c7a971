<template>
    <div class="star-rating" v-on:mouseleave="resetRating">
        <div v-for="n in 5"
             v-bind:key="n"
             class="star"
             v-bind:class="getClassForStar(n)"
             v-on:mousemove="setCurrentRating($event, n)"
             v-on:click="selectRating($event, n)">
        </div>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';

    @Component
    export default class StarRatingSelection extends Vue {
        @Prop() selectedRating!: number;

        currentRating = 0;

        getClassForStar(n: number): string {
            const roundedRating = Math.round(this.currentRating * 2) / 2;
            if (roundedRating >= n) {
                return 'filled';
            }
            if (roundedRating >= n - 0.5) {
                return 'semi-filled';
            }
            return '';
        }

        setCurrentRating(event: MouseEvent, idx: number): void {
            const width = (event.target as HTMLElement).offsetWidth;
            if(event.offsetX <= width / 2) {
                this.currentRating = idx - 0.5;
            } else {
                this.currentRating = idx;
            }
        }

        selectRating(event: MouseEvent, idx: number): void {
            this.setCurrentRating(event, idx);
            this.$emit('ratingchanged', this.currentRating);
        }

        resetRating(): void {
            this.currentRating = this.selectedRating;
        }
    }
</script>

<style scoped lang="scss" src="./starRating.scss"></style>
