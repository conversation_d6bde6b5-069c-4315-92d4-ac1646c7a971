describe("DocumentsSection", () => {
    it("looks as expected in initial state", async () => {

        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-DocumentsSection');

        await page.setViewport({width: 1440, height: 700});
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("DocumentsSection on 'XL' viewport");

        await page.setViewport({width: 576, height: 700});
        await page.waitFor(600);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("DocumentsSection on 'S' viewport");

    });

});