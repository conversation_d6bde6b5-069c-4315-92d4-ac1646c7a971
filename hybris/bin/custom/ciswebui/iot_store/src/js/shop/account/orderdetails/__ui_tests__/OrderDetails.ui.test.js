import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from "common/testtools/inlineTestPageCreator";
import {testMap} from 'common/testtools/scenariosstore';
import orderDetailsWithInvoicesData from 'common/testtools/scenariosstore/orderDetailsWithInvoicesData.json';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';
import {LicenseType} from 'common/types';
import {PaymentMethodType} from 'common/generated-types/types';

describe("order details page", () => {

    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.OrderDetails);
    });

    it("should look as expected for paid apps", async () => {
        await page.setViewport({width: 1199, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it("should not break the layout for long app names, desktop and mobile", async () => {
        data.pageData.entries = [
            {"productName":"People Counting Application For Better Human Observation","productCode":"A_00001005_evaluation","productUrl":"/p/A_00001005","companyName":"Demo Apps","versionName":"8.0.0","licenseName":"Subscription","licenseType":"SUBSCRIPTION","logoUrl": "/sample-data/logo.svg","itemPrice":{"symbol":"EUR","value":"0.00"},"totalPrice":{"symbol":"EUR","value":"69.69"},"quantity":4,"entryNumber":0}
        ];
        await page.setViewport({width: 1199, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');

        await Sizes.testSmallScreensize(2000);
    });

    it("should look as expected for evaluation apps", async () => {
        data.pageData.totalPrice = {"symbol":"EUR","value":"0.00"};
        data.pageData.totalPriceWithTax = {"symbol":"EUR","value":"0.00"};
        data.pageData.totalTax = {"symbol":"EUR","value":"0.00"};
        data.pageData.entries = [
            {"productName":"People Count","productCode":"A_00001005_evaluation","productUrl":"/p/A_00001005","companyName":"Demo Apps","versionName":"8.0.0","licenseName":"Trial","licenseType":"EVALUATION","logoUrl": "/sample-data/logo.svg","itemPrice":{"symbol":"EUR","value":"0.00"},"totalPrice":{"symbol":"EUR","value":"0.00"},"quantity":4,"entryNumber":0}
        ];
        data.pageData.paymentMethod = PaymentMethodType.ZERO;
        data.pageData.invoices = [];

        await page.setViewport({width: 1199, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it("should hide payment method", async () => {
        data.pageData.paymentMethod = PaymentMethodType.ZERO;
        await page.setViewport({width: 1199, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('should look as expected for invoice by seller', async () => {
        data.pageData.paymentMethod = 'INVOICE_BY_SELLER';
        await page.setViewport({width: 1400, height: 1400});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('should look as expected for own apps purchase', async () => {
        data.pageData.ownAppsPurchase = true;
        await page.setViewport({width: 1400, height: 1400});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it("subscription order", async () => {
        data.pageData.entries[0].licenseType = LicenseType.SUBSCRIPTION;
        data.pageData.entries[0].licenseName = 'Subscription';
        await page.setViewport({width: 1200, height: 750});
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });
});

describe("order details page with invoices", () => {

    let data;

    beforeEach(async () => {
        data = cloneDeep({
            ...testMap.OrderDetails,
            pageData: orderDetailsWithInvoicesData
        });
    });

    it("should look as expected", async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraSmallScreensize(2500);
        await Sizes.testSmallScreensize(2500);
        await Sizes.testMediumScreensize(2500);
        await Sizes.testLargeScreensize(2500);
        await Sizes.testExtraLargeScreensize(2500);
    });

    it("should look as expected when no invoices", async () => {
        data.pageData.invoices = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testExtraSmallScreensize(2500);
        await Sizes.testSmallScreensize(2500);
        await Sizes.testMediumScreensize(2500);
        await Sizes.testLargeScreensize(2500);
        await Sizes.testExtraLargeScreensize(2500);
    });
});

describe("order details with failed payment", () => {

    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.OrderDetails);
    });

    it("should look as expected", async () => {
        data.pageData.failedPayment = {
            cardNumber: "***5556",
            updateUrl: "google.com"
        }
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraSmallScreensize(2500);
        await Sizes.testSmallScreensize(2500);
        await Sizes.testMediumScreensize(2500);
        await Sizes.testLargeScreensize(2500);
        await Sizes.testExtraLargeScreensize(2500);
    });
});