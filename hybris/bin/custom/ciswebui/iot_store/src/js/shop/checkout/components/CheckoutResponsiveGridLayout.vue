<template>
    <div class="root">
        <v-container class="mt-4 mt-lg-16">

            <v-row v-if="!!$slots['breadcrumb-container']">
                <v-col cols="12">
                    <div class="breadcrumb-container">
                        <slot name="breadcrumb"></slot>
                    </div>
                </v-col>
            </v-row>

            <v-row v-if="headerTitle">
                <v-col cols="12">
                    <div class="header-container">
                        <h1 class="d-flex align-content-center mb-8">
                            <CDIcon v-if="headerIcon" color="black" class="mr-4" large>{{ headerIcon }}</CDIcon>
                            {{ headerTitle }}
                        </h1>
                    </div>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12" xl="8">
                    <div class="responsive-container">
                        <div class="left-container">
                            <div class="sub-container-1">
                                <slot name="main-content-1"></slot>
                            </div>
                            <div class="sub-container-2" v-if="hasContentSlot(3)">
                                <slot name="main-content-3"></slot>
                            </div>
                            <div class="sub-container-3" v-if="hasContentSlot(4)">
                                <slot name="main-content-4"></slot>
                            </div>
                            <div class="sub-container-4" v-if="hasContentSlot(5)">
                              <slot name="main-content-5"></slot>
                            </div>
                        </div>
                    </div>
                </v-col>
                <v-col cols="12" xl="4">
                    <div class="right-container mt-xl-16">
                        <slot name="main-content-2"></slot>
                    </div>
                </v-col>
            </v-row>

            <v-row v-if="hasContentSlot()">
                <v-col cols="12">
                    <div class="button-container">
                        <slot name="button-container"></slot>
                    </div>
                </v-col>
            </v-row>

        </v-container>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';

    @Component
    export default class CheckoutResponsiveGridLayout extends Vue {
      @Prop() headerTitle!: string;
      @Prop() headerIcon!: string;

	    hasContentSlot(position: number): boolean {
		    return this.$slots[`main-content-${position}`] !== undefined || this.$slots['button-container'] !== undefined;
	    }
    }
</script>
