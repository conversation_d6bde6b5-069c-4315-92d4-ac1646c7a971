import {shallowMount} from '@vue/test-utils';
import AppCta from 'shop/productdetails/components/AppCta';
import 'common/testtools/unit_tests_mock';
import vuetify from 'common/plugins/brands/azena/vuetify';

jest.mock('common/util');

const mountCta = (licenses, readOnly) => shallowMount(AppCta, {
    mocks: {
        $t: () => {
        }
    },
    vuetify,
    propsData: {
        licenses,
        readOnly
    }
});

const fullLicense = {
    licenseType: {
        code: 'FULL'
    },
    purchasability: 'PURCHASABLE'
};

const evaluationLicense = {
    licenseType: {
        code: 'EVALUATION'
    },
    purchasability: 'PURCHASABLE'
};

const subscriptionLicense = {
    licenseType: {
        code: 'SUBSCRIPTION'
    },
    purchasability: 'PURCHASABLE'
};

describe('App CTA', () => {
    beforeEach(() => {
        window.frontendData = {
            coreData: {
                currentCompany: {
                    companyApproved: true,
                },
                allowedToBuy: true
            }
        };
    });

    it('No place order permission -> no buttons visible.', async () => {
        window.frontendData.coreData.allowedToBuy = false;
        const wrapper = mountCta([], false);
        expect(wrapper.find('#trial').exists()).toBe(false);
        expect(wrapper.find('#purchase').exists()).toBe(false);
    });

    it('No evaluation license -> only purchase button visible.', async () => {
        const wrapper = mountCta([fullLicense], false);
        await wrapper.trigger('click');
        expect(wrapper.find('#trial').exists()).toBe(false);
        expect(wrapper.find('#purchase').exists()).toBe(true);
    });

    it('Place order permission -> trial and purchase button visible.', async () => {
        const wrapper = mountCta([fullLicense, evaluationLicense], false);
        expect(wrapper.find('#trial').exists()).toBe(true);
        expect(wrapper.find('#purchase').exists()).toBe(true);
    });

    it('Not available in current country -> no buttons visible', async () => {
        const wrapper = mountCta([fullLicense, evaluationLicense], true);
        expect(wrapper.find('#trial').exists()).toBe(false);
        expect(wrapper.find('#purchase').exists()).toBe(false);
    });

    it('Subscription and full licenses available -> only purchase button for subscription license visible', async () => {
        const wrapper = mountCta([subscriptionLicense, fullLicense], false);
        expect(wrapper.find('[data-license-type="full"]').exists()).toBe(false);
        expect(wrapper.find('[data-license-type="subscription"]').exists()).toBe(true);
    });

    it('Subscription license available -> purchase button for subscription license visible', async () => {
        const wrapper = mountCta([subscriptionLicense], false);
        expect(wrapper.find('[data-license-type="subscription"]').exists()).toBe(true);
        expect(wrapper.find('[data-license-type="full"]').exists()).toBe(false);
    });

    it('Full license available -> purchase button for full license visible', async () => {
        const wrapper = mountCta([fullLicense], false);
        expect(wrapper.find('[data-license-type="full"]').exists()).toBe(true);
        expect(wrapper.find('[data-license-type="subscription"]').exists()).toBe(false);
    });

    it('Tool license available -> App Cta not visible', async () => {
        const wrapper = mountCta([{licenseType: {code: 'TOOL'}}], false);
        expect(wrapper.find('#third-party-app-cta').exists()).toBe(false);
    });

    const unpurchasableStatuses = ["UNPAYABLE", "COUNTRY_DISABLED", "COUNTRY_BLOCKED", "UNAVAILABLE", "TYPE_UNSUPPORTED"];

    describe.each(unpurchasableStatuses)(`Unpurchasable statuses`, (status) => {
        it(`License with purchasability status ${status} -> purchase button not visible`, async () => {
            const wrapper = mountCta([{...fullLicense, purchasability: status}], false);
            await wrapper.trigger('click');
            expect(wrapper.find('#purchase').exists()).toBe(false);
        });
    });
});
