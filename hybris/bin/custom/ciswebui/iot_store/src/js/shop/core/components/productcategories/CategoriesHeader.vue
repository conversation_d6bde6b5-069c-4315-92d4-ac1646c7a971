<template>
  <div class="categories-header-body">
    <div class="categories-header" v-if="categoriesLinks.length > 0">
      <a v-for="navigationItem in categoriesLinks"
         v-bind:target="navigationItem.target"
         v-bind:key="navigationItem.text"
         v-bind:href="navigationItem.url">
        {{ translateNavigation(navigationItem) }}
      </a>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {NavigationItemData, NavigationItemGroup} from 'common/generated-types/types';
import {navigationService} from 'common/services';
import {sortBy} from 'lodash';

@Component
export default class CategoriesHeader extends Vue {

  translateNavigation(navigationItem : NavigationItemData) : string {
    return navigationService.translate(this.$i18n, navigationItem);
  }

  get categoriesLinks(): NavigationItemData[] {
    return sortBy(navigationService.byGroup(NavigationItemGroup.PRODUCT_CATEGORIES), 'index');
  }

}
</script>

<style scoped lang="scss">
@import "common/layout";
@import "common/design";

.categories-header-body {

  justify-content: center;

  margin: 0 auto;

  .categories-header {
    display: none;
    @include respond-to('M') {
      display: block;
    }

    flex: 0 0 auto;
    padding: 0 10px;

    a {
      margin: 0 10px;
      color: var(--v-grey-darken2);
    }
  }
}
</style>
