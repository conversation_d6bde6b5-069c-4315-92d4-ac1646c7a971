import * as Sizes from 'common/testtools/testScreenSizes';

describe("FollowAppChanges", () => {
    it("shows component correctly", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-FollowAppChangesSection');

        await Sizes.testExtraLargeScreensize(150);
        await Sizes.testLargeScreensize(150);
        await Sizes.testMediumScreensize(150);
        await Sizes.testSmallScreensize(150);
    });
});
