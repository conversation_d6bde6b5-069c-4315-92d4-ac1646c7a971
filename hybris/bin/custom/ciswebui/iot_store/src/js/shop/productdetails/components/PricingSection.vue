<template>
  <div class="pricing-section" data-id="container-pricing-section">
    <v-container :fluid="true" class="pa-0">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <v-row>
          <v-col cols="12">
            <h2 class="label">{{ $t('shop.productDetails.pricingSection.pricing') }}</h2>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" lg="8">
            <div class="licenses-box">
              <div v-for="license in licenses" v-bind:key="license.code">
                <div v-test:license="license.licenseType.code.toLowerCase()" class="license">
                  <license-item class="cell-license-item py-8 mb-4"
                                v-bind:license="license"
                                v-bind:displayLicenses="displayLicenses"
                                v-bind:product="product"
                                ref="items">
                  </license-item>
                  <tool-item class="cell-license-item" v-if="isToolProduct(license)"
                             :license="license"
                             :company="product.company"
                             :product-name="product.name"
                             ref="tools">
                  </tool-item>
                </div>
              </div>
            </div>
          </v-col>
          <v-col cols="12" lg="3" offset-lg="1" v-if="!userPermissionService.isAnonymous()">
            <CDBlock>
              <pricing-section-sidebar class="pricing-sidebar"
                                       v-bind:product="product">
              </pricing-section-sidebar>
            </CDBlock>
          </v-col>
        </v-row>

        <div class="permission-wrapper">
          <permission-text v-if="userPermissionService.isAnonymous()">
            <template v-slot:content>
              <i18n path="shop.productDetails.pricingSection.anonymousUserInfo" tag="p">
                <template v-slot:url>
                  <a :href="`${loginEntry.url}`">
                    {{ $t("shop.productDetails.pricingSection.logIn") }}
                  </a>
                </template>
              </i18n>
            </template>
          </permission-text>

          <permission-text v-else-if="notAvailableInCountry">
            <template v-slot:content>
              <p class="permission-info-text">{{ $t('shop.productDetails.pricingSection.countryPermissionText') }}</p>
            </template>
          </permission-text>

          <permission-text v-else-if="!isAllowedToBuy">
            <template v-slot:content>
              <p class="permission-info-text">{{ $t('shop.productDetails.pricingSection.permissionText') }}</p>
            </template>
          </permission-text>

          <permission-text v-else-if="userCountryBlockedInDeveloperCountry" data-id="text-permission-integrator-country-blocked">
            <template v-slot:content>
              <p class="permission-info-text">{{ $t('shop.productDetails.pricingSection.integratorCountryBlockedText') }}</p>
            </template>
          </permission-text>

          <permission-text v-else-if="isTrial">
            <template v-slot:content>
              <p class="permission-info-text">{{ $t('shop.productDetails.pricingSection.trialNowFullSoon') }}</p>
            </template>
          </permission-text>

          <permission-text v-else-if="licenses.length === 0">
            <template v-slot:content>
              <p class="permission-info-text">{{$t('shop.productDetails.productHeader.noLicense')}}</p>
            </template>
          </permission-text>
        </div>
      </v-container>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DetailProductData, NavigationItemData} from 'common/generated-types/types';
import {LicenseType, PurchasableLicenseData} from 'common/types';
import {
  licenseTypeService,
  navigationService,
  productDataService,
  userPermissionService
} from 'common/services';
import InfoIcon from 'common/images/boxed/icon-24-info-d.svg';
import IconWarning from 'common/images/boxed/icon-24-warning-d.svg';
import {PermissionText} from 'common/components';
import {LicenseItem, ToolItem} from 'shop/productdetails/components/license';
import {sortBy} from 'lodash';
import PricingSectionSidebar from 'shop/productdetails/components/PricingSectionSidebar.vue';

const LICENSE_ORDER_INDEX = ['EVALUATION', 'SUBSCRIPTION', 'FULL'];

@Component({
  components: {
    ToolItem,
    LicenseItem,
    InfoIcon,
    PermissionText,
    IconWarning,
    PricingSectionSidebar
  }
})
export default class PricingSection extends Vue {
  @Prop() product!: DetailProductData;
  @Prop() notAvailableInCountry!: boolean;
  @Prop() displayLicenses!: boolean;
  @Prop() isTrial!: boolean;

  productDataService = productDataService;
  userPermissionService = userPermissionService;
  navigationService = navigationService;
  licenseTypeService = licenseTypeService;

  LicenseType = LicenseType;

  $refs!: {
    items: LicenseItem[];
    tools: ToolItem[];
  };

  get loginEntry(): NavigationItemData {
    return this.navigationService.byItemCode('storeLogin');
  }

  onPurchaseButtonClicked(selectedLicense: LicenseType): void {
    if (this.licenseTypeService.isThirdPartyLicense(selectedLicense)) {
      const license = this.$refs.items.find(item => item.license.licenseType.code === selectedLicense) as LicenseItem;
      if (license) {
        license.$el.scrollIntoView({block: 'center'});
        license.focus();
      }
    } else {
      const tool = this.$refs.tools.find(item => item.license.licenseType.code === selectedLicense) as ToolItem;
      if (tool) {
        tool.$el.scrollIntoView({block: 'center'});
        tool.focus();
      }
    }
  }

  get isAllowedToBuy(): boolean {
    return productDataService.isToolApp(this.product) || (!this.product.ownCompanyAppAndCanBuy && userPermissionService.isAllowedToBuy());
  }

  get userCountryBlockedInDeveloperCountry(): boolean {
    return this.product.integratorCountryBlockedInDeveloperCountry;
  }

  isToolProduct(license: PurchasableLicenseData): boolean {
    return !this.licenseTypeService.isThirdPartyLicense(license.licenseType.code as LicenseType);
  }

  get licenses(): PurchasableLicenseData[] {
    const licensesData = this.displayLicenses ? this.product.licenses : this.product.supportedLicenses;
    if (!licensesData) return [];

    return sortBy(licensesData, item => {
      const index = LICENSE_ORDER_INDEX.indexOf(item.licenseType.code);
      return index !== -1 ? index : 1000;
    }) as PurchasableLicenseData[];
  }
}
</script>

<style lang="scss" scoped>
.pricing-section {
  .license-item {
    border-bottom: 1px solid var(--v-grey-lighten1);
  }
}
</style>
