import util from 'common/util';
import {CartItemData, CartPageData, LicensePurchaseData, LicenseQuantityData} from 'common/generated-types/types';
import {AxiosResponse} from "axios";
import {UnwrapRef} from "vue";
import {DiscountProductPriceMap} from "common/types";

let axios = util.axios;
const cartPath = '/shop/api/cart';

export default class CartResource {
    private readonly cartCode: string;

    constructor(cartCode: string) {
        this.cartCode = cartCode;
    }

    static addToCart(selectedLicenses: LicenseQuantityData[], consent?: UnwrapRef<boolean | null>) {
        return axios.post<LicensePurchaseData>(cartPath, {
            licenses: selectedLicenses,
            ...(consent && { specialOfferAccepted: true })
        })
    }

    updateCart(item: CartItemData):Promise<AxiosResponse<CartPageData>> {
        return axios.put<CartPageData>(`${cartPath}/${this.cartCode}`, item);
    }

    removeEntry(entryNumber: number):Promise<AxiosResponse<CartPageData>> {
        return axios.delete<CartPageData>(`${cartPath}/${this.cartCode}/entry/${entryNumber}`);
    }

    static setPromotionConsent(licenseCode: string): Promise<AxiosResponse<DiscountProductPriceMap>> {
        const promotionConsentPath = `/shop/api/promotion/consent/${licenseCode}`;
        return axios.get<DiscountProductPriceMap>(promotionConsentPath);
    }
};