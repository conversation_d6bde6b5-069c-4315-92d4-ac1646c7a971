<template>
  <div class="integrations-section">
    <v-container :fluid="true" class="pa-0">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <v-row>
          <v-col cols="12" xl="8">
            <h2 class="mb-2">{{$t('shop.productDetails.integrationsSection.title')}}</h2>
            <i18n path="shop.productDetails.integrationsSection.description" tag="p">
              <template v-slot:url>
                <a :href="`${corporateHome}/integrations`" target="_blank">
                  {{ $t("shop.productDetails.integrationsSection.appIntegrations") }}
                </a>
              </template>
            </i18n>
            <div class="integration-item" v-for="integration in sortedAppIntegrationList">
              <div class="integration-body" v-if="integration.type !== customType">
                <div class="integration-details">
                  <v-row>
                    <v-col cols="12">
                      <div class="integration-name d-flex">
                        <CDIcon icon="$gateway" color="black" v-if=" integration.integrationType === 'GATEWAY'"></CDIcon>
                        <CDIcon icon="$certificate" color="black" v-else-if="integration.integrationType === 'STANDARD'"></CDIcon>
                        <CDIcon icon="$doublesquares" color="black" v-else></CDIcon>
                        <h3 class="ml-2">{{integration.displayName}}</h3>
                      </div>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" xl="8">
                      <div class="integration-description" v-if="integration.externalDescription != null">
                        {{integration.externalDescription}}
                      </div>
                      <div class="integration-description" v-else>
                        {{$t('shop.productDetails.integrationsSection.customDescription')}}
                      </div>
                    </v-col>
                    <v-col cols="12" xl="3" offset-xl="1" class="d-flex justify-xl-end pt-0 pt-xl-2 pl-2">
                      <div class="integration-download">
                        <CDButtonTextIcon icon="$download"
                                          color="primary"
                                          download
                                          v-if="integration.documentation"
                                          v-bind:href="integration.documentation.url">
                          {{$t('shop.productDetails.integrationsSection.downloadPdf')}}
                        </CDButtonTextIcon>
                      </div>
                    </v-col>
                  </v-row>
                </div>
              </div>
              <div class="integration-body" v-else>
                <div class="integration-details">
                  <v-row>
                    <v-col cols="12" xl="8">
                      <div class="integration-name-customer d-flex">
                        <CDIcon icon="$doublesquares" color="black"></CDIcon>
                        <h3 class="ml-2">{{integration.displayName}}</h3>
                        <tooltip v-if="integration.type === customType" class="info-hint ml-1" v-bind:text="integration.externalDescription != null ?
                            integration.externalDescription : $t('shop.productDetails.integrationsSection.customDescription')">
                          <info-icon></info-icon>
                        </tooltip>
                      </div>
                    </v-col>
                    <v-col cols="12" xl="3" offset-xl="1" class="d-flex justify-xl-end pt-0 pt-xl-2 pl-2">
                      <div class="integration-download">
                        <CDButtonTextIcon icon="$download"
                                          color="primary"
                                          download
                                          v-if="integration.documentation"
                                          v-bind:href="integration.documentation.url">
                          <span class="download-text">
                            {{$t('shop.productDetails.integrationsSection.downloadPdf')}}
                          </span>
                        </CDButtonTextIcon>
                      </div>
                    </v-col>
                  </v-row>
                </div>
              </div>
            </div>
          </v-col>
          <v-col cols="12" xl="3" offset-xl="1">
            <CDBlock>
              <div class="integration-item integration-item-static">
                <h3 class="mb-2" v-html="$t('shop.productDetails.integrationsSection.missingIntegration')"></h3>
                <div class="integration-description">
                <i18n path="shop.productDetails.integrationsSection.missingDescription">
                  <template v-slot:email>
                    <a :href="`mailto:${appContactInfo.emailAddress}`">{{ $t("shop.productDetails.integrationsSection.developer") }}</a>
                  </template>
                </i18n>
                </div>
              </div>
            </CDBlock>
          </v-col>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AppContactData, AppIntegrationType, EulaType, PublicAppIntegrationData} from 'common/generated-types/types';
import { Tooltip } from 'common/components';
import InfoIcon from 'common/images/boxed/icon-24-info-b.svg';

@Component({
  components: {
    Tooltip,
    InfoIcon
  }
})
export default class IntegrationsSection extends Vue {
  @Prop() appIntegrations!: PublicAppIntegrationData[];
  @Prop() appContactInfo!: AppContactData;
  @Prop() corporateHome!: string;

  sortedAppIntegrationList: PublicAppIntegrationData[] = [];
  customType: string = EulaType.CUSTOM;

  mounted(): void {
    this.sortedAppIntegrationList = this.appIntegrations.filter((i: PublicAppIntegrationData) => i.type === AppIntegrationType.STANDARD)
        .concat(this.appIntegrations.filter((i: PublicAppIntegrationData) => i.type === AppIntegrationType.CUSTOM));
  }

}
</script>

<style scoped lang="scss">
@import 'common/vuetify';
.integration-body {
  border-top: 1px solid var(--v-grey-lighten1);
  padding: map-get($spacers, 5) 0;
}
</style>
