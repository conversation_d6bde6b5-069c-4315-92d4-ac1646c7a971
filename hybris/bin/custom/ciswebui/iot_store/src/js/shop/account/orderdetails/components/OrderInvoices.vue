<template>
  <div>
    <v-row>
      <v-col>
        <span class="text-h3">
          {{ title }}
        </span>
      </v-col>
    </v-row>
    <v-row class="mt-4">
      <v-col>
        <CDDataTable
            :headers="invoiceTableHeaders"
            :items="invoices"
            :expanded="invoices"
            :mobile-breakpoint="0"
            hide-default-footer
            item-key="externalId"
            sort-by="externalId"
            :sort-desc="true"
            data-id="table-order-invoices">

          <template v-slot:[`header.grossAmount`]="{ header }">
            {{ header.text }}
            <v-tooltip bottom v-if="!invoiceBySellerOrOwnAppsPurchase">
              <template v-slot:activator="{ on }">
                <CDIcon v-on="on" small>$info</CDIcon>
              </template>
              {{ $t('shop.orderDetails.invoices.table.headers.totalAmount.tooltipText') }}
            </v-tooltip>
          </template>

          <template v-slot:[`item.externalId`]="{ item }">
            <span class="text-h5">{{ item.externalId }}</span>
          </template>

          <template v-slot:[`item.invoiceDate`]="{ item }">
            {{ formatDateShort(item.invoiceDate) }}
          </template>

          <template v-slot:[`item.grossAmount`]="{ item }">
            {{ formatAmount(item.grossAmount) }}
          </template>

          <template v-slot:[`item.invoiceStatus`]="{ item }">
            <span :class="{highlighted: item.invoiceStatus === 'OVERDUE' || item.invoiceStatus === 'PAYMENT_FAILED'}">
              {{ formatStatus(item.invoiceStatus) }}
            </span>

          </template>

          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length">
              <v-row class="my-2" no-gutters>
                <v-col cols="12" class="my-1 d-lg-none">
                  <span class="text-h5">
                    {{ invoiceDateLabel }}:
                  </span>
                  {{ formatDateShort(item.invoiceDate) }}
                </v-col>
                <v-col cols="12" class="my-1 d-md-none">
                  <span class="text-h5">
                    {{ $t('shop.orderDetails.invoices.table.headers.invoiceStatus.label') }}:
                  </span>
                  {{ formatStatus(item.invoiceStatus) }}
                </v-col>
                <v-col cols="12" class="my-1"
                       v-for="creditNote in creditNotes(item)"
                       v-bind:key="creditNote.externalId">
                  {{ formatDateLong(creditNote.creationTime) }} -
                  <span>
                    {{
                      $t('shop.orderDetails.invoices.table.data.documentIssuedDate.label',
                          {documentType: creditNoteDocumentLabel(creditNote.creditNoteType)})
                    }}
                  </span>
                  <a :href="creditNote.documentUrl"
                     :data-id="`link-invoice-credit-note-download-${creditNote.externalId}`"
                     download>
                    {{ creditNote.displayName }}
                    <CDIcon small>$download</CDIcon>
                  </a>
                </v-col>
                <v-col cols="12" class="my-1">
                  {{ formatDateLong(item.creationTime) }} -
                  <span>
                    {{
                      $t('shop.orderDetails.invoices.table.data.documentIssuedDate.label',
                          {documentType: invoiceDocumentLabel})
                    }}
                  </span>
                  <a :href="item.documentUrl"
                     :data-id="`link-invoice-download-${item.externalId}`"
                     download>
                    {{ item.displayName }}
                    <CDIcon small>$download</CDIcon>
                  </a>
                </v-col>
              </v-row>
            </td>
          </template>

        </CDDataTable>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {InvoiceCreditNoteData, InvoiceData, OrderDetailsData, PaymentMethodType, PriceData} from 'common/generated-types/types';
import {camelCase, sortBy} from 'lodash';
import {TranslateResult} from 'vue-i18n';
import {i18nService} from 'common/services';

@Component
export default class OrderInvoices extends Vue {
  @Prop() orderDetailsData!: OrderDetailsData;

  localeForFormats = '';

  mounted(): void {
    this.localeForFormats = i18nService.getLocaleForFormats();
  }

  invoiceTableHeaders = [
    {
      text: this.invoiceIdLabel,
      value: 'externalId',
      align: 'start'
    },
    {
      text: this.invoiceDateLabel,
      value: 'invoiceDate',
      align: 'center d-none d-lg-table-cell'
    },
    {
      text: this.$t('shop.orderDetails.invoices.table.headers.invoiceStatus.label'),
      value: 'invoiceStatus',
      align: 'center d-none d-md-table-cell'
    },
    {
      text: this.totalAmountLabel,
      value: 'grossAmount',
      align: 'end'
    },
  ];

  get invoices(): InvoiceData[] {
    return this.orderDetailsData.invoices;
  }

  get title(): TranslateResult {
    if (this.invoiceBySellerOrder || this.ownAppsPurchase) {
      return this.$t(`shop.orderDetails.invoices.label.documents`);
    } else {
      return this.$t(`shop.orderDetails.invoices.label.default`);
    }
  }

  get invoiceBySellerOrder(): boolean {
    return this.orderDetailsData.paymentMethod === PaymentMethodType.INVOICE_BY_SELLER;
  }

  get ownAppsPurchase(): boolean {
    return this.orderDetailsData.ownAppsPurchase;
  }

  get invoiceBySellerOrOwnAppsPurchase(): boolean {
    return this.invoiceBySellerOrder || this.ownAppsPurchase;
  }

  get invoiceDocumentLabel(): TranslateResult {
    if (this.invoiceBySellerOrOwnAppsPurchase) {
      return this.$t(`shop.orderDetails.invoices.table.data.documentType.transactionReport`);
    } else {
      return this.$t(`shop.orderDetails.invoices.table.data.documentType.invoice`);
    }
  }

  get invoiceIdLabel(): TranslateResult {
    if (this.invoiceBySellerOrOwnAppsPurchase) {
      return this.$t(`shop.orderDetails.invoices.table.headers.invoiceId.label.document`);
    } else {
      return this.$t(`shop.orderDetails.invoices.table.headers.invoiceId.label.default`);
    }
  }

  get invoiceDateLabel(): TranslateResult {
    if (this.invoiceBySellerOrOwnAppsPurchase) {
      return this.$t(`shop.orderDetails.invoices.table.headers.invoiceDate.label.document`);
    } else {
      return this.$t(`shop.orderDetails.invoices.table.headers.invoiceDate.label.default`);
    }
  }

  get totalAmountLabel(): TranslateResult {
    if (this.invoiceBySellerOrOwnAppsPurchase) {
      return this.$t(`shop.orderDetails.invoices.table.headers.totalAmount.label.document`);
    } else {
      return this.$t('shop.orderDetails.invoices.table.headers.totalAmount.label.default');
    }
  }

  formatAmount(amount: PriceData): string {
    return `${amount.symbol} ${amount.value}`;
  }

  formatDateLong(date: string): string {
    return this.$d(new Date(date), 'long', this.localeForFormats);
  }

  formatDateShort(date: string): string {
    return this.$d(new Date(date), 'short', this.localeForFormats);
  }

  formatStatus(status: string): TranslateResult {
    return this.$t(`shop.orderDetails.invoices.table.data.invoiceStatus.${camelCase(status)}`);
  }

  creditNoteDocumentLabel(creditNoteType: string): TranslateResult {
    return this.$t(`shop.orderDetails.invoices.table.data.documentType.${camelCase(creditNoteType)}`);
  }

  creditNotes(invoice: InvoiceData): InvoiceCreditNoteData[] {
    return sortBy(invoice.creditNotes, function (creditNote) {
      return new Date(creditNote.issuanceDate);
    }).reverse();
  }
}
</script>

<style lang="scss" scoped>
@import "common/design";

:deep(.v-data-table__expanded__content) {
  box-shadow: none !important;
}

:deep(.v-data-table__expanded__row) {
  td {
    vertical-align: middle;
    border-bottom: none !important;
  }
}

:deep(.v-data-table-header) {
  th {
    vertical-align: middle;
  }
}

.highlighted {
  color: var(--v-error-base);
}
</style>
