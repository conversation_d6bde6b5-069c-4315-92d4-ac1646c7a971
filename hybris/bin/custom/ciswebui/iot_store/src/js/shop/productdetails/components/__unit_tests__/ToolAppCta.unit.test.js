import {shallowMount} from '@vue/test-utils';
import ToolAppCta from 'shop/productdetails/components/ToolAppCta';
import vuetify from 'common/plugins/brands/azena/vuetify';

jest.mock('common/util');

const mountCta = (licenses, readOnly) => shallowMount(ToolAppCta, {
    mocks: {
        $t: () => {
        }
    },
    vuetify,
    propsData: {
        licenses,
        readOnly
    }
});

const toolLicense = {
    licenseType: {
        code: 'TOOL'
    }
};

describe('Tool CTA', () => {
    it('When Tool license available -> Get Free Tool button visible.', async () => {
        const wrapper = mountCta([toolLicense], false);
        expect(wrapper.find('#tool-app-cta').exists()).toBe(true);
        expect(wrapper.find('#tool').exists()).toBe(true);
    });

    it('When Tool license not available -> No Get Free Tool button visible.', async () => {
        const wrapper = mountCta([], false);
        expect(wrapper.find('#tool-app-cta').exists()).toBe(false);
        expect(wrapper.find('#tool').exists()).toBe(false);
    });
});