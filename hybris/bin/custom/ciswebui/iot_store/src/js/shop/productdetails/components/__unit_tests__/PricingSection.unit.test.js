import {createLocalVue, mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import {PricingSection} from 'shop/productdetails/components';
import {LicenseType} from 'common/types';
import 'common/test-directive';
import licenseActivationResource from 'shop/resources/licenseActivationResource';
import vuetify from 'common/plugins/brands/azena/vuetify';
import {i18n} from 'common/i18n';

jest.mock('common/util');
jest.mock('shop/resources/licenseActivationResource');

const localVue = createLocalVue();
localVue.use(i18n);

const thirdPartyLicense = [
    {
        code: LicenseType.EVALUATION,
        currencyIsocode: 'USD',
        price: 0,
        licenseType: {
            code: LicenseType.EVALUATION,
            type: LicenseType.EVALUATION
        }
    },
    {
        code: LicenseType.FULL,
        currencyIsocode: 'USD',
        price: 50,
        licenseType: {
            code: LicenseType.FULL,
            type: LicenseType.FULL
        }
    }
];

const toolLicense = [
    {
        code: LicenseType.TOOL,
        currencyIsocode: 'USD',
        price: 0,
        licenseType: {
            code: LicenseType.TOOL,
            type: LicenseType.TOOL
        }
    }
];

const productDeveloper = {
    friendlyName: null,
    name: 'Bosch Digital Commerce',
    companyCountry: 'DE'
};

const mountPricingSection = (licenses, displayLicenses, isTrial, integratorCountryBlocked = false) => mount(PricingSection, {
    mocks: {
        $t: () => {
        },
        $te: () => {
        },
        $n: () => {
        }
    },
    stubs: {
        'info-icon': true
    },
    data() {
        return {showChild: true}
    },
    vuetify,
    localVue,
    propsData: {
        notAvailableInCountry: false,
        displayLicenses,
        isTrial,
        product: {
            licenses: licenses,
            company: productDeveloper,
            supportedLicenses: [
                {
                    code: LicenseType.EVALUATION,
                    currencyIsocode: 'USD',
                    price: 0,
                    licenseType: {
                        code: LicenseType.EVALUATION,
                        type: LicenseType.EVALUATION
                    }
                },
                {
                    code: LicenseType.FULL,
                    currencyIsocode: 'USD',
                    price: 100,
                    licenseType: {
                        code: LicenseType.FULL,
                        type: LicenseType.FULL
                    }
                },
                {
                    code: LicenseType.SUBSCRIPTION,
                    currencyIsocode: 'USD',
                    price: 100,
                    licenseType: {
                        code: LicenseType.SUBSCRIPTION,
                        type: LicenseType.SUBSCRIPTION
                    }
                }
            ],
            integratorCountryBlockedInDeveloperCountry: integratorCountryBlocked
        }
    }
});

describe('Pricing section', () => {
    beforeEach(() => {
        window.frontendData = {
            coreData: {
                currentCompany: {
                    companyApproved: true,
                },
                allowedToBuy: true
            }
        };
    });

    function verifyElementPresence(wrapper, allowFullPurchase, allowEvalPurchase) {
        expect(wrapper.find('.license-full .cell-license-info').exists()).toBe(true);
        expect(wrapper.find('.license-full .cell-price-info').exists()).toBe(allowFullPurchase);
        expect(wrapper.find('.license-evaluation .cell-license-info').exists()).toBe(true);
        expect(wrapper.find('.license-evaluation .cell-price-info').exists()).toBe(true);
    }

    function verifyLicenseOrder(wrapper) {
        const expectedLicenseDisplayOrder = ['license-evaluation', 'license-subscription', 'license-full'];
        expect(
            wrapper.findAll('.license-full,.license-evaluation,.license-subscription').wrappers
                .map((wrapper) => wrapper.element.getAttribute('class').split(" ").pop())
        ).toMatchObject(expectedLicenseDisplayOrder);
    }

    it('User has purchase permission', () => {
        const wrapper = mountPricingSection(thirdPartyLicense, true, false);
        verifyElementPresence(wrapper, true, true);
    });

    it('User has no purchase permission', () => {
        window.frontendData.coreData.currentCompany.companyApproved = false;
        const wrapper = mountPricingSection(thirdPartyLicense, false, false);
        verifyElementPresence(wrapper, false, false);
        verifyLicenseOrder(wrapper);
    });

    it('Pricing Section for tool license ', () => {
        licenseActivationResource.getLicenseActivationStatusForCurrent.mockImplementation(() => Promise.resolve({
            status: 200,
            data: {
                activationStatus: 'INACTIVE'
            }
        }));
        const wrapper = mountPricingSection(toolLicense, true, false);
        expect(wrapper.find('[data-id="container-cell-tool-info"]').exists()).toBe(true);
        expect(wrapper.find('[data-id="span-company-name"]').exists()).toBe(true);
    });

    it('Unapproved Company User can acquire tools,so see no permission warning', () => {
        window.frontendData.coreData.currentCompany.companyApproved = false;
        const wrapper = mountPricingSection(toolLicense, true, false);
        expect(wrapper.find('.permission-wrapper').exists()).toBe(true);
        expect(wrapper.find('.permission-wrapper .permission-info .permission-info-text').exists()).toBe(false);
    });

    it('integrator country blocked in developer country', () => {
        window.frontendData.coreData.currentCountry = {
            isocode: "CH"
        };
        window.frontendData.coreData.activeCountries = [{
            'isocode': 'DE',
            'blockedCountriesCommercial': ['CH']
        }];

        const wrapper = mountPricingSection(thirdPartyLicense, true, false, true);
        expect(wrapper.find('.permission-wrapper').exists()).toBe(true);
        expect(wrapper.find('[data-id="text-permission-integrator-country-blocked"]').exists()).toBeTruthy();
    });
});
