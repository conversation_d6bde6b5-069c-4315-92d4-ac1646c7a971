import companyApps from 'common/testtools/scenariosstore/companyAppsData.json';
import {acceptCookieBanner, createInlineUiTestPage} from 'common/testtools/inlineTestPageCreator';
import {abortRequest, respondRequest} from 'common/testtools/testRequests';
import * as Sizes from 'common/testtools/testScreenSizes';
import {testMap} from 'common/testtools/scenariosstore';
import minimalCompanyProfileData from 'common/testtools/scenariosstore/minimalCompanyProfileData.json';
import companyProfileDataWithMultiLineDescription from 'common/testtools/scenariosstore/companyProfileDataWithMultiLineDescription.json';

const REST_URL = '/shop/api/products';
const companyAppsRestHandler = respondRequest(REST_URL, {
    status: 200,
    contentType: 'application/json',
    body: JSON.stringify(companyApps)
});

beforeEach(async () => {
    await page.setRequestInterception(true);
});

describe('Company profile', () => {
    it('looks as expected', async () => {
        page.on('request', companyAppsRestHandler);

        await createInlineUiTestPage(testMap.CompanyProfile, [{url: REST_URL, data: companyApps}]);
        await acceptCookieBanner();

        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });

    it('shows message for error response', async () => {
        page.on('request', respondRequest(REST_URL, {
            status: 404,
            contentType: 'application/json',
            body: JSON.stringify({userMessages:[{level: 'ERROR', code:'companyprofile.notFound'}]})
        }));

        await createInlineUiTestPage(testMap.CompanyProfile, [{url: REST_URL, data: companyApps}]);
        await acceptCookieBanner();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('shows message for error response without error details', async () => {
        page.on('request', respondRequest(REST_URL, {
                status: 500,
                contentType: 'application/json',
                body: JSON.stringify({})
            })
        );

        await createInlineUiTestPage(testMap.CompanyProfile, [{url: REST_URL, data: companyApps}]);
        await acceptCookieBanner();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('shows message for aborted request', async () => {
        page.on('request', abortRequest(REST_URL));

        await createInlineUiTestPage(testMap.CompanyProfile, [{url: REST_URL, data: companyApps}]);
        await acceptCookieBanner();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });
});

describe('Company profile Minimal', () => {
    it('looks as expected', async () => {
        page.on('request', companyAppsRestHandler);

        await createInlineUiTestPage(
            {
                ...testMap.CompanyProfile,
                pageData: minimalCompanyProfileData
            },
            [{url: REST_URL, data: companyApps}]
        );
        await acceptCookieBanner();

        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });
});

describe('Company profile with multiline description', () => {
    it('looks as expected', async () => {
        page.on('request', companyAppsRestHandler);

        await createInlineUiTestPage(
            {
                ...testMap.CompanyProfile,
                pageData: companyProfileDataWithMultiLineDescription
            },
            [{url: REST_URL, data: companyApps}]
        );
        await acceptCookieBanner();

        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });
});
