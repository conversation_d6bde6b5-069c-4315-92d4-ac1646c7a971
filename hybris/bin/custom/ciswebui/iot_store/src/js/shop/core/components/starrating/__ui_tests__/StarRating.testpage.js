import Vue from 'vue';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import StarRating from 'shop/core/components/starrating/StarRating';
import vuetify from 'common/plugins/brands/azena/vuetify';

new Vue({
    el: '#vue-app',
    vuetify,
    components: {
        TestPageRoot,
        StarRating,
    },
    template: `
      <test-page-root>
        <div class="without-rating" style="height: 20px;">
          <star-rating></star-rating>        
        </div>
        <div class="with-rating">
          <star-rating v-bind:rating="3.47"></star-rating>
        </div>
      </test-page-root>
    `
});
