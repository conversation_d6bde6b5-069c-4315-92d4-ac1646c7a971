<template>
  <div>
    <div v-if="showRegisterProject" data-id="container-project-registration">
      <v-row dense>
        <v-col cols="12">
          <p class="text-h3">
            {{$t('shop.productDetails.privateOfferDialog.projectRegistration.projectData.title')}}
          </p>
        </v-col>
        <v-col cols="12" lg="6">
          <CDInput
              :label="$t('shop.productDetails.privateOfferDialog.projectRegistration.projectData.projectName')"
              v-model="offerRegistration.projectName"
              data-id="input-project-registration-project-name">
          </CDInput>
        </v-col>
        <v-col cols="12" lg="6">
          <CDInput
              :label="$t('shop.productDetails.privateOfferDialog.projectRegistration.projectData.customerName')"
              v-model="offerRegistration.customerName"
              data-id="input-project-registration-customer-name">
          </CDInput>
        </v-col>
        <v-col cols="12" lg="6">
          <CDDatePicker
              type="month"
              color="primary"
              :label="$t('shop.productDetails.privateOfferDialog.projectRegistration.projectData.startDate')"
              v-model="offerRegistration.plannedStartDate"
              data-id="input-project-registration-planned-start-date">
          </CDDatePicker>
        </v-col>
      </v-row>
      <v-row dense>
        <v-col cols="12">
          <p class="text-h3">
            {{$t('shop.productDetails.privateOfferDialog.projectRegistration.addressData.title')}}
          </p>
        </v-col>
        <v-col cols="12" lg="6">
          <CDSelect
              return-object
              :items="countries"
              item-text="name"
              :label="$t('shop.productDetails.privateOfferDialog.projectRegistration.addressData.country')"
              v-model="offerRegistration.siteAddress.country"
              data-id="input-project-registration-address-country">
          </CDSelect>
        </v-col>
        <v-col cols="12">
          <CDInput
              v-model="offerRegistration.siteAddress.line1"
              :label="$t('shop.productDetails.privateOfferDialog.projectRegistration.addressData.line1')"
              data-id="input-project-registration-address-line1">
          </CDInput>
        </v-col>
        <v-col cols="12" lg="6">
          <CDInput
              :label="$t('shop.productDetails.privateOfferDialog.projectRegistration.addressData.postalCode')"
              v-model="offerRegistration.siteAddress.postalCode"
              data-id="input-project-registration-address-postal-code">
          </CDInput>
        </v-col>
        <v-col cols="12" lg="6">
          <CDInput
              :label="$t('shop.productDetails.privateOfferDialog.projectRegistration.addressData.city')"
              v-model="offerRegistration.siteAddress.city"
              data-id="input-project-registration-address-city">
          </CDInput>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Vue, VModel, Prop} from 'vue-property-decorator';
import {commercefacades, PrivateOfferProjectRegistrationData} from 'common/generated-types/types';
import {shopCoreDataProvider} from 'common/provider';
import {cloneDeep} from 'lodash';

@Component({
  components: {}
})
export default class PrivateOfferRegistration extends Vue {
  @VModel() offerRegistration!: PrivateOfferProjectRegistrationData;

  @Prop() showRegisterProject!: boolean;

  countries: Array<commercefacades.CountryData> = [];

  async mounted() {
      this.countries = cloneDeep(shopCoreDataProvider.data.activeCountries);
      this.countries.forEach((country) => {country.name = this.$i18n.t(`country.${country.isocode}`).toString();});
  }
}
</script>

<style scoped lang="scss">
@import "common/design";


</style>
