import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import ReviewItem from 'shop/productdetails/components/ReviewItem';
import StarRating from 'shop/core/components/starrating/StarRating';

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    data: {
        reviews: [
            {
                'rating': 2.5,
                'showCompany': true,
                'showName': true,
                'alias': 'testName',
                'company': 'smart iot',
                'comment': '',
                'headline': '',
                'date': '2020-04-01T15:14:13.664Z'
            },
            {
                'rating': 3.5,
                'showCompany': false,
                'showName': false,
                'alias': 'testName',
                'company': 'smart iot',
                'comment': 'Test comment',
                'headline': '',
                'date': '2020-04-01T15:14:13.664Z'
            },
            {
                'rating': 4,
                'showCompany': false,
                'showName': true,
                'alias': 'testName',
                'company': 'smart iot',
                'comment': 'Another test comment',
                'headline': 'Test headline',
                'date': '2020-04-01T15:14:13.664Z'
            },
            {
                'rating': 3.5,
                'showCompany': true,
                'showName': false,
                'company': 'smart iot',
                'comment': 'Test comment',
                'headline': '',
                'date': '2020-04-01T15:14:13.664Z'
            }
        ]
    },
    components: {
        TestPageRoot,
        ReviewItem,
        StarRating
    },
    methods: {},
    template: `
      <test-page-root>
        <div style="padding: 60px;">
          <div v-for="review in reviews" style="padding: 50px 0;">
            <review-item v-bind:review="review">
            </review-item>
          </div>
          <div class="page-end"></div>
        </div>
      </test-page-root>
    `
});
