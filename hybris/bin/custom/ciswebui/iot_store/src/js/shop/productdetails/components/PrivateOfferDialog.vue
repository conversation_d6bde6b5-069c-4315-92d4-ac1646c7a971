<template>
    <portal to="portal-store" v-if="isOpen">
        <div class="private-offer-request-popup"
             tabindex="0"
             v-on:keyup.esc="close">

            <div class="modal-mask">
                <div class="modal-wrapper">
                    <div class="modal-container"
                         v-bind:class="{'iframe-loading': pageSpinner.isActive}"
                         v-on:click.stop>

                        <v-row>
                          <v-col cols="12">
                            <p class="text-h2 mb-1">
                              {{$t('shop.productDetails.privateOfferDialog.header')}}
                            </p>
                            <p class="subtitle-1">{{$t('shop.productDetails.privateOfferDialog.description')}}</p>
                          </v-col>
                        </v-row>

                        <div>
                            <v-row>
                              <v-col cols="12">
                                <private-offer-logo v-bind:product="product">
                                </private-offer-logo>
                              </v-col>
                            </v-row>
                            <v-row dense>
                              <v-col cols="12">
                                <p class="text-h3 mb-1">
                                  {{$t('shop.productDetails.privateOfferDialog.requestedConditionTitle')}}
                                </p>
                                <p class="subtitle-1 text--disabled">
                                  {{$t('shop.productDetails.privateOfferDialog.requestedConditionDescription')}}
                                </p>
                              </v-col>

                              <v-col cols="10" xl="5">
                                <CDInput :label="$t('shop.productDetails.privateOfferDialog.fullLicensePriceHelper')"
                                         :error-messages="requestFullPriceError ? requestFullPriceErrorMsg : ''"
                                         :error="requestFullPriceError"
                                         type="number"
                                         min="0"
                                         step="1"
                                         v-on:change="requestFullLicensePriceChange($event)"
                                         data-id="input-item-list-request-price-full">
                                </CDInput>
                              </v-col>

                              <v-col cols="2">
                                <div class="originalPriceDiv item-list-original-price">
                                  <span v-if="isSupportFullLicense" class="originalPrice">{{currency}} {{originalFullLicensePrice}}</span>
                                  <span v-else class="currency">{{currency}}</span>
                                </div>
                              </v-col>

                              <v-col cols="10" xl="5">
                                <CDInput :label="$t('shop.productDetails.privateOfferDialog.fullLicenseQuantityHelper')"
                                         :error-messages="fullLicenseQuantityError ? $t('shop.productDetails.privateOfferDialog.quantityError') : ''"
                                         :error="fullLicenseQuantityError"
                                         type="number"
                                         min="0"
                                         step="1"
                                         oninput="this.value = Math.abs(this.value)"
                                         v-on:change="fullLicenseQuantityChange($event)"
                                         data-id="input-item-list-quantity-full">
                                </CDInput>
                              </v-col>

                              <v-col cols="10" xl="5">
                                <CDInput :label="$t('shop.productDetails.privateOfferDialog.subscriptionPriceHelper')"
                                          :error-messages="requestSubscriptionPriceError ? requestSubscriptionPriceErrorMsg : ''"
                                          :error="requestSubscriptionPriceError"
                                          type="number"
                                          min="0"
                                          step="1"
                                          v-on:change="subscriptionRequestPriceChange($event)"
                                         data-id="input-item-list-request-price-subs">
                                </CDInput>
                              </v-col>


                              <v-col cols="2">
                                <div class="originalPriceDiv item-list-original-price">
                                  <span v-if="isSupportSubscription" class="originalPrice">{{currency}} {{originalSubscriptionPrice}}</span>
                                  <span v-else class="currency">{{currency}}</span>
                                </div>
                              </v-col>

                              <v-col cols="10" xl="5">
                                <CDInput  :label="$t('shop.productDetails.privateOfferDialog.subscriptionQuantityHelper')"
                                          :error-messages="subscriptionQuantityError ? $t('shop.productDetails.privateOfferDialog.quantityError') : ''"
                                          :error="subscriptionQuantityError"
                                          type="number"
                                          min="0"
                                          oninput="this.value = Math.abs(this.value)"
                                          step="1"
                                          v-on:change="subscriptionQuantityChange($event)"
                                          data-id="input-item-list-quantity-subs">
                                </CDInput>
                              </v-col>


                            </v-row>
                        </div>

                        <div>
                          <v-row dense>
                            <CDCheckbox v-model="registerProject"
                                        color="primary"
                                        data-id="checkbox-register-project">
                              <template v-slot:label>
                                <i18n path="shop.productDetails.privateOfferDialog.projectRegistration.enable" tag="div">
                                  <template v-slot:linkToGuide >
                                    <a :href="`${projectRegistrationDocsLink}`"
                                       @click.stop
                                       target="_blank">
                                      {{ $t("shop.productDetails.privateOfferDialog.projectRegistration.learnMore") }}
                                    </a>
                                  </template>
                                </i18n>
                              </template>
                            </CDCheckbox>
                          </v-row>

                          <private-offer-registration v-model="projectRegistration"
                                                      :show-register-project="registerProject"
                                                      :countries="product.enabledCountries">
                          </private-offer-registration>
                        </div>

                      <v-row dense>
                        <v-col cols="12">
                          <p class="text-h3">
                            {{ $t('shop.productDetails.privateOfferDialog.messageTitle') }}
                          </p>
                        </v-col>
                        <v-col cols="12">
                          <div id="messageDiv">
                            <CDTextArea name="privateOfferMessage"
                                        id="privateOfferMessage"
                                        :label="$t('shop.productDetails.privateOfferDialog.messageHelper')"
                                        counter="1000"
                                        v-model="message"
                                        data-id="textarea-private-offer-message">
                            </CDTextArea>
                          </div>
                        </v-col>
                      </v-row>

                      <v-row class="modal-footer">
                            <CDButton color="primary"
                                      v-on:click.prevent.stop="sendRequest"
                                       :disabled="!infoCompleted"
                                      data-id="button-submit">
                                {{ $t('send') }}
                            </CDButton>
                            <CDButton v-on:click="close">
                                {{ $t('cancel') }}
                            </CDButton>
                        </v-row>
                    </div>
                </div>
            </div>
        </div>
    </portal>
</template>

<script lang="ts">
import {Component, Emit, Prop, Vue, Watch} from 'vue-property-decorator';
import {pageSpinner} from 'common/components/spinner';
import {Portal} from 'portal-vue';
import {
  DetailProductData,
  ErrorMessageData,
  PrivateOfferProjectAddressData,
  PrivateOfferProjectRegistrationData
} from 'common/generated-types/types';
import PrivateOfferLogo from 'shop/productdetails/components/PrivateOfferLogo.vue';
import {messageService} from 'common/services';
import {PrivateOfferRequestData, PrivateOfferRequestItemData} from 'shop/resources/privateOfferResource';
import {LicenseType} from 'common/types';
import {PrivateOfferResource} from 'shop/resources';
import PrivateOfferRegistration from 'shop/productdetails/components/PrivateOfferRegistration.vue';
import {shopCoreDataProvider} from 'common/provider';
import {SupportUrlPaths} from 'common/constants';

@Component({
    components: {
        PrivateOfferRegistration,
        Portal,
        PrivateOfferLogo
    }
})
export default class PrivateOfferDialog extends Vue {
    @Prop() show!: boolean;
    @Prop() product!: DetailProductData;

    privateOfferResource = new PrivateOfferResource();

    supportLink = shopCoreDataProvider.data.supportUrl;
    isOpen = false;
    pageSpinner = pageSpinner;
    currency = '';
    defaultNumberValue = -1;
    originalFullLicensePrice = this.defaultNumberValue;
    originalSubscriptionPrice = this.defaultNumberValue;
    message = '';
    requestSubscriptionPrice = this.defaultNumberValue;
    requestFullLicensePrice = this.defaultNumberValue;
    fullLicenseQuantity = this.defaultNumberValue;
    subscriptionQuantity = this.defaultNumberValue;
    messageError = false;
    requestFullPriceError = false;
    requestSubscriptionPriceError = false;
    requestFullPriceErrorMsg = '';
    messageErrorMsg = '';
    requestSubscriptionPriceErrorMsg = '';
    fullLicenseQuantityError = false;
    subscriptionQuantityError = false;

    registerProject = false;

    projectRegistration: PrivateOfferProjectRegistrationData = {
      projectName: '',
      customerName: '',
      plannedStartDate: '',
      siteAddress: {} as PrivateOfferProjectAddressData,
    };

    privateOfferRequestData: PrivateOfferRequestData = {
        appCode: this.product.code,
        messageText: '',
        privateOfferRequestItems: [],
        projectRegistration: this.projectRegistration,
        registerProject: false
    };

    init(): void {
        this.message = '';
        this.requestSubscriptionPrice = this.defaultNumberValue;
        this.requestFullLicensePrice = this.defaultNumberValue;
        this.fullLicenseQuantity = this.defaultNumberValue;
        this.subscriptionQuantity = this.defaultNumberValue;
        this.messageError = false;
        this.requestFullPriceError = false;
        this.requestSubscriptionPriceError = false;
        this.requestFullPriceErrorMsg = '';
        this.requestSubscriptionPriceErrorMsg = '';
        this.fullLicenseQuantityError = false;
        this.subscriptionQuantityError = false;
    }

    mounted(): void {
        this.currency = this.product.currency;
    }

    get projectRegistrationDocsLink(): string {
      return `${this.supportLink}${SupportUrlPaths.PROJECT_REGISTRATION}`;
    }

    get infoCompleted(): boolean {
        return !this.messageError && !this.requestFullPriceError &&
            !this.requestSubscriptionPriceError && !this.fullLicenseQuantityError && !this.subscriptionQuantityError;
    }

    @Watch('message')
    inputMessage(): void {
        const messageLength = this.message.length;
        if (messageLength >= 0 && messageLength <= 1000) {
            this.messageError = false;
            this.privateOfferRequestData.messageText = this.message;
        } else {
            this.messageError = true;
            this.messageErrorMsg = this.$t('shop.productDetails.privateOfferDialog.messageLengthError').toString();
        }
    }

    requestFullLicensePriceChange(requestFullLicensePriceStr: string): void {
        if (requestFullLicensePriceStr.length === 0) {
            this.requestFullLicensePrice = this.defaultNumberValue;
            this.requestFullPriceError = false;
            return;
        }
        const requestFullLicensePrice = parseFloat(requestFullLicensePriceStr);
        if (requestFullLicensePrice < 0) {
            this.requestFullPriceError = true;
            this.requestFullPriceErrorMsg = this.$t('shop.productDetails.privateOfferDialog.priceNegativeError').toString();
        } else if (requestFullLicensePrice === this.originalFullLicensePrice) {
            this.requestFullPriceError = true;
            this.requestFullPriceErrorMsg = this.$t('shop.productDetails.privateOfferDialog.priceEqualsError').toString();
        } else {
            this.requestFullLicensePrice = requestFullLicensePrice;
            this.requestFullPriceError = false;
        }
    }

    fullLicenseQuantityChange(fullLicenseQuantity: string): void {
        if (fullLicenseQuantity.length === 0) {
            this.fullLicenseQuantityError = false;
            this.fullLicenseQuantity = this.defaultNumberValue;
            return;
        }
        if (parseFloat(fullLicenseQuantity) < 0) {
            this.fullLicenseQuantityError = true;
        } else {
            this.fullLicenseQuantityError = false;
            this.fullLicenseQuantity = parseInt(fullLicenseQuantity);
        }
    }

    subscriptionRequestPriceChange(requestSubscriptionPriceStr: string): void {
        console.log('change');
        if (requestSubscriptionPriceStr.length === 0) {
            this.requestSubscriptionPrice = this.defaultNumberValue;
            this.requestSubscriptionPriceError = false;
            return;
        }
        const requestSubscriptionLicensePrice = parseFloat(requestSubscriptionPriceStr);
        if (requestSubscriptionLicensePrice < 0) {
            this.requestSubscriptionPriceError = true;
            this.requestSubscriptionPriceErrorMsg = this.$t('shop.productDetails.privateOfferDialog.priceNegativeError').toString();
        } else if (requestSubscriptionLicensePrice === this.originalSubscriptionPrice) {
            this.requestSubscriptionPriceError = true;
            this.requestSubscriptionPriceErrorMsg = this.$t('shop.productDetails.privateOfferDialog.priceEqualsError').toString();
        } else {
            this.requestSubscriptionPrice = requestSubscriptionLicensePrice;
            this.requestSubscriptionPriceError = false;
        }
    }

    subscriptionQuantityChange(subscriptionQuantity: string): void {
        if (subscriptionQuantity.length === 0) {
            this.subscriptionQuantityError = false;
            this.subscriptionQuantity = this.defaultNumberValue;
            return;
        }
        if (parseFloat(subscriptionQuantity) < 0) {
            this.subscriptionQuantityError = true;
        } else {
            this.subscriptionQuantityError = false;
            this.subscriptionQuantity = parseInt(subscriptionQuantity);
        }
    }

    get isSupportFullLicense(): boolean {
        for (const entry of this.product.licenses) {
            if (entry.licenseType.code === 'FULL') {
                this.originalFullLicensePrice = entry.price;
                return true;
            }
        }
        return false;
    }

    get isSupportSubscription(): boolean {
        for (const entry of this.product.licenses) {
            if (entry.licenseType.code === 'SUBSCRIPTION') {
                this.originalSubscriptionPrice = entry.price;
                return true;
            }
        }
        return false;
    }

    @Watch('show')
    async onShowChanged(val: boolean): Promise<void> {
        if (val) {
            pageSpinner.start(0);
            this.isOpen = true;
            this.pageSpinner.stop();
            this.init();
        }
    }


    @Emit('modal-closed')
    close(): void {
        this.isOpen = false;
    }

    sendRequest(): void {
        this.privateOfferRequestData.privateOfferRequestItems = [];
        if (this.requestFullLicensePrice !== this.defaultNumberValue || this.fullLicenseQuantity !== this.defaultNumberValue) {
            const fullLicenseItem: PrivateOfferRequestItemData = {
                quantity: this.fullLicenseQuantity,
                originalPrice: this.originalFullLicensePrice,
                requestPrice: this.requestFullLicensePrice,
                licenseType: LicenseType.FULL
            };
            this.privateOfferRequestData.privateOfferRequestItems.push(fullLicenseItem);
        }
        if (this.requestSubscriptionPrice !== this.defaultNumberValue || this.subscriptionQuantity !== this.defaultNumberValue) {
            const subscriptionItem: PrivateOfferRequestItemData = {
                quantity: this.subscriptionQuantity,
                originalPrice: this.originalSubscriptionPrice,
                requestPrice: this.requestSubscriptionPrice,
                licenseType: LicenseType.SUBSCRIPTION
            };
            this.privateOfferRequestData.privateOfferRequestItems.push(subscriptionItem);
        }

        this.privateOfferRequestData.registerProject = this.registerProject;
        if (!this.registerProject) {
          this.privateOfferRequestData.projectRegistration = {} as PrivateOfferProjectRegistrationData;
        }

        this.pageSpinner.start(0);
        this.privateOfferResource.createPrivateOffer(this.privateOfferRequestData)
            .then(() => {
                messageService.success(this.$t('shop.productDetails.privateOfferDialog.success').toString(), true);
                this.pageSpinner.stop();
                window.location.reload();
            })
            .catch((error) => {
                pageSpinner.stop();
                messageService.clearAll();
                if (error.response && error.response.data) {
                    const errors = error.response.data as ErrorMessageData[];
                    if (error.response.status === 400 && errors.length > 0) {
                        messageService.error(errors.map(e => e.message));
                    } else if (error.response.status === 404 && error.response.data.message) {
                        messageService.error(error.response.data.message);
                    } else {
                        messageService.error(this.$t('backendError') as string);
                    }
                } else {
                    messageService.error(this.$t('backendError') as string);
                }
            });
    }

}
</script>

<style scoped lang="scss">
@import 'common/simpleDialog';
@import "common/design";
@import "shop/core/constants";
@import "common/layout";
@import "common/alerts";

.modal-container {
    // hardcoded max-width value as component will be deleted
    max-width: 991px;
    max-height: 860px;
    padding: 30px 30px;
    height: 90vh;
    width: 95vw;
    overflow: auto;

    &.iframe-loading {
        opacity: 0;
    }

    .originalPriceDiv {
      width: 90px;
      padding-top: 15px;

      .originalPrice {
        font-size: 18px;
        text-decoration: line-through;
      }
    }
    .currency {
      font-size: 18px;
    }

    .modal-footer {
        button {
            float: right;
            margin-left: 30px;
        }
    }
}

</style>
