import {
    createInlineUiTestPage,
    scrollDown,
    acceptCookieBanner,
    resetMouseCoords,
    scrollIntoView
} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import productsData from 'common/testtools/scenariosstore/productsData';
import {cloneDeep} from 'lodash';
import * as Sizes from 'common/testtools/testScreenSizes';
import navigationItemsForAnonymousUser from 'common/testtools/scenariosstore/navigationItemsForAnonymousUser.json';

const REST_URL = '/shop/api/products?page=0&sortBy=publishDate';
const productsRestHandler = (request, productsDataResult) => {
    if (request.url().endsWith(REST_URL)) {
        request.respond({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify(productsDataResult)
        });
    } else {
        request.continue();
    }
};

describe("products page with cookie banner", () => {
    it("looks as expected", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData));

        await createInlineUiTestPage(testMap.Products, [{url: REST_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });
});

describe("products page", () => {
    it("looks as expected", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData));

        await createInlineUiTestPage(testMap.Products, [{url: REST_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });

    it('shows mobile filters trigger on mobile', async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData));
        await createInlineUiTestPage(testMap.Products, [{url: REST_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 380, height: 1000});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot("mobile trigger");
    });

    it('shows mobile filters using portal', async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData));
        await createInlineUiTestPage(testMap.Products, [{url: REST_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 380, height: 1000});
        await page.waitFor(1000);
        await page.click('[data-id="button-toggle-filters"]');
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot("mobile filters");
    });

    it("has a footer and looks as expected", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData));

        await createInlineUiTestPage(testMap.Products, [{url: REST_URL, data: productsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 1200, height: 1809});
        await page.waitFor(3000);
        expect(await page.screenshot()).toMatchImageSnapshot("extra large size");

        await page.setViewport({width: 992, height: 1906});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("large size");

        await page.setViewport({width: 720, height: 2514});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("medium size");

        await page.setViewport({width: 576, height: 2495});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("small size");
    });

    it("sorting", async () => {
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData));

        await createInlineUiTestPage(testMap.Products);
        await acceptCookieBanner();

        await page.setViewport({width: 1200, height: 1809});
        await page.click('.selected-text');
        await page.waitFor(500);
        expect(await page.screenshot()).toMatchImageSnapshot("dropdown open");
    });

    it('Looks as expected for anonymous user', async () => {
        let data = cloneDeep(testMap.Products);
        data.coreData.userName = null;

        data.coreData.navigationItems = navigationItemsForAnonymousUser;

        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, productsData));

        await createInlineUiTestPage(data, [{url: REST_URL, data: data.coreData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 1220, height: 1906});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("anonymous large size");

        await page.setViewport({width: 576, height: 2495});
        await page.waitFor(600);

        await page.click('.cd-icon-user');
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("anonymous small size");
    });

    it("app cards with company profile links looks as expected", async () => {
        const coreData = cloneDeep(testMap.Products.coreData);
        coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        const dataWithProfilesLinks = cloneDeep(productsData);
        dataWithProfilesLinks.products.map(product => {
            product.company.hasPublishedProfile = true;
            product.company.profileUrl = '/profile-url/uid';
        });
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, dataWithProfilesLinks));

        await createInlineUiTestPage({...testMap.Products, pageData: dataWithProfilesLinks, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });

    it("load more button", async () => {
        const coreData = cloneDeep(testMap.Products.coreData);
        coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        const dataWithProfilesLinks = cloneDeep(productsData);
        dataWithProfilesLinks.totalNumberOfResults=4;
        dataWithProfilesLinks.totalNumberOfPages=2;
        dataWithProfilesLinks.pageSize=2;
        dataWithProfilesLinks.products.length=2;

        dataWithProfilesLinks.products.map(product => {
            product.company.hasPublishedProfile = true;
            product.company.profileUrl = '/profile-url/uid';
        });
        await page.setRequestInterception(true);
        page.on('request', (request) => productsRestHandler(request, dataWithProfilesLinks));

        await createInlineUiTestPage({...testMap.Products, pageData: dataWithProfilesLinks, coreData: coreData});
        await acceptCookieBanner();
        await scrollDown();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });
});
