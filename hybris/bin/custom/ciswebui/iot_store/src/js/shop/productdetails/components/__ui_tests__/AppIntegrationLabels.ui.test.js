describe("AppIntegrationLabels", () => {
    it("have correct views", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-AppIntegrationLabels');

        await page.setViewport({width: 1440, height: 700});
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("Labels on 'XL' viewport");

        await page.setViewport({width: 576, height: 700});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("Labels on 'S' viewport");

    });
});
