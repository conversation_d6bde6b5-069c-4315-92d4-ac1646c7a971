<template>
  <div class="get-own-app-section" v-if="isDownloadOwnAppsEnabled">
    <v-container :fluid="true" class="pa-0">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <h2 class="label mb-4">{{ $t('shop.productDetails.getOwnAppSection.title') }}</h2>
        <div class="description mb-4">
          <span>{{ $t('shop.productDetails.getOwnAppSection.description') }}</span>
        </div>
        <div class="section-quantity d-flex mb-4">
          <v-row>
            <v-col cols="3">
              <CDInput v-model.number="currentQuantity"
                       type="number"
                       min="0"
                       step="1"
                       data-id="input-quantity-get-own-apps"
                       :label="$t('shop.productDetails.getOwnAppSection.quantity')">
              </CDInput>
            </v-col>
            <v-col cols="3">
              <CDButton color="primary"
                        :disabled="!isValidQuantity(currentQuantity)"
                        data-id="button-get-own-apps"
                        v-on:click="addToCart">{{ $t('shop.productDetails.getOwnAppSection.getAppsButtonText') }}
              </CDButton>
            </v-col>
          </v-row>
        </div>
        <CDNotificationPersistent class="notification" type="info">{{
          $t('shop.productDetails.getOwnAppSection.infoText')
          }}
        </CDNotificationPersistent>
      </v-container>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {
  productDataService,
  userPermissionService,
  messageService,
  inputValidationService
} from 'common/services';
import {DetailProductData, LicenseQuantityData} from 'common/generated-types/types';
import {CDInput, CDButton, CDNotificationPersistent} from 'cd-system';
import {pageSpinner} from 'common/components/spinner';
import {CartResource} from 'shop/resources';
import {ShopRoute} from 'common/constants';
import {LicenseType, PurchasableLicenseData} from 'common/types';

@Component({
  components: {
    CDInput,
    CDButton,
    CDNotificationPersistent
  }
})
export default class GetOwnAppSection extends Vue {
  @Prop() product!: DetailProductData;

  productDataService = productDataService;
  userPermissionService = userPermissionService;
  quantity = '';
  license = this.getFullLicense;
  isDownloadOwnAppsEnabled = this.product?.ownCompanyAppAndCanBuy;

  isValidQuantity(quantity: number): boolean {
    return inputValidationService.isValidQuantity(String(quantity));
  }

  get currentQuantity(): number {
    return Number(this.quantity);
  }

  set currentQuantity(newQuantity: number) {
    this.quantity = String(newQuantity);
    this.license.quantity = newQuantity;
  }

  addToCart(): void {
    pageSpinner.start(0);
    CartResource.addToCart(new Array(this.license as LicenseQuantityData)).then(() => {
      const addedToCartMessage = this.$t('shop.productDetails.pricingSection.addToCartSuccess') as string;

      messageService.success(addedToCartMessage, true, true);
      window.location.assign(ShopRoute.CART);
    }).catch((error) => {
      pageSpinner.stop();
      messageService.clearAll();

      const statusCode = error.response.status;

      if (statusCode === 400) {
        messageService.errorResponse(error.response.data, this.$i18n, true);
        window.location.assign(ShopRoute.CART);
      } else if (statusCode === 422) {
        messageService.errorResponse(error.response.data, this.$i18n);
      } else {
        messageService.error(this.$t('shop.productDetails.pricingSection.addToCartGeneralError') as string);
      }
    });
  }

  get getFullLicense(): PurchasableLicenseData {
    return this.product.licenses.find(license => license.licenseType.code === LicenseType.FULL) as PurchasableLicenseData;
  }

}
</script>

<style scoped lang="scss">
@import "shop/core/common";

.get-own-app-section {
  background-color: var(--v-grey-lighten3);
  box-sizing: border-box;
  padding: 60px 0 90px 0;
  @include respond-to('M') {
    padding: 70px 0 110px 0;
  }
}
</style>
