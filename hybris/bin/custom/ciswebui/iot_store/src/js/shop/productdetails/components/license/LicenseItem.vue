<template>
  <div class="license-item" v-if="isThirdPartyLicense">

    <v-row class="no-gutters">
      <v-col cols="12" lg="8">
        <div class="cell-license-info">
          <div class="license-info">
            <h2 class="name text-h3 mb-2">{{license.name}}</h2>
            <p class="summary">{{ tm(licenseSummary) }}</p>
          </div>
        </div>
      </v-col>

      <v-col cols="12" lg="2" offset-lg="2">
        <div v-if="displayLicenses || isTrialLicense" class="cell-price-info">
          <div class="price-info text-lg-right">
            <template v-if="isCommercialLicense">
              <h2 class="discount-price text-h3" v-if="currentDiscount > 0">
                {{license.currencyIsocode}} {{$n(currentDiscount, 'price', localeForFormats)}}
              </h2>
              <h2 class="text-h3" :class="{ 'text-decoration-line-through' : currentDiscount > 0 }">
                {{license.currencyIsocode}} {{$n(license.price, 'price', localeForFormats)}}</h2>
              <p class="price-text mb-0">
                {{ licenseType === LicenseType.SUBSCRIPTION ?
                $t('shop.productDetails.pricingSection.perYear') :
                $t('shop.productDetails.pricingSection.pricetext')
                }}
              </p>
            </template>
            <h2 class="text-h3" v-else-if="isTrialLicense">{{$t('shop.productDetails.pricingSection.free')}}</h2>
          </div>
        </div>
      </v-col>
    </v-row>

    <v-row v-if="isAllowedToBuyLicense">
      <v-col cols="12">
        <div class="cell-quantity" ref="input">
          <v-row>
            <v-col cols="12" md="3">
              <CDInput class="mt-4"
                       :name="license.licenseType.code"
                       v-model.number="license.quantity"
                       type="number"
                       min="0"
                       step="1"
                       :label="$t('shop.productDetails.pricingSection.quantity')"
                       oninput="this.value = Math.abs(this.value)"
                       v-on:input="updateQuantityIfValid(license, $event, license.licenseType.code)">
              </CDInput>
            </v-col>
          </v-row>
          <div v-if="applyQuantityValidation && quantityValidationMessage !== ''" class="quantity-validation">
            <icon-warning></icon-warning>
            <div>{{ quantityValidationMessage }}</div>
          </div>
        </div>
        <license-details v-if="isAllowedToBuyLicense"
                         class="cell-license-details"
                         :license-code="license.licenseType.code">
        </license-details>
        <CDNotificationPersistent
            data-id="text-future-prices"
            v-if="(licenseType === LicenseType.SUBSCRIPTION) && license.futurePrices && license.futurePrices.length"
            type="info">
          {{$t('shop.productDetails.pricingSection.license.subscription.futurePriceText',
                { futurePrice: futurePrice, startDate: futurePriceStartingDate }
            )}}
        </CDNotificationPersistent>
      </v-col>
    </v-row>

    <v-row v-if="license.licenseType.code === LicenseType.FULL && showDiscounts">
      <v-col cols="12">
        <scale-prices :license="license">
        </scale-prices>
      </v-col>
    </v-row>

    <permission-text class="permission-info-container" v-if="isUnpayableSubscription">
      <template v-slot:content>
        <p class="permission-info-text">
          {{$t('shop.productDetails.pricingSection.subscriptionPermissionText')}}
        </p>
      </template>
    </permission-text>

    <v-row v-if="isAllowedToBuyLicense" class="cell-cart-action">
      <v-col cols="12" class="d-md-flex justify-md-end">
        <CDButton class="hydrated"
                  color="primary"
                  :block="$vuetify.breakpoint.smAndDown"
                  :disabled="(!license.quantity) || (quantityValidationMessage !== '' )"
                  v-on:click="addToCart">
          {{$t('shop.productDetails.pricingSection.addToCart')}}
        </CDButton>
      </v-col>
    </v-row>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {LicenseType} from 'common/types';
import {
  i18nService,
  inputValidationService,
  licenseTypeService,
  messageService,
  userPermissionService
} from 'common/services';
import {
  DetailProductData,
  LicensePurchasability,
  LicenseQuantityData,
  PriceRowData,
  ShopCoreData, StoreErrorResponseData
} from 'common/generated-types/types';
import {PurchasableLicenseData} from 'common/types.ts';
import {PermissionText} from 'common/components';
import InfoIcon from 'common/images/boxed/icon-24-info-d.svg';
import IconWarning from 'common/images/boxed/icon-24-warning-d.svg';
import {pageSpinner} from 'common/components/spinner';
import {ShopRoute} from 'common/constants';
import {CartResource} from 'shop/resources';
import {LicenseDetails} from 'shop/productdetails/components/license';
import ScalePrices from './ScalePrices.vue';
import {ShopCoreDataProvider} from 'common/provider/coreDataProvider';
import { useI18n } from 'vue-i18n-bridge';

@Component({
  components: {
    ScalePrices,
    LicenseDetails,
    InfoIcon,
    PermissionText,
    IconWarning
  }
})
export default class LicenseItem extends Vue {
  @Prop() license!: PurchasableLicenseData;
  @Prop() displayLicenses!: boolean;
  @Prop() product!: DetailProductData;

  coreData: ShopCoreData = ShopCoreDataProvider.data;
  inputQuantity = '';
  quantityValidationMessage = '';
  userPermissionService = userPermissionService;
  licenseTypeService = licenseTypeService;
  LicenseType = LicenseType;
  $refs!: {
    input: HTMLDivElement;
  };

  localeForFormats = '';

  mounted(): void {
    this.localeForFormats = i18nService.getLocaleForFormats();
  }

  tm(code: string) {
    const { tm } = useI18n();
    return tm(code);
  }

  get i18nService() {
    return i18nService;
  }

  get licenseSummary(): string {
    return `shop.productDetails.pricingSection.license.${this.licenseType.toLowerCase()}.summary`;
  }

  get isAllowedToBuyLicense(): boolean {
    return userPermissionService.canPurchaseLicense(this.license) && !this.product?.ownCompanyAppAndCanBuy;
  }

  get isCommercialLicense(): boolean {
    return licenseTypeService.isCommercialLicense(this.licenseType);
  }

  get isThirdPartyLicense(): boolean {
    return licenseTypeService.isThirdPartyLicense(this.licenseType);
  }

  get isTrialLicense(): boolean {
    return licenseTypeService.isTrialLicense(this.licenseType);
  }

  get applyQuantityValidation(): boolean {
      return this.isCommercialLicense;
  }

  get licenseType(): LicenseType {
    return this.license.licenseType.code as LicenseType;
  }

  get isUnpayableSubscription(): boolean {
    return this.licenseType === LicenseType.SUBSCRIPTION &&
        this.license.purchasability === LicensePurchasability.UNPAYABLE;
  }

  get currentQuantity(): number {
    return Number(this.inputQuantity);
  }

  get showDiscounts(): boolean {
    return this.license.scalePrices && this.license.scalePrices.length > 0;
  }

  get futurePrice() {
    const currencySymbol = this.earliestFuturePrice.currencyIsoCode;
    return `${currencySymbol} ${this.earliestFuturePrice.price}`;
  }

  get futurePriceStartingDate() {
    return this.$d(new Date(this.earliestFuturePrice.startDate), 'short', this.localeForFormats).toString();
  }

  get earliestFuturePrice(): PriceRowData {
    return this.license.futurePrices.sort(
        (row1 : PriceRowData, row2 : PriceRowData) => new Date(row2.startDate).getTime() - new Date(row1.startDate).getTime(),
    )[0];
  }

  get currentDiscount(): number {
    if (this.licenseType === LicenseType.FULL) {
      const priceRow = this.license.scalePrices
          ? this.license.scalePrices.filter(scalePrice => scalePrice.discountPercent > 0 && scalePrice.minQuantity <= this.currentQuantity)
          : [];
      return priceRow.length > 0
          ? priceRow[priceRow.length - 1].discountPrice
          : 0;
    } else {
      return 0;
    }
  }

  updateQuantityIfValid(license: PurchasableLicenseData, newQuantity: string, licenseType: string): void {
    if (licenseType === LicenseType.FULL) {
      const maxQuantityFull = this.coreData.allowedMaxQuantityLineItemFullLicense;
      if (Number(newQuantity) > maxQuantityFull) {
        this.quantityValidationMessage = this.$t('shop.productDetails.pricingSection.quantityInvalid', [maxQuantityFull]) as string;
        return;
      }
      this.quantityValidationMessage = '';
    }

    if (licenseType === LicenseType.SUBSCRIPTION) {
      const maxQuantitySubscription = this.coreData.allowedMaxQuantityLineItemSubsLicense;
      if (Number(newQuantity) > maxQuantitySubscription) {
        this.quantityValidationMessage = this.$t('shop.productDetails.pricingSection.quantityInvalid', [maxQuantitySubscription]) as string;
        return;
      }
      this.quantityValidationMessage = '';
    }

    const oldQuantity = license.quantity;

    if (inputValidationService.isQuantityOrZero(newQuantity)) {
      license.quantity = Number(newQuantity);
      this.inputQuantity = newQuantity;
      this.$forceUpdate();
      return;
    }
    license.quantity = newQuantity;
    this.inputQuantity = newQuantity;
    this.$forceUpdate();

    this.$nextTick(() => {
      license.quantity = oldQuantity;
      this.$forceUpdate();
    });
  }

  addToCart(): void {
    pageSpinner.start(0);

    CartResource.addToCart(new Array(this.license as LicenseQuantityData)).then(() => {
      const discount = this.license.scalePrices
          ? this.license.scalePrices
              .filter(v => v.discountPercent > 0)
              .sort((v1, v2) => v2.minQuantity - v1.minQuantity)
              .find(v => v.minQuantity <= this.license.quantity)
          : undefined;

      const addedToCartMessage = discount
          ? this.$t('shop.productDetails.pricingSection.addToCartSuccessWithDiscount', {percent: discount.discountPercent}) as string
          : this.$t('shop.productDetails.pricingSection.addToCartSuccess') as string;

      messageService.success(addedToCartMessage, true, true);
      window.location.assign(ShopRoute.CART);
    }).catch((error) => {
      pageSpinner.stop();
      messageService.clearAll();

      const statusCode = error.response.status;

      if (statusCode === 400) {
        messageService.errorResponse(error.response.data as StoreErrorResponseData, this.$i18n, true);
        window.location.assign(ShopRoute.CART);
      } else if (statusCode === 422) {
        messageService.errorResponse(error.response.data as StoreErrorResponseData, this.$i18n);
      } else {
        messageService.error(this.$t('shop.productDetails.pricingSection.addToCartGeneralError') as string);
      }
    });
  }

  focus(): void {
    if (this.$refs.input) {
      this.$refs.input.focus();
      const inputElement = this.$refs.input.querySelector('input') as HTMLInputElement;
      if (inputElement) {
        inputElement.select();
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "common/design";
@import "shop/core/common";
@import "shop/core/constants";

.license-item {

  .permission-info-container {
    grid-column: 1 / 4;
  }

  .cell-quantity {
    grid-column: 1 / 3;
    padding-bottom: 25px;

    .quantity-validation {
      font-size: 14px;
      color: var(--v-error-base);
      display: flex;

      div {
        margin-left: 14px;
        margin-top: 3px;
      }
    }
  }

  .cell-license-details {
    grid-column: 1/-1;
    margin-bottom: $spacing-s;
  }

  .scale-prices-container {
    grid-column: 1 / -1;
    @include respond-to('M') {
      grid-column: 1/2;
    }
  }
}
</style>
