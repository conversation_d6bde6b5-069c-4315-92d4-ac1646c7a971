<template>
    <CDBlock class="follow-app-changes">
        <CDToggle
            data-id="toggle-follow-app"
            v-model="subscribed"
            @change="toggleNotificationSubscription(subscribed)"
            :label="labelKey()"
            class="mt-0">
        </CDToggle>
        <p id="summary" class="mb-0">{{ summaryKey() }}</p>
    </CDBlock>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {productDataService} from 'common/services/productDataService';
import {pageSpinner} from 'common/components/spinner';
import followAppSubscriptionResource from 'shop/resources/followAppSubscriptionResource';
import {ErrorMessageData} from 'common/generated-types/types';
import {messageService} from 'common/services';
import {StoreDataProvider} from 'shop/resources';
import {FollowAppChangesData} from 'common/types';

@Component
export default class FollowAppChangesSection extends Vue {
  followAppChangesData = { code: '', name: '', followAppData: { subscribed:false }} as FollowAppChangesData;

  get subscribed(): boolean {
      return this.followAppChangesData.followAppData.subscribed;
  }
  set subscribed(follow: boolean) {
      this.followAppChangesData.followAppData.subscribed = follow;
  }

  created(): void {
    this.retrieveNotificationSubscriptionStatus();
  }

  retrieveNotificationSubscriptionStatus(): void {
    const followAppChangesData = productDataService.followAppChangesData(StoreDataProvider.pageData);
    if (followAppChangesData) {
      this.followAppChangesData = followAppChangesData;
      this.subscribed = followAppChangesData.followAppData?.subscribed;
    }
  }

  showMessage(follow: boolean): void {
    const appType = this.getAppType();
    const i18nKey = follow ?
        'shop.productDetails.followAppSection.' + appType + '.notification.success.follow':
        'shop.productDetails.followAppSection.' + appType + '.notification.success.unfollow';
    messageService.success(this.$t(i18nKey, { appName: this.followAppChangesData.name }) as string, false, true);
  }

  labelKey(): string {
    const appType = this.getAppType();
    return this.$t('shop.productDetails.followAppSection.'+ appType + '.label') as string;
  }

  summaryKey(): string {
    const appType = this.getAppType();
    return this.$t('shop.productDetails.followAppSection.'+ appType + '.summary') as string;
  }

  private getAppType() {
    return productDataService.isToolApp(StoreDataProvider.pageData) ? 'tool' : 'app';
  }

  toggleNotificationSubscription(follow: boolean): void {
    pageSpinner.start(0);
    messageService.clearAll();
    followAppSubscriptionResource.updateFollowAppSubscriptionForCurrent(this.followAppChangesData.code, follow)
        .then(() => {
          this.subscribed = follow;
          this.showMessage(this.subscribed);
        })
        .catch(error => {
          this.subscribed = this.followAppChangesData.followAppData.subscribed;
          const messages = error.response?.data?.map((e: ErrorMessageData) => e.message);
          messageService.error(messages || (this.$t('backendError') as string));
        }).finally(() => {
      pageSpinner.stop();
    })
    ;
  }
}

</script>
