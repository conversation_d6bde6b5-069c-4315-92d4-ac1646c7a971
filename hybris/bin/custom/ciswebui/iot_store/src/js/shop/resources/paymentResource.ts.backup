import util from 'common/util';

import {AxiosResponse} from "axios";
import {PaymentMethodType, PaymentProvider} from 'common/generated-types/types';
import {
    CreatePaymentInfoResponse,
    FinalizePaymentInfoCreationResponse,
    SepaMandatePayload
} from 'common/types';

let axios = util.axios;

export const PAYMENT_INFOS_RESOURCE: string = `/shop/payment-infos`;
//TODO update to an actual API
export const SEPA_MANDATE_RESOURCE: string = `http://localhost:8081/api/v1/mandates`;

let paymentResource = {
    deletePaymentMethod(paymentInfoId: string): Promise<AxiosResponse<void>> {
        const path = `${PAYMENT_INFOS_RESOURCE}/${paymentInfoId}`;
        return axios.delete(path);
    },

    setDefaultPaymentMethod(paymentInfoId: string): Promise<AxiosResponse<object>> {
        const path = `${PAYMENT_INFOS_RESOURCE}/${paymentInfoId}/default`;
        return axios.post(path);
    },

    removeDefaultPaymentMethod(paymentInfoId: string): Promise<AxiosResponse<object>> {
        const path = `${PAYMENT_INFOS_RESOURCE}/${paymentInfoId}/default`;
        return axios.delete(path);
    },

    createPaymentInfo(paymentProvider: PaymentProvider, paymentMethod: PaymentMethodType): Promise<AxiosResponse<CreatePaymentInfoResponse>> {
        const path = `${PAYMENT_INFOS_RESOURCE}`;
        return axios.post(path, {
            paymentProvider,
            paymentMethod
        });
    },

    /**
     * Finalizes the creation of payment infos from the corresponding drafts.
     *
     * @param paymentDraftIds - The payment draft ids to update.
     */
    finalizePendingPaymentInfosCreation(paymentDraftIds: string[]): Promise<AxiosResponse<FinalizePaymentInfoCreationResponse>> {
        const path = `${PAYMENT_INFOS_RESOURCE}/drafts/finalize`;
        return axios.post(path, {
            'paymentDraftIds': paymentDraftIds
        });
    },

    // SEPA Mandate API calls
    /**
     * Initialize a draft SEPA mandate
     * @returns Promise with SepaMandateDto containing auto-generated mandate reference
     */
    initializeSepaMandateDraft(companyId: string): Promise<AxiosResponse<any>> {
        const path = `${SEPA_MANDATE_RESOURCE}/initialize`;
        return axios.post(path, null, {
            params: {
                companyId: companyId
            }
        });
    },

    /**
     * Update or finalize a SEPA mandate
     * @param reference - The mandate reference
     * @param payload - The mandate data (can be partial for updates or complete for finalization)
     * @returns Promise with updated/finalized SepaMandateDto
     */
    updateOrFinalizeSepaMandateByReference(reference: string, payload: SepaMandatePayload): Promise<AxiosResponse<any>> {
        const path = `${SEPA_MANDATE_RESOURCE}/${reference}/activate`;
        return axios.post(path, payload);
    },

};

export default paymentResource;