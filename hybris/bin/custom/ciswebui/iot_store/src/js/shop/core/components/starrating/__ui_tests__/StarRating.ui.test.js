describe('StarRating', () => {
    it('is displayed correctly', async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-core-components-starrating-__ui_tests__-StarRating');
        expect(await page.title()).toBe('UI test - shop-core-components-starrating-__ui_tests__-StarRating');

        await page.waitFor(300);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });
});