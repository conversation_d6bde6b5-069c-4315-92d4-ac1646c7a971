<template>
  <div class="company-profile-header pt-16">
    <v-container fluid class="header-content">
      <v-row class="mx-2">
        <v-col cols="12">
          <v-img v-if="hasLogo"
                 max-height="114"
                 max-width="114"
                 class="rounded-lg"
                 data-id="image-company-logo"
                 :src="logoUrl"></v-img>
          <div v-else
               data-id="image-company-logo-placeholder">
            <logo-place-holder>
            </logo-place-holder>
          </div>

        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import LogoPlaceHolder from 'common/images/boxed/company-logo-placeholder.svg';
import {isEmpty} from 'lodash';

@Component({
  components: {
    LogoPlaceHolder
  }
})
export default class CompanyProfileHeader extends Vue {
  @Prop() logoUrl!: string;

  get hasLogo(): boolean {
    return !isEmpty(this.logoUrl);
  }
}
</script>
<style scoped lang="scss">
@import "common/design";
@import "shop/core/constants";

.company-profile-header {
  background-blend-mode: multiply;

  background: repeating-linear-gradient(
          var(--v-grey-lighten2),
          var(--v-grey-lighten2) 74.9%,
          var(--v-grey-lighten5) 75.1%,
          var(--v-grey-lighten5) 100%
  );

  // hardcoded breakpoint as component will be deleted
  .header-content {
    max-width: 1200px;
  }
}
</style>
