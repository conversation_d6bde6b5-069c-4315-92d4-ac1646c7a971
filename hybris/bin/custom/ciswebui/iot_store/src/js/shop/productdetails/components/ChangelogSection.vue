<template>
    <div class="changelog">
        <div class="collapse-container" ref="changelog">
            <h2 class="label mb-2">
                {{$t('shop.productDetails.changelogSection.label')}}
            </h2>
            <div v-for="changelog in changelogs" v-bind:key="changelog.version" class="mb-4">
                <div class="separator" v-if="changelog.version !== changelogs[0].version"></div>
                <h5 class="mb-2">
                    {{$t('shop.productDetails.changelogSection.version')}} {{changelog.version}}
                    <span class="sub-title grey--text text--darken-2 ml-4">{{ $d(new Date(changelog.creationDate), 'short') }}</span>
                </h5>
                <div class="rich-text" v-html="changelog.description" v-transformExternalLinks></div>
            </div>

            <div class="show-more" ref="morechangelog">
                <div v-show="!collapsibleOpened" class="gradient"></div>
            </div>
        </div>
        <CDButtonText color="primary" v-on:click="expandChangelog" class="more-button" ref="morebutton">
            {{ $t(collapsibleWord) }}
        </CDButtonText>
    </div>
</template>

<script lang="ts">
    import def from 'common/defaults';
    import anime from 'animejs';
    import {Component, Vue, Prop} from 'vue-property-decorator';
    import {commercefacades} from 'common/generated-types/types';
    import ChangelogData = commercefacades.ChangelogData;
    import {CDButtonText} from 'cd-system';

    @Component
    export default class Changelog extends Vue {
        @Prop() changelogs!: ChangelogData[];

        readonly changelogHeight = 300;
        collapsibleOpened = false;
        animation = anime({targets: []});

        $refs!: {
            morechangelog: HTMLElement;
            changelog: HTMLElement;
            morebutton: CDButtonText;
        };

        mounted(): void {
            const interval = setInterval(() => {
                if (this.$refs.changelog.offsetHeight > this.changelogHeight) {
                    this.$refs.changelog.style.height = this.changelogHeight + 'px';
                    this.$refs.morechangelog.style.display = 'block';
                    clearInterval(interval);
                } else {
                    this.$refs.morebutton.$el.setAttribute('style', 'display: none');
                    clearInterval(interval);
                }
            }, 50);
        }

        expandChangelog(): void {
            anime({
                targets: this.$refs.changelog,
                height: this.collapsibleOpened ? this.changelogHeight + 'px' : this.$refs.changelog.scrollHeight  + 30,
                delay: 100,
                duration: 150,
                easing: def.animation.easeout,
                complete: () => {
                    this.collapsibleOpened = !this.collapsibleOpened;
                }
            });
        }

        get collapsibleWord(): string {
            return this.collapsibleOpened ? 'shop.productDetails.changelogSection.less' : 'shop.productDetails.changelogSection.more';
        }

    }
</script>

<style lang="scss" scoped>
   .collapse-container {
       position: relative;
       overflow: hidden;
       width: 100%;
       ul {
           list-style: disc;
           margin-left: 15px;
       }
       ol {
           list-style: decimal;
           margin-left: 15px;
       }
   }

   .show-more {
       width: 100%;
       position: absolute;
       bottom: 0;
       display: none;
       .gradient {
           height: 60px;
           width: 100%;
           background: linear-gradient(rgba(255,255,255,0), white);
       }
   }
</style>
