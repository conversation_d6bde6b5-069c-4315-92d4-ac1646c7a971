<template>
  <div class="review" ref="reviewForm">
    <div class="review-form">

      <h3 class="mb-2">{{ $t('shop.productDetails.reviewCreationForm.create') }}</h3>

      <p>{{ $t('shop.productDetails.reviewCreationForm.ratingHeadline') }}</p>

      <star-rating-selection
          v-on:ratingchanged="updateRating"
          v-bind:selected-rating="reviewData.rating">
      </star-rating-selection>

      <CDInput class="mt-4"
               name="headline"
               v-model="reviewData.headline"
               :label="$t('shop.productDetails.reviewCreationForm.headline')"
               :counter="headlineMaxLimit"
               :error-messages="errorMessages.headline">
      </CDInput>

      <CDTextArea name="comment"
                  v-model="reviewData.comment"
                  :label="$t('shop.productDetails.reviewCreationForm.comment')"
                  :counter="commentMaxLimit"
                  :error-messages="errorMessages.comment">
      </CDTextArea>

      <div class="include-checkboxes">
        <CDCheckbox
            color="primary"
            data-id="checkbox-show-name"
            v-model="reviewData.showName"
            :label="$t('shop.productDetails.reviewCreationForm.showName')">
        </CDCheckbox>
        <CDCheckbox
            class="mt-0"
            color="primary"
            data-id="checkbox-show-company"
            v-model="reviewData.showCompany"
            :label="$t('shop.productDetails.reviewCreationForm.showCompany')">
        </CDCheckbox>
      </div>

      <div class="submit d-flex justify-space-between">
        <p class="cd-text-small grey--text text--darken-2">{{ $t('shop.productDetails.reviewCreationForm.required') }}</p>
        <CDButton
            color="primary"
            :disabled="submitDisabled || inSubmit"
            data-id="button-review-submit"
            @click="submitReview">
          {{ $t('submit') }}
        </CDButton>
      </div>
    </div>

  </div>
</template>

<script lang="ts">
import {Component, Vue, Prop} from 'vue-property-decorator';
import {messageService} from 'common/services';
import StarRatingSelection from 'shop/core/components/starrating/StarRatingSelection.vue';
import util from 'common/util';
import anime from 'animejs';
import def from 'common/defaults';
import {commercefacades} from 'common/generated-types/types';
import ReviewData = commercefacades.ReviewData;

@Component({components: {StarRatingSelection}})
export default class ReviewCreationForm extends Vue {
  @Prop() postUrl!: string;

  $refs!: {
    reviewForm: HTMLElement
  };

  reviewData: ReviewData = {
    rating: 0.0,
    showCompany: false,
    showName: false,
    comment: '',
    headline: '',
    alias: '',
    id: '',
    date: '',
    company: '',
    principal: {
      uid: '',
      name: ''
    }
  };

  errorMessages: { [key: string]: string; } = {};
  submitDisabled = true;
  inSubmit = false;

  readonly headlineMaxLimit = 255;
  readonly commentMaxLimit = 4000;

  validateMandatoryFields(): void {
    if(this.reviewData && this.reviewData.rating > 0) {
      console.log('Enable submit');
      this.submitDisabled = false;

      delete this.errorMessages.rating;

      return;
    }

    this.submitDisabled = true;

    if(!this.reviewData || this.reviewData.rating <= 0) {
      this.errorMessages.rating = this.$t('shop.productDetails.reviewCreationForm.rating') as string;
    }
  }

  updateRating(rating: number): void {
    console.log('New rating: ' + rating);
    this.reviewData.rating = rating;
    this.validateMandatoryFields();
  }

  submitReview(): void {
    if (this.inSubmit) {
      return;
    }
    this.inSubmit = true;
    util.axios.post(this.postUrl, this.reviewData).then((response) => {
      this.hideForm();
      this.$emit('new-review', response.data);
    }).catch((error) => {
      let message = this.$t('shop.productDetails.reviewCreationForm.generalError');

      if (error.response && error.response.status === 400) {
        this.errorMessages = error.response.data;
        message = this.$t('shop.productDetails.reviewCreationForm.fieldValidation');
      }

      if (error.response.data.review) {
        message = error.response.data.review;
      }

      messageService.error(message as string, true);
    }).then(() => {
      this.inSubmit = false;
    });
  }

  hideForm(): void {
    this.$refs.reviewForm.style.height = this.$refs.reviewForm.offsetHeight.toString();
    anime({
      targets: this.$refs.reviewForm,
      easing: def.animation.easeout,
      duration: 300,
      opacity: 0
    });
    anime({
      targets: this.$refs.reviewForm,
      easing: def.animation.easeout,
      duration: 150,
      delay: 300,
      height: 0,
      complete: ()=>{
        this.$refs.reviewForm.style.display = 'none';
      }
    });
  }
};
</script>
