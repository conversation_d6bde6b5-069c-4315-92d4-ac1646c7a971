<template>
    <div>
        <template v-if="marketingHeaders.length > 0 && userPermissionService.isAnonymous()">
            <marketing-header :marketing-headers="marketingHeaders"></marketing-header>
        </template>
        <alert-static-important v-bind:centered="true" v-if="isMaintenanceOn" class="alert-info maintenance-banner">
          <p><strong>{{$t('shop.productDetails.productHeader.maintenance.title')}}</strong></p>
          <p>{{$t('shop.productDetails.productHeader.maintenance.subtitle')}}</p>
        </alert-static-important>
        <div class="app-overview-header jumbotron">
            <div class="app-overview-header-content" v-bind:class="{searching: query}">
                <p class="text-h2 text-center white--text products-header"> {{ $t('shop.products.header') }}</p>
                <div class="search-container">
                    <div class="search">
                        <CDInput
                            data-id="input-search"
                            :clearable="true"
                            :value="query"
                            background-color="white"
                            :placeholder="$t('shop.products.searchProducts')"
                            prepend-inner-icon="$search"
                            @keydown.enter="onSearch($event.target.value)"
                            @click:clear="onClear">
                        </CDInput>
                    </div>
                </div>
                <header-image class="header-image"></header-image>
            </div>
        </div>

        <div class="app-overview-container">
            <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="mb-16 mt-8">

                <v-row v-if="!userPermissionService.isAnonymous() && !isMaintenanceOn">
                    <v-col cols="12">
                        <div class="alert alert-warning" v-if="!coreData.currentCountry.canBuy && hasResults">
                            <strong>{{$t('shop.productDetails.pricingSection.trialNowFullSoon')}}</strong>
                        </div>
                    </v-col>
                </v-row>

                <v-row>
                    <v-col cols="4" class="d-none d-md-flex overflow-hidden" :md="isDirectSalesEnabled ? 4 : 3">
                        <product-filter ref="productFilter"
                            :facets="facets"
                            :current-country="coreData.currentCountry"
                            :user-company-id="coreData.currentCompany?.companyUid"
                            :init-facets="facetSelection">
                        </product-filter>
                    </v-col>

                    <v-col cols="12" :md="isDirectSalesEnabled ? 8 : 9">
                        <v-row>
                            <v-col cols="12" lg="6">
                                <h2 class="text-h2 app-overview-title" v-if="!appsLoading">
                                    <template v-if="facetSelection.length > 0 && query">
                                        {{$t('shop.products.facets.filteredSearchResultsFor')}} "{{query}}"
                                    </template>
                                    <template v-else-if="facetSelection.length > 0">
                                        {{$t('shop.products.facets.filteredSearchResults')}}
                                    </template>
                                    <template v-else-if="!query">
                                        {{$t('shop.products.title')}}
                                    </template>
                                    <template v-else>
                                        {{$t('shop.products.searchResults')}} "{{query}}"
                                    </template>
                                    ({{totalProducts}})
                                </h2>
                            </v-col>

                            <v-col cols="12" lg="6" v-if="coreData.basestore === BaseStores.AZENA">
                                <dropdown v-model="sortSelection" class="sort-dropdown" v-if="hasResults">
                                    <template #prefix>
                                        <span class="sort-prefix">{{$t('shop.products.sorting.prefix')}}:</span>
                                    </template>
                                    <dropdown-item v-for="option in sortingOptions"
                                                v-bind:key="option.id"
                                                v-bind:item="option">
                                    </dropdown-item>
                                </dropdown>
                            </v-col>
                        </v-row>

                        <v-row v-if="$vuetify.breakpoint.smAndDown">
                          <v-col cols="12">
                            <CDButtonTextIcon
                                icon="$filter"
                                @click="$refs.productFilter.mobileFilters = true"
                                data-id="button-toggle-filters">
                              {{$t('shop.products.facets.filters')}}
                            </CDButtonTextIcon>
                          </v-col>
                        </v-row>

                        <v-row v-if="facetSelection.length > 0">
                            <v-col cols="12">
                                <div class="filter-selection">
                                    <label class="text-h4 d-inline-flex mr-2">
                                        {{$t('shop.products.facets.filteredBy')}}
                                    </label>
                                    <div ref="tags" class="status-tag-wrapper d-inline-flex">
                                        <status-tag
                                            v-for="facet in facetSelection"
                                            :key="facet.facetIndex"
                                            @click="onFilterRemove(facet)"
                                            :data-facet-id="facet.facetIndex"
                                            :text="getFacetItemLabel(facet)"
                                            class="mr-2"
                                            close>
                                        </status-tag>
                                    </div>
                                </div>
                            </v-col>
                        </v-row>

                        <v-row v-if="isDirectSalesEnabled">
                          <v-col v-for="product in formattedProducts"
                                 :key="product.code"
                                 cols="12">
                            <v-flex fill-height d-flex>
                              <aa-app-card
                                  :app-card-data="product"
                                  :hide-prices="storeRootStore.isManagedAccount"></aa-app-card>
                            </v-flex>
                          </v-col>
                        </v-row>
                        <v-row v-else>
                          <v-col cols="12" md="6" xl="4" v-for="product in formattedProducts" :key="product.code">
                            <v-flex fill-height d-flex xs12>
                              <app-card :id="product.code" :appCardData="product"></app-card>
                            </v-flex>
                          </v-col>
                        </v-row>
                        <v-row v-if="isDirectSalesEnabled && showSpecialOfferConditions" class="mt-16">
                          <v-col cols="12">
                            <special-offer-conditions></special-offer-conditions>
                          </v-col>
                        </v-row>

                        <div class="loadmore-button mt-16"
                             v-if="displayLoadMoreButton">
                          <CDButton
                              @click="loadProducts"
                              data-id="button-load-more"
                              color="primary">
                            {{ $t('shop.products.loadMore') }}
                          </CDButton>
                        </div>

                        <v-row v-if="!appsLoading && !hasResults">
                          <v-col cols="12">
                            <i18n path="shop.products.noSearchResults" tag="div">
                              <template v-slot:url>
                                <a :href="`${coreData.supportUrl}`" target="_blank">{{
                                    $t('shop.products.contactUs')
                                  }}</a>
                              </template>
                            </i18n>
                          </v-col>
                        </v-row>

                    </v-col>
                </v-row>
                <div class="d-none" id="total-apps">{{totalProducts}}</div>
            </v-container>
      </div>

        <div ref="greedyTrigger" class="page-end">
            <div ref="lazyTrigger"></div>
        </div>

    </div>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import {
  CategoryPageData,
  FacetData,
  FacetItem,
  NavigationItemData,
  NavigationItemGroup,
  ShopCoreData,
  SimpleProductData,
  StoreAvailabilityMode
} from 'common/generated-types/types';
import HeaderImage from 'common/images/boxed/application-store-header.svg';
import {MarketingHeader, ProductFilter} from 'shop/products/components';
import {messageService, navigationService, productDataService, userPermissionService} from 'common/services';
import {productResource, StoreDataProvider} from 'shop/resources';
import {pageSpinner} from 'common/components/spinner';
import {assignInWith, camelCase, find, isEqual, snakeCase, sortBy, throttle, toUpper} from 'lodash';
import {AppCardInterface, SearchQueryParams} from 'common/types';
import {AlertStaticImportant, StatusTag, AppCard} from 'common/components';
import Dropdown, {Item} from 'common/components/dropdown/Dropdown.vue';
import DropdownItem from 'common/components/dropdown/DropdownItem.vue';
import {BaseStores, ShopRoute} from 'common/constants';
import {FacetGroup, SortingOption} from 'shop/products/types';
import {Dictionary} from 'vue-router/types/router';
import {AaAppCard} from 'aa/shop/products/components';
import {useProductSelectionStore} from 'shop/store/productSelection';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import SpecialOfferConditions from 'aa/shop/core/components/specialoffer/SpecialOfferConditions.vue';

const THROTTLE_WAIT = 1000;

    const SUPPORTED_FACETS = ['countries', 'licenseTypes', 'useCases', 'industries', 'hardwareRequirements', 'packages', 'licenses',
      'vehicleType', 'firstLevelCategories'];
    @Component({
        components: {
            AppCard,
            SpecialOfferConditions,
            AaAppCard,
            Dropdown,
            DropdownItem,
            ProductFilter,
            HeaderImage,
            StatusTag,
            AlertStaticImportant,
            MarketingHeader
        }
    })
    export default class Products extends Vue {
        @Prop() pageData!: CategoryPageData;

        products: SimpleProductData[] = [];
        facets: FacetData[] = [];
        pageSpinner = pageSpinner;
        totalProducts = 0;
        query = '';
        pageNumber = 0;
        navigationService = navigationService;
        userPermissionService = userPermissionService;
        coreData: ShopCoreData = StoreDataProvider.coreData;
        appsLoading = false;
        lastExecutedQuery!: SearchQueryParams;
        BaseStores = BaseStores;
        productSelectionStore = useProductSelectionStore();
        storeRootStore = useStoreRootStore();

        isMaintenanceOn = Boolean(this.coreData.moduleConfig.STORE_MAINTENANCE_BANNER);
        isCompanyProfileFeatureOn = Boolean(this.coreData.moduleConfig.ENABLE_COMPANY_PROFILE);

        facetSelection: FacetItem[] = [];

        translatableFacetGroups = [FacetGroup.LICENSE_RUNTIMES.toString()];

        isPrivateApp(product: SimpleProductData): boolean {
            return product.storeAvailabilityMode === StoreAvailabilityMode.RESTRICTED_BUYER;
        }
        readOnly(product: SimpleProductData): boolean {
            return productDataService.readOnly(product);
        }

        availableInUserCountry(product: SimpleProductData): boolean {
            return productDataService.availableInCountry(product);
        }

        get displayLoadMoreButton(): boolean {
            return !this.appsLoading && this.totalProducts > this.products.length;
        }

        getFacetItemLabel(facetItem: FacetItem): string {
          const facetGroup = facetItem.facetIndex;
          const facetItemName = facetItem.name;
          const i18nKey = `shop.products.facets.group.${facetGroup.toLowerCase()}.values.${facetItemName.toLowerCase()}`;
          if (this.$te(i18nKey) || this.$te(i18nKey, 'en')) {
            return this.$t(i18nKey).toString();
          } else {
            return facetItemName;
          }
        }

        get formattedProducts(): AppCardInterface[] {
            const formattedUrl = (product: SimpleProductData): string => {
              if (this.isDirectSalesEnabled) {
                const url = ShopRoute.PRODUCT_SELECTION(product.code);
                return userPermissionService.isAnonymous() ? ShopRoute.LOGIN_REDIRECT(url) : url;
              } else {
                return product.url;
              }
            };

            const priceInfo = (product: SimpleProductData) => {
              return this.isDirectSalesEnabled && !this.userPermissionService.isAnonymous()
                  ? this.productSelectionStore.prices?.get(product.code) : undefined;
            };

            const formattedChips = (product: SimpleProductData) => {
                const chips = [];
                if (this.isDirectSalesEnabled) {
                    const runtimeChips = product.licenseRuntimes?.map(
                        runtime => ({ text: this.$t(`shop.runtime.${runtime}`) as string })
                    );
                    chips.push(...runtimeChips);
                }
                if (!userPermissionService.isAnonymous()) {
                    const isPrivate = this.isPrivateApp(product);
                    const isReadOnly = this.readOnly(product);
                    const availableInCountry = this.availableInUserCountry(product);
                    if(isPrivate) {
                        chips.push({
                            text: this.$t('shop.gridItem.privateAppLabel') as string,
                            closable: false as boolean
                        });
                    }
                    if(isReadOnly) {
                        chips.push({
                            text: this.$t('shop.gridItem.readOnlyLabel') as string,
                            closable: false as boolean
                        });
                    } else if(!availableInCountry) {
                        chips.push({
                            text: this.$t('shop.gridItem.notAvailableLabelShort') as string,
                            closable: false as boolean
                        });
                    }
                }
                return chips;
            };

            return this.products.map(product => ({
                title: product.name,
                img: product.logoUrl,
                body: product.shortDescription,
                sub: product.company.name,
                subPrefix: this.$i18n.t('by') as string,
                subAsLink: this.isCompanyProfileFeatureOn && product.company.hasPublishedProfile,
                subLinkHref: product.company.profileUrl,
                url: formattedUrl(product),
                code: product.code,
                chips: formattedChips(product),
                priceInfo: priceInfo(product)
            }));
        }

        sortSelection = {
            id: SortingOption.PUBLISH_DATE,
            label: 'shop.products.sorting.created'
        };

        sortSelectionAA = {
            id: SortingOption.ACQUISITION_COUNT,
            label: 'shop.products.sorting.acquisitionCount'
        };

        sortingOptions = [
            this.sortSelection,
            {
                id: SortingOption.ACQUISITION_COUNT,
                label: 'shop.products.sorting.acquisitionCount'
            }, {
                id: SortingOption.RELEVANCE,
                label: 'shop.products.sorting.relevance'
            }
        ];

        $refs!: {
            greedyTrigger: HTMLElement;
            lazyTrigger: HTMLElement;
            productFilter: ProductFilter;
            tags: HTMLElement;
        };

        created(): void {
            this.setPageTitle();
            this.parseQueryUrl();
        }

        private setPageTitle(): void {
            document.title = this.$t('navigation.storePageTitle') as string;
        }

        async mounted(): Promise<void> {
            await this.getProducts(false);

            if (this.isDirectSalesEnabled && !userPermissionService.isAnonymous()) {
                await this.productSelectionStore.fetchPrices();
            }

            this.$on('on-facet-selection', this.handleFacetSelection);
            // translating sort labels
            this.sortSelection = {
                ...this.sortSelection,
                label: this.$t(this.sortSelection.label).toString()
            };
            this.sortingOptions.forEach(option => {
                option.label = this.$t(option.label).toString();
            });
        }

        @Watch('sortSelection')
        onSortSelectionChanged(val: Item, oldVal: Item): void {
            if (oldVal.id !== val.id && !this.appsLoading) {
                this.throttledMakeSearch(this.query);
            }
        }

        getProducts(updateUrl = true): Promise<void> {
            const queryParams = this.buildQuery();
            if (this.appsLoading || (isEqual(queryParams, this.lastExecutedQuery) && this.totalProducts <= this.products.length)) {
                return Promise.resolve();
            }

            this.appsLoading = true;
            return productResource.getProducts(queryParams).then(response => {
                const data = response.data;
                this.lastExecutedQuery = queryParams;
                this.populateItems(data);
                if(updateUrl) {
                    this.$router.push({path: this.$route.path, query: this.serializeQuery(queryParams)})
                        .catch((reason) => {
                            console.log('navigation failed because of: %o', reason);
                        });
                }
            }).catch(error => {
                messageService.errorResponse(error.response?.data, this.$i18n);
            }).finally(() => {
                this.pageSpinner.isActive && this.pageSpinner.stop();
                this.appsLoading = false;
            });
        }

        private serializeQuery(params: SearchQueryParams): Dictionary<string> {
            const res:Dictionary<string> = {};
            Object.entries(params).forEach(([key, value]) => {
                if(key !== 'page') {
                    res[key] = value.toString();
                }
            });
            return res;
        };

        private buildQuery(): SearchQueryParams {
            const params: SearchQueryParams = {
                page: this.pageNumber
            };

            if (this.query) {
                params.query = this.query;
            }

            if (this.coreData.basestore === BaseStores.AZENA) {
                params.sortBy = camelCase(SortingOption[this.sortSelection.id]);
            } else {
                params.sortBy = camelCase(SortingOption[this.sortSelectionAA.id]);
            }

            if (this.facetSelection.length > 0) {
                this.facetSelection.forEach(facet => {
                    assignInWith(params, {[facet.facetIndex]: facet.facetValue});
                });
            }

            return params;
        }

        private parseQueryUrl(): void {
            const queryParams = {
                page: 0,
                ...this.$route.query,
            } as SearchQueryParams;

            const sortingId = toUpper(snakeCase(queryParams.sortBy||'')) as keyof typeof SortingOption;
            this.sortSelection = find(this.sortingOptions, {id: SortingOption[sortingId]}) || this.sortSelection;

            this.query = queryParams.query || '';

            SUPPORTED_FACETS.forEach(group => {
                const queryParam = queryParams[group as 'countries' | 'licenseTypes' | 'useCases' | 'industries' | 'hardwareRequirements' | 'packages' | 'licenses' | 'vehicleType'];
                if(queryParam) {
                    this.facetSelection.push(
                        ...queryParam.split(',').map(s => ({
                            name: s,
                            facetIndex: group,
                            facetValue: s,
                            count: 0, // not known yet at this moment
                        } as FacetItem))
                    );
                }
            });
        }

        onSearch(query: string): void {
            const relSort = find(this.sortingOptions, {id: SortingOption.RELEVANCE});
            if (relSort) {
                this.sortSelection = relSort;
            }
            this.throttledMakeSearch(query);
        }

        onClear(): void {
            this.throttledMakeSearch();
        }

        get hasResults(): boolean {
            return this.products.length > 0;
        }

        get marketingHeaders(): NavigationItemData[] {
            return sortBy(this.navigationService.byGroup(NavigationItemGroup.MARKETING_HEADER), 'index');
        }

        get isDirectSalesEnabled(): boolean {
          return Boolean(this.coreData.moduleConfig.DIRECT_SALES) && this.coreData.basestore === BaseStores.AA;
        }

        handleFacetSelection(facetSelection: FacetItem[]): void {
            this.facetSelection = facetSelection;
            this.invalidateSearch();
            this.getProducts();
        }

        onFilterRemove(facet: FacetItem): void {
            this.$refs.productFilter.onFacetSelection(facet);
        }

        throttledMakeSearch = throttle((query?: string) => this.makeSearch(query), THROTTLE_WAIT);

        private invalidateSearch(): void {
            this.pageNumber = 0;
            this.products = [];
            this.facets = [];
            this.pageSpinner.start(0);
        }

        loadProducts(): void {
          if (this.totalProducts > this.products.length) {
            if (!this.pageSpinner.isActive && this.products.length > 0) {
              this.pageNumber++;
              this.getProducts();
            }
          }
        }

        private makeSearch(query?: string): void {
            this.query = query || '';
            this.invalidateSearch();
            this.getProducts();
        }

        private populateItems(pageData: CategoryPageData): void {
            if (pageData.totalNumberOfResults !== this.totalProducts) {
                this.totalProducts = pageData.totalNumberOfResults;
            }

            if (this.pageNumber === 0) {
                Vue.set(this, 'facets', pageData.facets);
            }
            this.products.push(...pageData.products);
        }

        get showSpecialOfferConditions(): boolean {
            return this.formattedProducts?.some((product) => product.priceInfo?.specialOffer);
        }
    }
</script>
<style scoped lang="scss">
    @import "common/design";
    @import "common/alerts";
    @import "shop/core/constants";

    .apps-container {
        section {
            @include grid-columns();
        }
    }

    .alert {
        &.maintenance-banner {
            background-color: var(--v-info-base);
            margin-bottom: 0;
        }
    }

    .loadmore-button {
      margin: auto;
      width: 50%;
      display: flex;
      justify-content: center;
    }

    .app-overview-header {
        background-blend-mode: multiply;
        position: relative;
        justify-content: center;
        align-items: center;
        display: flex;

        padding: $spacing-m $spacing-xs $spacing-m $spacing-xs;
        @include respond-to('M') {
            padding: $spacing-l;
        }

        &-content {

            position: relative;
            max-width: 1200px;
            width: 100%;

            @include respond-to('L') {
                margin: 50px 0 38px 0;
            }

            &.searching {
                h1 {
                    display: none;
                }
            }

            @include respond-to('M') {
                &.searching {
                    h1 {
                        display: block;
                    }
                }
            }

            .products-header {
                line-height: 1.2;
                font-weight: 600;
                margin-bottom: 35px;

                @include respond-to('L') {
                    margin: 0 150px 35px 150px;
                }
            }

            .search-container {
                display: flex;
                justify-content: center;

                .search {
                    max-width: 384px;
                    width: 100%;
                }
            }

            .header-image {
                display: none;
                @include respond-to('L') {
                    display: block;
                    position: absolute;
                    transform: translateY(-50%);
                    top: 50%;
                    right: 0;
                    background-blend-mode: difference;
                    mix-blend-mode: luminosity;
                }
            }
        }
    }

    .v-application {
        a {
          &:not(.v-btn):not(.v-card) {
            color: var(--v-primary-base) !important;
          }
          &:hover {
            color: inherit;
          }
        }
        :deep {
            .v-card__subtitle .install-count {
                transform: translateY(-3px);
                display: inline-flex;
            }
            // hides dropdown arrow
            // temporary. to be removed once autocomplete functionality added
            .v-text-field .v-input__append-inner {
                display: none !important;
            }
        }
    }
</style>
