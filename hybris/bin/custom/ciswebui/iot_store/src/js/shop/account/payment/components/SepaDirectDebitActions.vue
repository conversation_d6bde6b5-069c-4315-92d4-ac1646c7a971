
<template>
    <div class="actions d-flex justify-end">
        <div class="actions-menu mt-n2 actions-for-non-default" v-if="!sepaDirectDebit.defaultPaymentInfo"
             data-id="container-actions-menu">
            <CDFlyoutMenu rounded="b-lg tl-lg tr-0"
                          menu-icon="$more"
                          menu-color="black">
                <CDFlyoutMenuItem :title="setPreferenceText"
                                  class="set-default"
                                  data-id="set-default-payment"
																	:icon="'$payment'"
                                  v-on:cdClick="setAsDefault(sepaDirectDebit.id)">
                </CDFlyoutMenuItem>
                <CDFlyoutMenuItem :title="removePaymentOptionText"
                                  data-id="delete-non-default-payment"
                                  class="delete ml-auto"
																	:icon="'$trash'"
                                  v-on:cdClick="confirmDeletePaymentInfo()">
                </CDFlyoutMenuItem>
                <CDFlyoutMenuItem v-if="isSepaMandateEnabled"
																	:title="viewPaymentOptionText"
                                  data-id="view-payment-option"
                                  class="ml-auto"
																	:icon="'$info'"
                                  v-on:cdClick="viewPaymentInfo()">
                </CDFlyoutMenuItem>
            </CDFlyoutMenu>
        </div>
        <div class="actions-menu mt-n2 actions-for-default" v-else data-id="container-actions-menu">
            <CDFlyoutMenu rounded="b-lg tl-lg tr-0"
                          menu-icon="$more"
                          menu-color="black">
                <CDFlyoutMenuItem :title="removePreferenceText"
                                  class="set-default"
                                  data-id="unset-default-payment"
																	:icon="'$payment'"
                                  v-on:cdClick="removeAsDefault(sepaDirectDebit.id)">
                </CDFlyoutMenuItem>
                <CDFlyoutMenuItem :title="removePaymentOptionText"
                                  data-id="delete-default-payment"
                                  class="delete ml-auto"
																	:icon="'$trash'"
                                  v-on:cdClick="confirmDeletePaymentInfo">
                </CDFlyoutMenuItem>
                <CDFlyoutMenuItem  v-if="isSepaMandateEnabled"
																	 :title="viewPaymentOptionText"
                                   data-id="view-payment-option"
                                   class="ml-auto"
																	 :icon="'$info'"
                                   v-on:cdClick="viewPaymentInfo()">
                </CDFlyoutMenuItem>
            </CDFlyoutMenu>
        </div>
        <remove-payment-option-dialog :isOpen="showConfirmationPopup"
                                      data-id="remove-payment-option-dialog"
                                      v-on:cancelAction="showConfirmationPopup = false"
                                      v-on:confirm="doDeletePaymentInfo">
        </remove-payment-option-dialog>
    </div>

</template>
<script lang="ts">
import {Vue, Component, Prop} from 'vue-property-decorator';
import {ErrorMessageData, SepaMandatePaymentInfoData} from 'common/generated-types/types';
import RemovePaymentOptionDialog from 'shop/account/payment/components/RemovePaymentOptionDialog.vue';
import {pageSpinner} from 'common/components/spinner';
import {messageService} from 'common/services';
import {paymentResource} from 'shop/resources';

@Component({
	components: { RemovePaymentOptionDialog}
})
export default class SepaDirectDebitActions extends Vue {
	@Prop() sepaDirectDebit!: SepaMandatePaymentInfoData;
  @Prop({ default: false }) isSepaMandateEnabled!: boolean;

	showConfirmationPopup = false;
	get removePreferenceText(): string {
		return this.$t('shop.payment.removePreference') as string;
	}

	get setPreferenceText(): string {
		return this.$t('shop.payment.setPreference') as string;
	}

	get removePaymentOptionText(): string {
		return this.$t('shop.payment.removePaymentOption') as string;
	}

	get viewPaymentOptionText(): string {
		return this.$t('shop.payment.viewPaymentOption') as string;
	}

	confirmDeletePaymentInfo(): void {
		this.showConfirmationPopup = true;
	}

	viewPaymentInfo(): void {
		if (this.isSepaMandateEnabled) {
			this.$emit('payment-info-viewed', this.sepaDirectDebit.id);
		}
	}

	doDeletePaymentInfo(): void {
		pageSpinner.start();
		messageService.clearAll();
		paymentResource.deletePaymentMethod(this.sepaDirectDebit.id)
			.then(() => {
				this.$emit('payment-info-deleted', this.sepaDirectDebit.id);
				messageService.success(this.$t('shop.payment.removed') as string);
			})
			.catch((error) => {
				const response = error.response;
				if (response && response.data) {
					const data = response.data as ErrorMessageData;
					messageService.error(data.message);
				} else {
					messageService.error(this.$t('backendError') as string);
				}
			})
			.finally(() => {
				pageSpinner.stop();
				this.showConfirmationPopup = false;
			});
	}

	removeAsDefault(paymentInfoId: string): void {
		pageSpinner.start();
		messageService.clearAll();
		paymentResource.removeDefaultPaymentMethod(paymentInfoId)
			.then(() => {
				this.$emit('default-payment-info-unset', paymentInfoId);
				messageService.success(this.$t('shop.payment.removePreferenceSuccess') as string);
			})
			.catch((error) => {
				const response = error.response;
				if (response && response.data) {
					const data = response.data as ErrorMessageData;
					messageService.error(data.message);
				} else {
					messageService.error(this.$t('backendError') as string);
				}
			})
			.finally(() => {
				pageSpinner.stop();
			});
	}

	setAsDefault(paymentInfoId: string): void {
		pageSpinner.start();
		messageService.clearAll();
		paymentResource.setDefaultPaymentMethod(paymentInfoId)
			.then(() => {
				this.$emit('default-payment-info-set', paymentInfoId);
				messageService.success(this.$t('shop.payment.setPreferenceSuccess') as string);
			})
			.catch((error) => {
				const response = error.response;
				if (response && response.data) {
					const data = response.data as ErrorMessageData;
					messageService.error(data.message);
				} else {
					messageService.error(this.$t('backendError') as string);
				}
			})
			.finally(() => {
				pageSpinner.stop();
			});
	}
}
</script>
<style scoped lang="scss">

</style>
