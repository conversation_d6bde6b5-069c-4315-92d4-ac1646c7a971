<template>
  <div class="product-header">
    <v-container :fluid="true" class="pa-0">
      <v-container :fluid="$vuetify.breakpoint.lgAndDown">
        <div v-if="isThirdPartyApp" class="breadcrumbs my-2">
          <v-row>
            <v-col cols="12">
              <span><a v-bind:href="baseUrl">{{$t('shop.productDetails.productHeader.applicationStore')}}</a></span>
              <span class="breadcrumb-arrow">></span>
              <span>{{productName}}</span>
            </v-col>
          </v-row>
        </div>
        <v-row class="mb-4">
          <v-col cols="12" :xl="video ? 6 : 8">

            <div class="d-md-flex align-md-center mb-4">
              <div class="logo mr-md-6 mx-sm-auto mx-md-0 mb-sm-4" ref="logoheader">
                <div v-bind:style="{backgroundImage: `url(${logoUrl})`}"></div>
              </div>
              <div class="text-center text-lg-left">
                <span class="text-h1 product-name" data-id="text-productname">{{productName}}</span>
                <CDChip class="mb-3" v-if="isThirdPartyPrivateApp">{{ $t('shop.productDetails.productHeader.privateAppLabel') }}</CDChip>
              </div>
            </div>

            <div class="mb-4">
              <div class="companyName d-flex d-md-inline-flex">
                <span class="d-flex grey--text text--darken-2 mr-1">{{$t('shop.productDetails.productHeader.byCompany')}}</span>
                <a v-if="displayLinkToCompanyProfile"
                   :href="company.profileUrl"
                   data-id="link-company-name">{{companyName}}</a>
                <span v-else data-id="text-company-name">{{companyName}}</span>
              </div>

              <div class="product-reviews-overview d-sm-flex d-md-inline-flex" v-if="numberOfReviews > 0">
                <span class="mx-4 d-none d-md-flex grey--text">|</span>
                <star-rating class="product-star-rating d-flex align-center mr-3" v-bind:rating="averageRating"></star-rating>
                {{numberOfReviews}} {{$tc('shop.productDetails.productHeader.reviewsOverview.review', numberOfReviews)}}
              </div>

              <div class="product-number-installs d-sm-flex d-md-inline-flex" v-if="numberOfInstalls >= 500">
                <span class="mx-4 d-none d-md-flex grey--text">|</span>
                {{ $t('shop.productDetails.productHeader.reviewsOverview.purchases', {installs: $n(roundedInstalls, 'decimal', localeForFormats)}) }}
              </div>
            </div>

            <p class="short-description cd-text-large">{{shortDescription}}</p>

            <app-cta
                v-if="displayCta"
                :licenses="licenses"
                :read-only="readOnly">
            </app-cta>

            <tool-app-cta
                v-if="displayCta"
                :licenses="licenses">
            </tool-app-cta>

            <app-integration-labels v-if="hasAppIntegrations"
                                    v-bind:app-integrations="appIntegrations">
            </app-integration-labels>

          </v-col>
          <v-col cols="12" v-if="video" md="8" xl="6" class="pa-sm-0 my-sm-0">
            <youtube-player v-if="video" class="youtube-player pl-xl-4" v-bind:video="video">
            </youtube-player>
          </v-col>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {isEmpty} from 'lodash';
    import StarRating from 'shop/core/components/starrating/StarRating.vue';
    import AppIntegrationLabels from 'shop/productdetails/components/AppIntegrationLabels.vue';
    import ToolAppCta from 'shop/productdetails/components/ToolAppCta.vue';
    import AppCta from 'shop/productdetails/components/AppCta.vue';
    import util from 'common/util';
    import {i18nService, numberRounderService} from 'common/services';
    import {
        PublicAppIntegrationData,
        ProductLicenseData,
        StoreAvailabilityMode,
        IotCompanyData,
        ShopCoreData,
        AppVideoData
    } from 'common/generated-types/types';
    import {LicenseType} from 'common/types';
    import {ShopCoreDataProvider} from 'common/provider/coreDataProvider';
    import {YoutubePlayer} from 'common/components/videoplayers';

@Component({
        components: {
            AppCta,
            ToolAppCta,
            StarRating,
            AppIntegrationLabels,
            YoutubePlayer
        }
    })
    export default class ProductHeader extends Vue {
        @Prop() productName!: string;
        @Prop() company!: IotCompanyData;
        @Prop() logoUrl!: string;
        @Prop() averageRating!: number;
        @Prop() numberOfReviews!: number;
        @Prop() numberOfInstalls!: number;
        @Prop() licenses!: ProductLicenseData[];
        @Prop() readOnly!: boolean;
        @Prop() storeAvailabilityMode!: StoreAvailabilityMode;
        @Prop() code!: string;
        @Prop() appIntegrations!: PublicAppIntegrationData[];
        @Prop() availableInUserCountry!: boolean;
        @Prop() isThirdPartyApp!: boolean;
        @Prop() shortDescription!: string;
        @Prop() video!: AppVideoData;

        coreData: ShopCoreData = ShopCoreDataProvider.data;
        baseUrl: string = this.coreData.baseUrl;
        isCompanyProfileFeatureOn = Boolean(this.coreData.moduleConfig.ENABLE_COMPANY_PROFILE);

        numberRounderService = numberRounderService;
        LicenseType = LicenseType;

        $refs!: {
            logoheader: HTMLElement
            headermsg: HTMLElement;
        };

  localeForFormats = '';

        mounted(): void {
            util.loadLogoImage([{
                logoUrl: this.logoUrl,
                logoElement: this.$refs.logoheader
            }]);
          this.localeForFormats = i18nService.getLocaleForFormats();
        }

        get hasAppIntegrations(): boolean {
          return !isEmpty(this.appIntegrations);
        }

        get i18nService() {
          return i18nService;
        }

        get roundedInstalls(): number {
            return this.numberRounderService.purchases(this.numberOfInstalls);
        }

        get isPrivateApp(): boolean {
          return this.storeAvailabilityMode === StoreAvailabilityMode.RESTRICTED_BUYER;
        }

        get isThirdPartyPrivateApp(): boolean {
          return this.isThirdPartyApp && this.isPrivateApp;
        }

        get displayCta(): boolean {
          return this.availableInUserCountry;
        }

        get companyName(): string {
          return this.company?.friendlyName || this.company?.name;
        }

        get displayLinkToCompanyProfile(): boolean {
          return this.isCompanyProfileFeatureOn && this.company?.hasPublishedProfile;
        }
    }
</script>

<style scoped lang="scss">
@import "shop/core/logo";
@import "shop/core/constants";

.product-header {

  .logo {
    width: 78px;
    height: $header-height;
    overflow: hidden;
    border-radius: 10px;
    div {
      @extend %-logo-inner;
    }
  }


  .reviews-and-installs {
    .product-reviews-overview {
      .star-rating {
        margin-top: -1px;
        margin-right: 8px;
      }
    }
  }
}
</style>
