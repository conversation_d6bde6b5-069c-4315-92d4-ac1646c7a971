<template>
  <div class="company-name grey--text text--darken-2">
    <span data-id="span-by-company">{{ $t('shop.productDetails.productHeader.byCompany') }}</span>
    <span data-id="span-company-name">{{ company && (company.friendlyName || company.name) }}</span>
  </div>
</template>
<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {IotCompanyData} from 'common/generated-types/types';

@Component({})
export default class ByCompany extends Vue {
  @Prop() company!: IotCompanyData;
}
</script>
