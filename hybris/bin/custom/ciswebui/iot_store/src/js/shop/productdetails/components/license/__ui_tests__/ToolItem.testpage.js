import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import {ToolItem} from 'shop/productdetails/components/license';
import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import {createApp} from 'vue-demi';

Vue.use(VueI18n, { bridge: true });

const testLicenseCode = 'A_00000318_tool';

const mock = new MockAdapter(axios);
mock.onPatch( `/shop/api/company/current/license/${testLicenseCode}/activation`).reply(200, {
    response: {}
});

window.frontendData = {
    coreData: {
        currentCompany: {
            companyApproved: true,
        },
        allowedToBuy: true,
        navigationItems: [
            {
                'id': 'globalSupport',
                'itemCode': 'globalSupport',
            }
        ]
    }
};

const options = {
    vuetify,
    i18n,
    el: '#vue-app',
    components: {
        TestPageRoot,
        ToolItem
    },
    data: {
        license: {
            code: testLicenseCode,
            name: 'TOOL',
            licenseType: {
                code: 'TOOL',
                type: 'LicenseType'
            },
            purchasability: 'PURCHASABLE'
        },
        company: {
            name: 'S&ST',
            friendlyName: 'S&ST'
        },
        productName: 'Queue Management'
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <tool-item class="cell-license-item"
                     :license="license"
                     :company="company"
                     :product-name="productName"
                     ref="tools">
          </tool-item>
        </div>
      </test-page-root>
    `
};

const app = createApp(options);
app.use(i18n);
app.mount('#app');
