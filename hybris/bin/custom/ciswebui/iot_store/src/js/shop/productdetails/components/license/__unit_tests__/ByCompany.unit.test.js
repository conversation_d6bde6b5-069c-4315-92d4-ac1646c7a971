import {mount} from '@vue/test-utils';
import 'common/test-directive';
import ByCompany from "../ByCompany";

jest.mock('common/util');

const companyWithFriendlyName = {
    name: 'Security & Safety Things',
    friendlyName: 'S&ST'
};

const companyWithOutFriendlyName = {
    name: 'Security & Safety Things',
    friendlyName: null
};


const mountByCompany = (company) => mount(ByCompany, {
    mocks: {
        $t: () => 'By'
    },
    propsData: {
        company: company
    },
});

describe('By Company', () => {

    beforeEach(() => {
        window.frontendData = {
            coreData: {
                currentCompany: {
                    companyApproved: true,
                },
                allowedToBuy: true
            }
        };
    });

    function verifyByCompany(wrapper, companyName) {
        expect(wrapper.find('[data-id="span-by-company"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="span-by-company"]').text()).toBe('By');
        expect(wrapper.find('[data-id="span-company-name"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="span-company-name"]').text()).toBe(companyName);

    }

    it('Company with friendly name -> company friendly name is displayed', () => {
        const wrapper = mountByCompany(companyWithFriendlyName);
        verifyByCompany(wrapper, companyWithFriendlyName.friendlyName);
    });

    it('Company without friendly name -> company name is displayed', () => {
        const wrapper = mountByCompany(companyWithOutFriendlyName);
        verifyByCompany(wrapper, companyWithOutFriendlyName.name);
    });

    it('Empty company -> company name empty ', () => {
        const wrapper = mountByCompany({});
        verifyByCompany(wrapper, '');
    });

    it('No company -> company name empty', () => {
        const wrapper = mountByCompany(null);
        verifyByCompany(wrapper, '');
    });

});
