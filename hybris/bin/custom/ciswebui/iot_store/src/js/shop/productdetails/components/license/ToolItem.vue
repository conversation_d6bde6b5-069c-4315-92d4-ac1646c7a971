<template>
  <div data-id="container-tool-item" v-if="isToolProduct">
    <div data-id="container-cell-tool-info">
      <div class="tool-info mb-4">
        <h2 class="name text-h3 mb-2">{{license.name}}</h2>
        <by-company :company="company"></by-company>
      </div>
    </div>
    <template v-if="isAllowedToBuyLicense">
      <div class="cell-tool-action" ref="input">
        <CDToggle
            class="mt-0"
            v-model="enabled"
            data-id="toggle-get-free-tool"
            @change="toggleEnabledTool($event)"
            :label="$t('shop.productDetails.pricingSection.enableTool')">
        </CDToggle>
      </div>
      <license-details class="cell-license-details"
                       :license-code="license.licenseType.code">
      </license-details>
      <tool-enabled-popup :show="showToolEnabledModal"
                          :product-name="productName"
                          v-on:modal-closed="showToolEnabledModal = false">
      </tool-enabled-popup>
    </template>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {LicenseType} from 'common/types';
import {licenseTypeService, messageService, userPermissionService} from 'common/services';
import licenseActivationResource from 'shop/resources/licenseActivationResource';
import {IotCompanyData, LicenseActivationStatus} from 'common/generated-types/types';
import {PurchasableLicenseData} from 'common/types.ts';
import {pageSpinner} from 'common/components/spinner';
import {ByCompany, LicenseDetails} from 'shop/productdetails/components/license';
import ToolEnabledPopup from 'shop/productdetails/components/ToolEnabledPopup.vue';

@Component({
  components: {
    ByCompany,
    LicenseDetails,
    ToolEnabledPopup
  }
})
export default class ToolItem extends Vue {
  @Prop() license!: PurchasableLicenseData;
  @Prop() company!: IotCompanyData;
  @Prop() productName!: string;

  LicenseType = LicenseType;
  enabled = false;
  showToolEnabledModal = false;
  $refs!: {
    input: HTMLDivElement;
  };

  created(): void {
    if (this.isAllowedToBuyLicense) {
      this.retrieveLicenseActivationStatus();
    }
  }

  get isAllowedToBuyLicense(): boolean {
    return userPermissionService.canPurchaseLicense(this.license);
  }

  get licenseType(): LicenseType {
    return this.license.licenseType.code as LicenseType;
  }

  get isToolProduct(): boolean {
    return !licenseTypeService.isThirdPartyLicense(this.licenseType);
  }

  retrieveLicenseActivationStatus(): void {
    licenseActivationResource.getLicenseActivationStatusForCurrent(this.license.code)
        .then(response => {
          this.enabled = response.data.activationStatus === LicenseActivationStatus.ACTIVE;
        }).catch(error => {
          messageService.errorResponse(error.response?.data, this.$i18n);
    });
  }

  toggleEnabledTool(status: boolean): void {
    pageSpinner.start( 0);
    const licenseActivationStatus = status ? LicenseActivationStatus.ACTIVE : LicenseActivationStatus.INACTIVE;
    licenseActivationResource.setLicenseActivationStatusForCurrent(this.license.code, licenseActivationStatus)
        .then(() => {
          this.showToolEnabledModal = status;
          this.enabled = status;
        }).catch(error => {
          messageService.errorResponse(error.response?.data, this.$i18n);
        }).finally(() => {
          pageSpinner.stop();
        });
  }

  focus(): void {
    if (this.$refs.input) {
      this.$refs.input.focus();
      const inputElement = this.$refs.input.querySelector('input') as HTMLInputElement;
      if (inputElement) {
        inputElement.select();
      }
    }
  }
}
</script>

