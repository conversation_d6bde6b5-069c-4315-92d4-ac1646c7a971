import {mount, shallowMount} from '@vue/test-utils';
import ProductHeader from '../ProductHeader';
import {StoreAvailabilityMode} from 'common/generated-types/types';
import {cloneDeep} from 'lodash';
import vuetify from 'common/plugins/brands/azena/vuetify';

jest.mock('common/util');

const defaultCoreData = {
    moduleConfig : {
    }
};

describe('ProductHeader', () => {
    beforeAll(() => {
        window.frontendData = {
            coreData: defaultCoreData
        };
    });

    const mountProductHeader  = (numberOfInstalls, numberOfReviews = 0) => mount(ProductHeader, {
        mocks: {
            $t: (word) => {
                return word;
            },
            $n: () => {},
            $tc: () => {}
        },
        vuetify,
        propsData: {
            numberOfReviews,
            numberOfInstalls
        }
    });

    it('should show number of installs if value is gte  500', () => {
        const wrapper = mountProductHeader(500);
        const installs = wrapper.find('.product-number-installs');

        expect(installs.exists()).toBe(true);
    });

    it('should show number of installs if value is lt  500', () => {
        const wrapper = mountProductHeader(499);
        const installs = wrapper.find('.product-number-installs.show-left');
        const reviews = wrapper.find('.product-reviews-overview');

        expect(installs.exists()).toBe(false);
        expect(reviews.exists()).toBe(false);
    });
});

describe('ProductHeader Cta', () => {
    beforeAll(() => {
        window.frontendData = {
            coreData : defaultCoreData
        };
    });
    const mountProductHeader  = (availableInUserCountry) => shallowMount(ProductHeader, {
        mocks: {
            $t: (word) => {
                return word;
            },
            $n: () => {},
            $tc: () => {}
        },
        vuetify,
        propsData: {
            availableInUserCountry
        }
    });

    it('should be displayed when product available in country', () => {
        const wrapper = mountProductHeader(true);

        expect(wrapper.vm.displayCta).toBeTruthy();
    });

    it('should not be displayed when product not available in country', () => {
        const wrapper = mountProductHeader(false);

        expect(wrapper.vm.displayCta).toBeFalsy();
    });
});

describe('ProductHeader Private App', () => {
    beforeAll(() => {
        window.frontendData = {
            coreData: defaultCoreData
        };
    });
    const mountProductHeader  = (storeAvailabilityMode,isThirdPartyApp) => mount(ProductHeader, {
        mocks: {
            $t: (word) => {
                return word;
            },
            $n: () => {},
            $tc: () => {}
        },
        vuetify,
        propsData: {
            storeAvailabilityMode,
            isThirdPartyApp
        }
    });

    it('private app badge displayed for 3rd party private app', () => {
        const wrapper = mountProductHeader(StoreAvailabilityMode.RESTRICTED_BUYER,true);

        expect(wrapper.vm.isThirdPartyPrivateApp).toBeTruthy();
    });

    it('private app badge should not be displayed for third party tool', () => {
        const wrapper = mountProductHeader(StoreAvailabilityMode.RESTRICTED_BUYER,false);

        expect(wrapper.vm.isThirdPartyPrivateApp).toBeFalsy();
    });
});


describe('ProductHeader Company', () => {

    const draftPropsData = {
        name: 'ABC Company',
        friendlyName: null,
        companyUid: '34104cd1-4885-4f54-aadb-7a5851e4c337',
        hasPublishedProfile: true,
        profileUrl: '/company-profile/34104cd1-4885-4f54-aadb-7a5851e4c337'
    };

    const mountProductHeader  = (iotCompanyData) => mount(ProductHeader, {
        mocks: {
            $t: (word) => {
                return word;
            },
            $n: () => {},
            $tc: () => {}
        },
        vuetify,
        propsData: {
            company: iotCompanyData
        }
    });

    let coreData = {};
    let propsData = {};
    beforeEach(() => {
        propsData = cloneDeep(draftPropsData);
        coreData = cloneDeep(defaultCoreData);
        coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        window.frontendData = {
            coreData : coreData
        };
    });

    it('is a link when profile is published', () => {
        propsData.hasPublishedProfile = true;

        const wrapper = mountProductHeader(propsData);

        expect(wrapper.find('[data-id="link-company-name"]').exists()).toBeTruthy();
        expect(wrapper.find('[data-id="text-company-name"]').exists()).toBeFalsy();
    });

    it('is text when company profile is published and feature switch disabled', () => {
        propsData.hasPublishedProfile = false;
        coreData.moduleConfig.ENABLE_COMPANY_PROFILE = false;

        const wrapper = mountProductHeader(propsData);

        expect(wrapper.find('[data-id="link-company-name"]').exists()).toBeFalsy();
        expect(wrapper.find('[data-id="text-company-name"]').exists()).toBeTruthy();
    });

    it('is text when company profile is not published', () => {
        propsData.hasPublishedProfile = false;

        const wrapper = mountProductHeader(propsData);

        expect(wrapper.find('[data-id="link-company-name"]').exists()).toBeFalsy();
        expect(wrapper.find('[data-id="text-company-name"]').exists()).toBeTruthy();
    });
});
