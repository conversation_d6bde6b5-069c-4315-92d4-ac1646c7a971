import ProductHeader from 'shop/productdetails/components/ProductHeader.vue';
import PricingSection from 'shop/productdetails/components/PricingSection.vue';
import GallerySection from 'shop/productdetails/components/GallerySection.vue';
import GallerySlideshow from 'shop/productdetails/components/GallerySlideshow.vue';
import ChangelogSection from 'shop/productdetails/components/ChangelogSection.vue';
import DescriptionSection from 'shop/productdetails/components/DescriptionSection.vue';
import DocumentsSection from 'shop/productdetails/components/DocumentsSection.vue';
import ReviewSection from 'shop/productdetails/components/ReviewSection.vue';
import SideBar from 'shop/productdetails/components/SideBar.vue';
import SupportSection from 'shop/productdetails/components/SupportSection.vue';
import RequirementsSection from 'shop/productdetails/components/RequirementsSection.vue';
import ReviewCreationForm from 'shop/productdetails/components/ReviewCreationForm.vue';
import DualUseSection from 'shop/productdetails/components/DualUseSection.vue';
import ReviewItem from 'shop/productdetails/components/ReviewItem.vue';
import IntegrationsSection from 'shop/productdetails/components/IntegrationsSection.vue';
import AppIntegrationLabels from 'shop/productdetails/components/AppIntegrationLabels.vue';
import PricingSectionSidebar from 'shop/productdetails/components/PricingSectionSidebar.vue';
import FollowAppChangesSection from 'shop/productdetails/components/FollowAppChangesSection.vue';
import GetOwnAppSection from 'shop/productdetails/components/GetOwnAppSection.vue';
import CompanyProfileSection from 'shop/productdetails/components/CompanyProfileSection.vue';

export {
    ProductHeader,
    PricingSection,
    GallerySection,
    GallerySlideshow,
    ChangelogSection,
    CompanyProfileSection,
    DescriptionSection,
    IntegrationsSection,
    DocumentsSection,
    ReviewSection,
    RequirementsSection,
    ReviewCreationForm,
    ReviewItem,
    DualUseSection,
    SideBar,
    SupportSection,
    AppIntegrationLabels,
    PricingSectionSidebar,
    FollowAppChangesSection,
    GetOwnAppSection
};
