<template>
  <div class="root">
    <div class="product" v-test:cart-item="item.entryNumber">
      <v-row>
        <v-col cols="12" class="d-flex">

          <div class="image-box">
            <a :href="productSelectionUrl(item)">
              <div ref="logo" class="logo">
                <div v-bind:style="{'background-image': 'url(' + item.logoUrl + ')'}"></div>
              </div>
            </a>
          </div>

          <v-row no-gutters class="product-info-row">
            <v-col cols="12" md="9">
              <div class="product-info ml-4">
                <div class="product-name">
                  <h3>
                    <span class="mr-1">{{ item.productName }}</span>
                    <CDChip v-if="LicenseType.FULL !== item.licenseType" class="labeled-license">
                      {{ $t(`shop.license.${item.licenseType.toLowerCase()}.name`) }}
                    </CDChip>
                    <CDChip v-if="isOwnAppPurchase">
                      {{ $t('shop.cartItem.ownApp.title') }}
                    </CDChip>
                  </h3>

                </div>
                <div class="product-code">
                  <p class="mb-0">{{item.appCode}}</p>
                </div>

                <div v-if="!isOwnAppPurchase" class="unit-price">
                  <div>
                    <cart-item-bundle-info :bundle-info="bundleInfo"></cart-item-bundle-info>
                    {{ item.itemPrice.symbol }} {{ item.itemPrice.value }} {{subscriptionDuration}}
                    <span data-id="text-future-price" v-if="futureSubscriptionPrice">
                      /
                      {{
                        $t(
                            'shop.checkout.orderEntries.futurePrice',
                            { futurePrice: formatPrice(futureSubscriptionPrice) }
                        )
                      }}
                    </span>
                  </div>

                  <div v-if="showDiscount" class="text-decoration-line-through">
                    {{ basePrice.currencyIso }} {{ basePrice.formattedValue }}
                  </div>
                </div>

                <div v-if="isOwnAppPurchase" class="own-app-fee-note">
                  {{$t('shop.checkout.ownApp.feeNote')}}
                </div>

              </div>
            </v-col>

            <v-col cols="12" md="3" class="d-lg-flex hidden-lg-and-down justify-end">
              <div class="total-price font-weight-bold">
              <span v-if="!isOwnAppPurchase">
                {{ item.totalPrice.symbol }} {{ item.totalPrice.value }}
              </span>
              </div>
              <div class="cart-actions ml-4">
                <CDIcon :data-id="`icon-delete-action-${item.entryNumber}`" class="delete-action" color="black" v-on:click="deleteItem">
                  $trash
                </CDIcon>
              </div>
            </v-col>

          </v-row>
        </v-col>
      </v-row>

      <v-row class="align-baseline" no-gutters>
        <v-col cols="4" lg="4">
            <div class="quantity">
              <CDInput
                  class="mt-2"
                  type="number"
                  ref="quantityInput"
                  :label="$t('quantity')"
                  v-model="item.quantity"
                  min="1"
                  v-test:detail-quantity.v-input.input="item.entryNumber"
                  v-on:keydown="validateInput($event)"
                  v-on:blur="editItem($event.target.value)"
                  v-on:keyup.enter="editItem($event.target.value)"
                  v-on:keydown.esc="escape($event)"></CDInput>
            </div>
          </v-col>

          <v-col cols="8" class="d-flex d-lg-none justify-end">
            <div class="total-price font-weight-bold">
            <span v-if="!isOwnAppPurchase">
              {{ item.totalPrice.symbol }} {{ item.totalPrice.value }}
            </span>
            </div>
            <div class="cart-actions ml-4">
              <CDIcon :data-id="`icon-delete-action-${item.entryNumber}`" class="delete-action" color="black" v-on:click="deleteItem">
                $trash
              </CDIcon>
            </div>
          </v-col>

      </v-row>

      <v-row v-if="discounts.length > 0" no-gutters>
        <v-col cols="12">
          <div class="unit-row discount-hint d-flex align-center">
            <tooltip class="hint">
                <CDIcon color="black" class="mr-2">$discount</CDIcon>
                <template #text>
                  <p v-for="discount in discounts" v-bind:key="discount.minQuantity"
                     v-bind:class="{highlighted: isActiveDiscount(discount)}">
                    {{
                      $t('shop.cartItem.hint', {
                        min: discount.minQuantity,
                        percent: calcPercent(discount.value)
                      })
                    }}
                  </p>
                </template>
            </tooltip>
            <div class="text">{{$t('shop.cartItem.volumeDiscount')}}</div>
          </div>
        </v-col>
      </v-row>

      <v-row v-if="quantityValidationMessage !== ''">
        <v-col cols="12">
          <CDNotificationPersistent v-model="showValidationMessage" type="error" :dismissible="hasQuantityValidationMessage" class="quantity-validation" v-on:input="closeNotification" >
            <p class="mb-0">{{ quantityValidationMessage }}</p>
          </CDNotificationPersistent>
        </v-col>
      </v-row>

      </div>
    </div>

</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import util from 'common/util';
import {
  BundleInfoData,
  CartItemData,
  PriceData,
  ScalePriceInfo,
  ShopCoreData,
  StoreErrorResponseData
} from 'common/generated-types/types';
import {pageSpinner} from 'common/components/spinner';
import {inputValidationService, messageService} from 'common/services';
import {CartResource} from 'shop/resources';
import Tooltip from 'common/components/Tooltip.vue';
import {round} from 'lodash';
import {LicenseType} from 'common/types';
import { BaseStores, ShopRoute } from 'common/constants';
import {useCartStore} from 'shop/store/cart';
import {CartItemBundleInfo} from 'shop/core/components/cartitembundleinfo';
import { ShopCoreDataProvider } from 'common/provider/coreDataProvider';

const CART_DATA_CHANGED = 'cart-data-changed';

@Component({
  components: {
    CartItemBundleInfo,
    Tooltip
  }
})
export default class CartItem extends Vue {
  @Prop() item!: CartItemData;
  @Prop() maxQuantity!: number;
  @Prop() cartCode!: string;
  @Prop() isOwnAppPurchase!: boolean;

  coreData: ShopCoreData = ShopCoreDataProvider.data;
  cartStore = useCartStore();

  $refs!: {
    logo: HTMLElement;
    quantityInput: HTMLElement;
  };

  LicenseType = LicenseType;
  oldQuantity = this.item.quantity;
  quantityValidationMessage = '';
  showValidationMessage = true;

  cartResource = new CartResource(this.cartCode);

  get basePrice(): ScalePriceInfo | undefined {
    return this.item.scalePrices?.find(value => value.minQuantity <= 1);
  }

  get showDiscount(): boolean {
    return !!this.basePrice && this.basePrice.formattedValue !== this.item.itemPrice.value;
  }

  get discounts(): ScalePriceInfo[] {
    return this.item.scalePrices ? this.item.scalePrices.filter(v => v.minQuantity !== 1) : [];
  }

  get isSubscription(): boolean {
      return this.item.licenseType === LicenseType.SUBSCRIPTION;
  }

  get subscriptionDuration(): string {
    return this.item.licenseType === LicenseType.SUBSCRIPTION ? this.$t('shop.cartItem.for12Months').toString() : '';
  }

  get futureSubscriptionPrice(): PriceData | null {
      const productFuturePrices = this.item.productFuturePrices;
      const hasFuturePrices = productFuturePrices?.length;
      return this.isSubscription && hasFuturePrices ? productFuturePrices[0] : null;
  }

  get bundleInfo(): BundleInfoData {
    return this.item.bundleInfo;
  }

  get isDirectSalesEnabled(): boolean {
    return Boolean(this.coreData.moduleConfig?.DIRECT_SALES) && this.coreData.basestore === BaseStores.AA;
  }

  productSelectionUrl(item: CartItemData): string {
    if(this.isDirectSalesEnabled) {
      return ShopRoute.PRODUCT_SELECTION(item.appCode);
    } else {
      return item.productUrl;
    }
  }

  formatPrice(price: PriceData) : string {
    return `${price.symbol} ${price.value}`;
  }

  @Watch('item', {immediate: true})
  onItemChange(): void {
    this.$nextTick(() => {
      util.loadLogoImage([{logoUrl: this.item.logoUrl, logoElement: this.$refs.logo}]);
    });
  }

  deleteItem(): void {
    if (pageSpinner.isActive) {
      return;
    }

    pageSpinner.start();
    messageService.clearAll();

    this.cartResource.removeEntry(this.item.entryNumber)
        .then(response => {
          pageSpinner.stop(150);
          this.$emit(CART_DATA_CHANGED, response.data);
          this.cartStore.incrementQuantity(-this.item.quantity);
          messageService.success(this.$t('shop.cartItem.itemRemovedSuccessMessage') as string, false, true);
        })
        .catch(error => {
          pageSpinner.stop();
          console.log(error);
          messageService.error(this.$t('shop.cartItem.itemRemovedErrorMessage') as string);
        });
  }

  resetToPreviousQuantity():void {
      this.item.quantity = this.oldQuantity;
  }

  validateInput(e: KeyboardEvent): void {
      const inputElement = e.target as HTMLInputElement;
      const inputString = inputElement.value as string;
      const eventKey = e.key as string;
      if (!inputValidationService.isPermissibleQuantity(inputString, this.maxQuantity) ||
          eventKey === '.' ||
          eventKey === '0' && inputString.length === 0 ||
          eventKey === '-') {
        e.preventDefault();
        this.resetToPreviousQuantity();
      }
  }

  escape(e: Event): void {
    e.preventDefault();
    this.resetToPreviousQuantity();
  }

  editItem(quantity: string): void {
    const newQuantity = Number(quantity);

    if (newQuantity === 0) {
        this.resetToPreviousQuantity();
    }

    if (pageSpinner.isActive || !inputValidationService.isNumber(quantity) || this.oldQuantity === newQuantity) {
      return;
    }

    this.item.quantity = newQuantity;
    pageSpinner.start();
    messageService.clearAll();

    this.cartResource.updateCart(this.item).then(response => {
      const responseData = response.data;
      const updatedCartItem = responseData.cartItems.filter((item: CartItemData) => item.productCode === this.item.productCode);
      if (updatedCartItem.length === 0) {
        messageService.error(this.$t('shop.cartItem.messageOnUpdateCartFailed') as string);
        this.$emit(CART_DATA_CHANGED, responseData);
        pageSpinner.stop();
        return;
      }
      this.$emit(CART_DATA_CHANGED, responseData);
      const changedQuantity = updatedCartItem[0].quantity - this.oldQuantity;
      this.$nextTick(() => {
        this.quantityValidationMessage = '';
        this.showValidationMessage = false;
        if (changedQuantity !== 0) {
          const currentDiscount = this.discounts.find(d => this.isActiveDiscount(d));
          const updateMessage = this.showDiscount && currentDiscount
              ? this.$t('shop.cartItem.messageOnUpdateCartWithDiscount', {
                percent: this.calcPercent(currentDiscount.value)
              }) as string
              : this.$t('shop.cartItem.messageOnUpdateCart') as string;
          messageService.success(updateMessage, false, true);
          this.cartStore.incrementQuantity(changedQuantity);
          this.oldQuantity = newQuantity;
        }
      });
      pageSpinner.stop();
    }).catch((error) => {
      pageSpinner.stop();
      messageService.clearAll();
      if (error.response?.status === 400) {
        const errorResponseData = (error.response.data as StoreErrorResponseData);
        if (errorResponseData?.errorCode === 'cart.invalidQuantity') {
          this.quantityValidationMessage = this.$t('shop.error.cart.invalidQuantity',
            [this.coreData.allowedMaxQuantityLineItemFullLicense, this.coreData.allowedMaxQuantityLineItemSubsLicense])
            .toString();
          this.showValidationMessage = true;
        } else if (errorResponseData?.userMessages && errorResponseData.userMessages.length > 0) {
          const userMessage = errorResponseData.userMessages[0];
          this.quantityValidationMessage = messageService.translateOrDefault(userMessage, this.$i18n);
          this.showValidationMessage = true;
        } else {
          messageService.error(this.$t('shop.cartItem.updateCartGeneralError') as string);
        }
      } else {
        messageService.error(this.$t('shop.cartItem.updateCartGeneralError') as string);
      }
    });
  }

  calcPercent(value: number): number | undefined {
    if (this.basePrice) {
      return round((this.basePrice.value - value) / this.basePrice.value * 100);
    }
  }

  isActiveDiscount(discount: ScalePriceInfo): boolean {
    return this.item.quantity >= discount.minQuantity && (this.item.quantity <= discount.maxQuantity || discount.maxQuantity == null);
  }

  hasQuantityValidationMessage(): boolean {
    return this.quantityValidationMessage !== '';
  }

  closeNotification() {
    messageService.clearAll();
    this.resetToPreviousQuantity();
  }
}
</script>

<style lang="scss" scoped>
@import "shop/cartpage/components/cartlistgridsystem";
</style>
