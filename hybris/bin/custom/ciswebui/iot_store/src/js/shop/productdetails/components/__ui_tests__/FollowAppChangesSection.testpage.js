import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import {FollowAppChangesSection} from '../index';

window.frontendData.pageData={ code: 'A_123456', name: 'testApp',followAppData: {subscribed:false}};

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    data: {},
    components: {
        TestPageRoot,
        FollowAppChangesSection
    },
    methods: {},
    template: `
      <test-page-root>
        <follow-app-changes-section>
        </follow-app-changes-section>
      </test-page-root>
    `
});
