<template>
    <div class="dual-use-section">
        <v-container :fluid="true" class="pa-0">
            <v-container :fluid="$vuetify.breakpoint.lgAndDown">
                <h2 class="label">{{ $t('shop.productDetails.dualUseInfo.header') }}</h2>
                <p>{{infoText}}</p>
                <p><a v-bind:href="exportOverviewUrl">{{$t('shop.productDetails.dualUseInfo.linkText')}}</a></p>
            </v-container>
        </v-container>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {ShopRoute} from 'common/constants';

    @Component
    export default class DualUseSection extends Vue {
        @Prop() infoText!: string;

        readonly exportOverviewUrl = ShopRoute.EXPORT_INFORMATION_OVERVIEW;
    }
</script>
