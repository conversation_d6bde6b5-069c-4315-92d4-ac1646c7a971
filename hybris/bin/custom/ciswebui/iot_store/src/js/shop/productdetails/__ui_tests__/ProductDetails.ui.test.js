import {
    acceptCookieBanner,
    createInlineUiTestPage,
    resetMouseCoords,
    scrollDown,
    scrollIntoView
} from 'common/testtools/inlineTestPageCreator';
import {abortRequest, respondRequest} from 'common/testtools/testRequests';
import {testMap} from 'common/testtools/scenariosstore';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';
import {StoreAvailabilityMode} from 'common/generated-types/types';
import companyProfileDataWithLogo from 'common/testtools/scenariosstore/companyProfileDataWithLogo.json';
import {breakpoints} from 'common/breakpoints';

describe('ProductDetailsPage', () => {

    let styleContent;
    beforeEach(() => {
        styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    });

    it('looks as expected on different screen sizes', async () => {
        await createInlineUiTestPage(testMap.ProductDetails);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});

        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });

    it('looks as expected with minimal page data', async () => {
        Object.assign({}, testMap.MinimalProductDetails);
        let coreData = cloneDeep(testMap.ProductDetails.coreData);
        coreData.allowedToBuy = true;
        await createInlineUiTestPage(testMap.MinimalProductDetails);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await Sizes.testExtraLargeScreensize(3200);
        await Sizes.testLargeScreensize(3200);
        await Sizes.testMediumScreensize(3500);
        await Sizes.testSmallScreensize(3800);
    });

    it('looks as expected when no license is present', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        let pageData = cloneDeep(testMap.ProductDetails.pageData);
        pageData.licenses = [];
        data.pageData = pageData;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(800);
        expect(await page.screenshot()).toMatchImageSnapshot('init');
    });

    it('looks as expected when app is not available in current country', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        let pageData = cloneDeep(testMap.ProductDetails.pageData);
        let coreData = cloneDeep(testMap.ProductDetails.coreData);
        pageData.enabledCountries = [{
            'currencyIsoCode': 'EUR',
            'isocode': 'VA',
            'name': 'Vatican City State',
            'canBuy': true,
            'sepaEnabled': true
        }];
        data.pageData = pageData;
        data.coreData = coreData;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('not available in your country header');

        await page.waitFor(1000);
        await page.$eval('.product-details-pricing', scrollIntoView);
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('not available in your country pricing section');
    });

    it('looks as expected when app is private and user country not in enabled countries', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.enabledCountries = [];
        data.pageData.storeAvailabilityMode = StoreAvailabilityMode.RESTRICTED_BUYER;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('private app available regardless of countries restrictions');

        await page.waitFor(1000);
        await page.$eval('.product-details-pricing', scrollIntoView);
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('private app available pricing section');
    });

    it('looks as expected when downloading own apps is enabled', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        let pageData = cloneDeep(testMap.ProductDetails.pageData);
        pageData.ownCompanyAppAndCanBuy = true;
        data.pageData = pageData;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(600);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot('');
    });

    it('looks as expected when downloading own apps is enabled and quantity is focused', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        let pageData = cloneDeep(testMap.ProductDetails.pageData);
        pageData.ownCompanyAppAndCanBuy = true;
        data.pageData = pageData;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 1200});
        await page.$eval('[data-id="input-quantity-get-own-apps"]', scrollIntoView);
        await page.click('[data-id="input-quantity-get-own-apps"]');
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('looks as expected after click on button in header on mobile', async () => {
        await createInlineUiTestPage(testMap.ProductDetails);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 576, height: 700});
        await page.$eval('.purchase-button', scrollIntoView);
        await page.waitFor(1000);
        await page.click('#purchase');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('Click on button in header jumps to pricing section and focuses correct input', async () => {
        await createInlineUiTestPage(testMap.ProductDetails);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(800);
        await page.click('#trial');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('Evaluation license input focused');
        await page.$eval('.purchase-button', scrollIntoView);
        await page.waitFor(1000);
        await page.click('#purchase');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('Full license input focused');
    });

    it('Click on purchase button in header jumps to subscription license pricing section when product has both full and subscription licenses', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        const subscriptionLicense = {
            'code': 'A_00000017_sub',
            'name': 'Subscription',
            'summary': null,
            'price': 100,
            'currencyIsocode': 'USD',
            'currencySymbol': 'USD',
            'licenseType': {
                'code': 'SUBSCRIPTION',
                'type': 'LicenseType'
            },
            'quantity': 0,
            'purchasability': 'PURCHASABLE',
            'scalePrices': [],
            'futurePrices': []
        };
        data.pageData.licenses.push(subscriptionLicense);

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.$eval('.purchase-button', scrollIntoView);
        await page.waitFor(1000);
        await page.click('#purchase');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('Subscription license input focused');
    });

    it('Click on purchase button in header jumps to subscription license pricing section when product has subscription license and no full license', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.licenses = [{
            'code': 'A_00000017_sub',
            'name': 'Subscription',
            'summary': null,
            'price': 100,
            'currencyIsocode': 'USD',
            'currencySymbol': 'USD',
            'licenseType': {
                'code': 'SUBSCRIPTION',
                'type': 'LicenseType'
            },
            'quantity': 0,
            'purchasability': 'PURCHASABLE',
        }];

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.$eval('.purchase-button', scrollIntoView);
        await page.waitFor(1000);
        await page.click('#purchase');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('Subscription license input focused');
    });

    it('Selecting a license enables addToCart button', async () => {
        await createInlineUiTestPage(testMap.ProductDetails);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(2000);
        await page.click('#trial');
        await page.waitFor(2000);
        await page.type('input[name="FULL"]', '3');
        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('AddToCartButton enabled');
    });

    it('Click on image opens zoom gallery', async () => {
        Object.assign({}, testMap.MinimalProductDetails);
        let coreData = cloneDeep(testMap.ProductDetails.coreData);
        coreData.allowedToBuy = true;

        await createInlineUiTestPage(testMap.ProductDetails);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(800);
        await page.click('.image:nth-of-type(1)');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('zoom opened');
    });

    it('Shows a rounded down number (including "+" symbol and correct comma/decimal formatting) of +5,000 of purchases given installCount: 5123', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.acquisitionCount = 5123;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('installs count visible');
    });

    it('Looks as expected for anonymous user', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.coreData.userName = null;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot('');
    });

    it('looks as expected for user without purchase App permission', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.acquisitionCount = 0;
        data.coreData.allowedToBuy = false;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(600);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('looks as expected for user with purchase App permission but company is not approved', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.acquisitionCount = 0;
        data.coreData.allowedToBuy = true;
        data.coreData.currentCompany.companyApproved = false;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(600);

        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot('');
    });

    it('looks as expected for user without purchase permission and only full license available', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.acquisitionCount = 0;
        data.coreData.allowedToBuy = false;
        data.pageData.licenses = [{
            'code': 'A_00000017_full',
            'name': 'Purchase',
            'summary': null,
            'price': 1999,
            'currencyIsocode': 'USD',
            'currencySymbol': 'USD',
            'licenseType': {
                'code': 'FULL',
                'type': 'LicenseType'
            },
            'quantity': 0
        }
        ];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(600);

        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot('');
    });

    it('looks as expected when app is read only', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        let pageData = cloneDeep(testMap.ProductDetails.pageData);
        pageData.enabledCountries = [];
        data.pageData = pageData;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(600);

        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot('');
    });

    it('looks as expected when scale prices are provided', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.licenses = [
            {
                'code': 'A_00000017_evaluation',
                'name': 'Trial',
                'summary': null,
                'price': 0,
                'currencyIsocode': 'USD',
                'currencySymbol': 'USD',
                'licenseType': {
                    'code': 'EVALUATION',
                    'type': 'LicenseType'
                },
                'purchasability': 'PURCHASABLE',
                'quantity': 0,
                'scalePrices': null
            },
            {
                'code': 'A_00000017_full',
                'name': 'Purchase',
                'summary': null,
                'price': 150,
                'currencyIsocode': 'USD',
                'currencySymbol': 'USD',
                'licenseType': {
                    'code': 'FULL',
                    'type': 'LicenseType'
                },
                'purchasability': 'PURCHASABLE',
                'quantity': 0,
                'scalePrices': [
                    {
                        'minQuantity': 0,
                        'discountPercent': 0,
                        'discountPrice': 150,
                        'currencyIsoCode': 'EUR'
                    },
                    {
                        'minQuantity': 10,
                        'discountPercent': 10,
                        'discountPrice': 135,
                        'currencyIsoCode': 'EUR'
                    }
                ]
            }
        ];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(1000);
        const input_full = await page.$('input[name="FULL"]', scrollIntoView);
        await input_full.type('15', {});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('shows reduced price and strikeout full price');
    });

    it('correct eula look standard', async () => {
        let data = Object.assign({}, testMap.ProductDetails);
        let pageData = cloneDeep(testMap.ProductDetails.pageData);
        pageData.appContactInfo.eula.type = 'STANDARD';
        data.pageData = pageData;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('correct eula look standard with document', async () => {
        let data = Object.assign({}, testMap.ProductDetails);
        let pageData = cloneDeep(testMap.ProductDetails.pageData);
        pageData.appContactInfo.eula.type = 'STANDARD';
        pageData.appContactInfo.eula.standardEulaAppendix = {
            "displayName": "Test document",
            "url": "/app/manuals/pc_00001004_documentation-files_6f464b36-f7e5-4aa6-8db3-37e9533aca28",
            "size": 249136,
            "code": "pc_00001004_documentation-files_6f464b36-f7e5-4aa6-8db3-37e9533aca28"
        };
        data.pageData = pageData;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('should have empty lines in description ', async () => {
        let data = Object.assign({}, testMap.ProductDetails);
        let pageData = cloneDeep(testMap.ProductDetails.pageData);
        pageData.description = `<p>People Counter runs fully embedded in ceiling-mounted network</p>
                                <p></p>
                                <p>cameras and automatically counts in real time the number of people passing  
                                  <strong>direction. Based on proven technology, it </strong>
                                </p>
                                <p><strong></strong></p>
                                <p>
                                  <strong>provides the data you need to leverage your business. Visitor traffic</strong> 
                                  knowledge allows you to 
                                  <strong>analyze customer flows 
                                    <em>and traffic trends, </em>
                                  </strong>
                                </p>
                                <p><strong><em></em></strong></p>
                                <p>
                                  <strong>
                                    <em>learn about impact</em> of advertising and promotions, improve staff
                                  </strong> 
                                  <em>planning, optimize opening hours, identify 
                                    <strong>and reward high </strong>
                                  </em>
                                </p>
                                <p><em><strong></strong></em></p>
                                <p><em><strong>performing stores</strong> and employees.</em></p>`
        data.pageData = pageData;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('buyer from blocked country cannot purchase commercial licences', async () => {
        const coreData = cloneDeep(testMap.ProductDetails.coreData);
        const dataWithBlockedCountry = cloneDeep(testMap.ProductDetails.pageData);
        dataWithBlockedCountry.licenses = dataWithBlockedCountry.licenses
            .map(license => ({ ...license, purchasability: 'COUNTRY_BLOCKED' }));
        dataWithBlockedCountry.galleryItems = [];
        dataWithBlockedCountry.integratorCountryBlockedInDeveloperCountry = true;

        await createInlineUiTestPage({...testMap.ProductDetails, pageData: dataWithBlockedCountry, coreData: coreData});
        await acceptCookieBanner();
        await scrollDown();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });
});

describe('ProductDetailsPage Sidebar', () => {

    let styleContent;
    beforeEach(() => {
        styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    });

    it('does not contain country availability information when app is private', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.storeAvailabilityMode = StoreAvailabilityMode.RESTRICTED_BUYER;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('contains country availability information when app is not private', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.storeAvailabilityMode = StoreAvailabilityMode.PUBLIC;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('does not contains follow app changes component for Anonymous User', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.coreData.userName = null;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('contains follow app changes component for Logged-in User but not subscribed', async () => {
        let data = cloneDeep(testMap.ProductDetails);

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('contains follow app changes component for subscribed Logged-in User', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.coreData.userName = 'testUser';
        data.pageData.followAppData.subscribed = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });
});

describe('ProductDetailsPage Header', () => {

    let styleContent;
    beforeEach(() => {
        styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    });

    it('contains company name as link when profile is published', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.company.hasPublishedProfile = true;
        data.pageData.company.profileUrl = '/company-profile/uuid';
        data.coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await Sizes.testExtraLargeScreensize(1809);
        await Sizes.testLargeScreensize(1906);
        await Sizes.testMediumScreensize(2514);
        await Sizes.testSmallScreensize(2495);
    });

    it('contains app followed by chip when private', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        data.pageData.storeAvailabilityMode = StoreAvailabilityMode.RESTRICTED_BUYER;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await Sizes.testExtraLargeScreensize(1000);
        await Sizes.testLargeScreensize(1000);
        await Sizes.testMediumScreensize(1000);
        await Sizes.testSmallScreensize(1000);
    });

    describe('App integrations', () => {

        const iotGatewayIntegration = {
            "displayName": "Azena IoT Gateway",
            "externalDescription": "This app uses our IoT gateway and can share data via MQTT and REST.",
            "integrationType": "GATEWAY",
            "type": "STANDARD",
            "documentation": null
        };
        const onvifMetadataIntegration = {
            "displayName": "ONVIF metadata",
            "externalDescription": "This app sends metadata in an ONVIF XML schema.",
            "integrationType": "STANDARD",
            "type": "STANDARD",
            "documentation": null
        };
        const customIntegration = {
            "displayName": "Other custom",
            "externalDescription": null,
            "integrationType": null,
            "type": "CUSTOM",
            "documentation": null
        };

        it('looks as expected with one standard app integration', async () => {
            let data = cloneDeep(testMap.ProductDetails)
            data.pageData.appIntegrations = [iotGatewayIntegration];

            await createInlineUiTestPage(data);
            await acceptCookieBanner();
            await page.addStyleTag({content: styleContent});
            await Sizes.testExtraLargeScreensize(1000);
            await Sizes.testLargeScreensize(1000);
            await Sizes.testMediumScreensize(1000);
            await Sizes.testSmallScreensize(1000);
        });

        it('looks as expected with two standard app integrations', async () => {
            let data = cloneDeep(testMap.ProductDetails);
            data.pageData.appIntegrations = [iotGatewayIntegration, onvifMetadataIntegration];

            await createInlineUiTestPage(data);
            await acceptCookieBanner();
            await page.addStyleTag({content: styleContent});
            await Sizes.testExtraLargeScreensize(1000);
            await Sizes.testLargeScreensize(1000);
            await Sizes.testMediumScreensize(1000);
            await Sizes.testSmallScreensize(1000);
        });

        it('looks as expected with standard and custom app integrations', async () => {
            let data = cloneDeep(testMap.ProductDetails)
            data.pageData.appIntegrations = [iotGatewayIntegration, onvifMetadataIntegration, customIntegration];

            await createInlineUiTestPage(data);
            await acceptCookieBanner();
            await page.addStyleTag({content: styleContent});
            await Sizes.testExtraLargeScreensize(1000);
            await Sizes.testLargeScreensize(1000);
            await Sizes.testMediumScreensize(1000);
            await Sizes.testSmallScreensize(1000);
        });
    });
});

describe('ProductDetailsPage Pricing Section', () => {

    let styleContent;
    beforeEach(() => {
        styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    });

    it('displays message about price change when product has future prices for subscription', async () => {
        let data = cloneDeep(testMap.ProductDetails);
        const subscriptionLicense = {
            'code': 'A_00000017_sub',
            'name': 'Subscription',
            'summary': null,
            'price': 100,
            'currencyIsocode': 'USD',
            'currencySymbol': 'USD',
            'licenseType': {
                'code': 'SUBSCRIPTION',
                'type': 'LicenseType'
            },
            'quantity': 0,
            'purchasability': 'PURCHASABLE',
            'scalePrices': [],
            'futurePrices': [
                {
                    "currencyIsoCode": "USD",
                    "price": "200",
                    "startDate": "2023-02-01T00:00:00.000Z"
                }
            ]
        };
        data.pageData.licenses = [subscriptionLicense];

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});

        const elementToScrollIntoView = '[data-id="text-future-prices"]';

        await page.$eval(elementToScrollIntoView, scrollIntoView);
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('large size');

        await page.setViewport({width: breakpoints.XS, height: 1000});
        await page.waitFor(1000);
        await page.$eval(elementToScrollIntoView, scrollIntoView);
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot("extra small size");
    });
});

describe('ProductDetailsPage for Tool App', () => {

    let styleContent;
    beforeEach(() => {
        styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    });

    it('looks as expected on different screen sizes', async () => {
        await createInlineUiTestPage(testMap.ToolProductDetails);
        await page.waitFor(5000);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await resetMouseCoords();
        await page.waitFor(1000);
        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });

    it('Click on button in header jumps to pricing section and focuses correct input', async () => {
        await createInlineUiTestPage(testMap.ToolProductDetails);
        await page.waitFor(5000);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await page.waitFor(800);
        await page.click('#tool');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot('Tool license input focused');
    });

    it('looks as expected for tool app', async () => {
        await createInlineUiTestPage(testMap.ToolProductDetails);
        await page.waitFor(5000);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await resetMouseCoords();
        await page.waitFor(1000);
        await page.setViewport({width: 1200, height: 4500});
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot('with pricing section');
    });

    it('Follow tool looks as expected on different screen sizes', async () => {
        await createInlineUiTestPage(testMap.FollowAppSectionToolProductDetails);
        await page.waitFor(5000);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await resetMouseCoords();
        await page.waitFor(1000);
        await Sizes.testExtraLargeScreensize(4400);
        await Sizes.testLargeScreensize(4500);
        await Sizes.testMediumScreensize(5100);
        await Sizes.testSmallScreensize(8000);
    });
});

describe('ProductDetailsPage with Company profile section', () => {

    let styleContent;
    beforeEach(async () =>{
        await page.setRequestInterception(true);
        styleContent = `input {
                      caret-color: transparent !important;
                    }`;
    });

    const REST_URL = '/shop/api/app/A_00000017/company-profile';
    const companyProfileRestHandler = respondRequest(REST_URL,{
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(companyProfileDataWithLogo)
    });

    it('looks as expected with company profile section on different screen sizes', async () => {
        page.on('request', companyProfileRestHandler);

        let data = cloneDeep(testMap.ProductDetailsWithCompanyProfileSection);
        data.coreData.allowedToBuy = true;
        data.coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        await createInlineUiTestPage(data);

        await page.waitFor(5000);
        await acceptCookieBanner();
        await page.addStyleTag({content: styleContent});
        await resetMouseCoords();
        await page.waitFor(1000);
        await Sizes.testExtraLargeScreensize(6000);
        await Sizes.testLargeScreensize(6000);
        await Sizes.testMediumScreensize(6000);
        await Sizes.testSmallScreensize(8000);
    });

    it('shows message for error response', async () => {
        page.on('request', respondRequest(REST_URL, {
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({userMessages:[{level: 'ERROR', code:'product.notFound'}]})
        }));

        let data = cloneDeep(testMap.ProductDetailsWithCompanyProfileSection);
        data.coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        await createInlineUiTestPage(data);

        await page.waitFor(1000);

        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('shows message for error response without error details', async () => {
        page.on('request', respondRequest(REST_URL, {
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({})
        }));

        let data = cloneDeep(testMap.ProductDetailsWithCompanyProfileSection);
        data.coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        await createInlineUiTestPage(data);

        await page.waitFor(1000);

        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('shows message for aborted request', async () => {
        page.on('request', abortRequest(REST_URL));

        let data = cloneDeep(testMap.ProductDetailsWithCompanyProfileSection);
        data.coreData.moduleConfig.ENABLE_COMPANY_PROFILE = true;
        await createInlineUiTestPage(data);

        await page.waitFor(1000);

        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });
});
