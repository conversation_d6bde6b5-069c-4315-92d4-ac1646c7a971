import {acceptCookieBanner, createInlineUiTestPage} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';
import oneCartData from 'common/testtools/scenariosstore/oneCartData.json';
import {BaseStores} from "../../../common/constants";

describe('cart page', () => {

  it('shows multi company cart', async () => {

    const data = cloneDeep(testMap.Cart);
    await createInlineUiTestPage(data);
    await acceptCookieBanner();

    await page.setViewport({width: 1200, height: 1472});
    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("extra large size");

    await page.setViewport({width: 992, height: 2054});
    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("large size");

    await page.setViewport({width: 720, height: 2095});
    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("medium size");

    await page.setViewport({width: 576, height: 2307});
    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("small size");
  });

  it('shows empty cart', async () => {

    const data = cloneDeep(testMap.CartEmpty);
    await createInlineUiTestPage(data);
    await acceptCookieBanner();

    await page.setViewport({width: 1200, height: 1400});
    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("extra large size");

    await page.setViewport({width: 992, height: 1400});
    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("large size");

    await page.setViewport({width: 720, height: 1400});
    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("medium size");

    await page.setViewport({width: 576, height: 1400});
    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("small size");

  });

  it('shows cart for own app purchase', async () => {
    const data = cloneDeep(testMap.OwnAppCart);
    await createInlineUiTestPage(data);
    await acceptCookieBanner();

    await page.waitFor(1500);
    expect(await page.screenshot()).toMatchImageSnapshot("ownAppPurchase UI elements are shown");
  });

  it('shows future prices for subscription license when present', async () => {
    const data = cloneDeep(testMap.CartFuturePrices);
    await createInlineUiTestPage(data);
    await acceptCookieBanner();

    await Sizes.testLargeScreensize(2000);
    await Sizes.testMediumScreensize(2000);
    await Sizes.testSmallScreensize(2000);
    await Sizes.testExtraSmallScreensize(2000);
  });

  describe('Cart interactions', () => {
    let data;
    beforeEach(async () => {
      data = cloneDeep({
        ...testMap.Cart,
        pageData: oneCartData
      });

      const cartResourcesRestHandler = request => {
        if (request.url().includes('/shop/api/cart')) {
          request.respond({
            status: 200,
            contentType: 'application/json',
            body: ''
          });
        } else {
          request.continue();
        }
      };
      await page.setRequestInterception(true);
      page.on('request', cartResourcesRestHandler);
    });

    afterEach(async () => {
      await page.setRequestInterception(false);
    });

    it('notification after item deletion', async () => {
      data.coreData.basestore = BaseStores.AZENA;
      await createInlineUiTestPage(data);
      await acceptCookieBanner();

      await page.click('[data-id="icon-delete-action-0"]');
      await page.waitFor(500);

      await Sizes.testLargeScreensize(2000);
    });
  });
});
