describe("ReviewSection", () => {
    it("looks as expected for each reviewState", async () => {
        await page.setViewport({width: 1200, height: 1000});
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-ReviewSection');
        await page.waitFor(400);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("different review states");
    });

    it("opens ReviewCreationForm when button is clicked", async () => {
        await page.setViewport({width: 1200, height: 1500});
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-ReviewSection');
        await page.click(".form-button");
        await page.waitFor(400);
        expect(await page.screenshot({fullPage: true})).toMatchImageSnapshot("review creation form active");
    })
});