<template>
    <div class="slideshow-wrapper" v-on:click="$emit('closeZoom')">
        <vue-gallery-slideshow
                v-bind:images="galleryImages"
                v-bind:index="index"
                v-on:close="$emit('closeZoom')">
        </vue-gallery-slideshow>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {GalleryItemData} from 'common/generated-types/types';
    import VueGallerySlideshow from 'vue-gallery-slideshow';

    @Component({components: {VueGallerySlideshow}})
    export default class GallerySlideshow extends Vue {
        @Prop() galleryImages!: GalleryItemData[];
        @Prop() index!: number;

        showZoom(index: number): void {
            this.index = index;
        }

    }

</script>

<style lang="scss">
    .slideshow-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        padding: 95px 48px;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2);
        z-index: 9998;
        .vgs {
            position: relative;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            background-color: white;
            display: grid;
            grid-template-columns: 92px 1fr 92px;
            grid-template-rows: 24px 1fr fit-content;
            width: -moz-fit-content;
            width: fit-content;
            height: unset;
            min-height: unset;

            .vgs__container {
                grid-row: 2 / 2;
                grid-column: 2 / 2;
                border-radius: 0;
                background-color: transparent;
                position: relative;
                left: unset;
                right: unset;
                height: unset;
                margin: auto;

                @media screen and (max-width: 768px) {
                    left: unset;
                    right: unset;
                    height: unset;
                    margin: auto;
                    top: unset;
                }

                .vgs__container__img {
                    object-fit: contain;
                }
            }

            .vgs__gallery {
                grid-row: 3 / 3;
                grid-column: 2 / 2;
                position: relative;
                margin: 0;
                left: 0;
                right: 0;
                overflow-x: auto;

                @media screen and (max-width: 768px) {
                    display: initial;
                }

                .vgs__gallery__title {
                    display: none;
                }

                .vgs__gallery__container {
                    width: auto;

                    .vgs__gallery__container__img {
                        border-radius: 0;
                        object-fit: contain;
                        margin-right: 24px;
                    }
                }
            }

            .vgs__close {
                grid-row: 1 / 1;
                grid-column: 3 / 3;
                color: var(--v-primary-base);
                position: relative;
                font-size: 36px;
                justify-self: end;
            }

            .vgs__prev {
                grid-row: 2 / 2;
                grid-column: 1 / 1;
                color: var(--v-primary-base);
                left: initial;
                padding-left: 50px;
                font-size: 48px;
                width: unset;
            }

            .vgs__next {
                grid-row: 2 / 2;
                grid-column: 3 / 3;
                color: var(--v-primary-base);
                position: relative;
                padding-right: 50px;
                justify-self: end;
                font-size: 48px;
                width: unset;
            }
        }
    }

</style>
