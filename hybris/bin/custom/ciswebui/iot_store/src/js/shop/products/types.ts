export enum FacetGroup {
    LICENSE_TYPE = "licenseTypes",
    COUNTRY = "countries",
    PRIVATE_APPS = "privateApps",
    USE_CASES = "useCases",
    INDUSTRIES = "industries",
    COMPANY_UID = "companyUid",
    HARDWARE_REQUIREMENTS = 'hardwareRequirements',
    PACKAGES = 'packages',
    LICENSES = 'licenses',
    LICENSE_RUNTIMES = 'licenseRuntimes',
    FIRST_LEVEL_CATEGORIES = 'firstLevelCategories',
    VEHICLE_TYPES = 'vehicleType'
}

export enum SortingOption {
    RELEVANCE,
    PUBLISH_DATE,
    ACQUISITION_COUNT
}
