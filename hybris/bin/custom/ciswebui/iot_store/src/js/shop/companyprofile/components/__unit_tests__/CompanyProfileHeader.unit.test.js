import CompanyProfileHeader from 'shop/companyprofile/components/CompanyProfileHeader';
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';

describe('CompanyProfileHeader', () => {

    it('displays company logo when provided', () => {
        const logoUrl = 'https://link-to-logo.com';
        const wrapper = wrapperComponentFactory(CompanyProfileHeader, {
            props: {
                logoUrl: logoUrl
            },
            global: {
                stubs: {
                    LogoPlaceHolder: {
                        template: `<img src='placeholder.svg' />`
                    }
                }
            }
        });
        expect(wrapper.vm.hasLogo).toBeTruthy();
        const logo = wrapper.find('[data-id="image-company-logo"]');
        expect(logo.exists()).toBeTruthy();
        expect(logo.attributes('src')).toEqual(logoUrl);
    });

    it('displays placeholder when no logo url provided', () => {
        const wrapper = wrapperComponentFactory(CompanyProfileHeader, {
            props: {
                logoUrl: ''
            }
        });
        expect(wrapper.vm.hasLogo).toBeFalsy();
        expect(wrapper.find('[data-id="image-company-logo"]').exists()).toBeFalsy();
        expect(wrapper.find('[data-id="image-company-logo-placeholder"]').exists()).toBeTruthy();
    });
});
