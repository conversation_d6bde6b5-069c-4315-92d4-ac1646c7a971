<template>
    <div class="root">
        <v-app>
            <div ref="mainwrap">
                <main>
                    <store-header class="header"
                                  v-bind:cameras-url="coreData.camerasUrl"
                                  v-bind:my-apps-url="coreData.myAppsUrl"
                                  v-bind:base-url="home"
                                  v-bind:user-name="coreData.userName"
                                  v-bind:number-of-cart-items="coreData.numberOfCartItems">
                    </store-header>

                    <div class="content-wrapper">
                        <div class="main-message">
                            <v-container>
                                <v-row>
                                    <v-col cols="12">
                                        <alert-display></alert-display>
                                    </v-col>
                                </v-row>
                            </v-container>
                        </div>
                        <div class="main-container">
                            <router-view v-if="coreData.httpStatus === 200"></router-view>
                            <slot></slot>
                        </div>

                        <help-slide-in></help-slide-in>

                        <CDCookieBanner data-id="cookie-banner"></CDCookieBanner>

                        <div class="footer-container">
                            <page-footer v-bind:footer-data="coreData.navigationItems"></page-footer>
                        </div>
                    </div>
                </main>
            </div>
            <portal-target name="portal-store" v-on:change="blurBackground"></portal-target>
        </v-app>
    </div>
</template>

<script lang="ts">
    import { Component, Vue } from 'vue-property-decorator';
    import StoreHeader from 'shop/core/components/StoreHeader.vue';
    import PageFooter from 'common/components/PageFooter.vue';
    import { ShopCoreData , GalleryItemData} from 'common/generated-types/types';
    import { messageService, userPermissionService } from 'common/services';
    import AlertDisplay from 'common/components/AlertDisplay.vue';
    import { StoreDataProvider } from 'shop/resources';
    import {ShopRoute} from 'common/constants';
    import { HelpSlideIn } from 'shop/core/components/helpslidein';

    @Component({
        components: {
            HelpSlideIn,
            StoreHeader,
            PageFooter,
            AlertDisplay
        }
    })
    export default class StoreRoot extends Vue {
        coreData = {} as ShopCoreData;
        galleryItems = {} as GalleryItemData[];
        userPermissionService = userPermissionService;

        readonly home = ShopRoute.HOME + '/';

        $refs!:{
            mainwrap: HTMLElement;
        };

        created(): void {
            const mergedFrontendData = StoreDataProvider.getMergedFrontendData();
            this.coreData = mergedFrontendData.coreData as ShopCoreData;
            this.galleryItems = mergedFrontendData.galleryItems;
        }

        mounted(): void {
            if (this.coreData.globalMessages.messages) {
                this.coreData.globalMessages.messages
                    .forEach(message => messageService.message(message, this.$i18n));
            }
        }

        blurBackground(hasContent: boolean): void {
          this.$nextTick(() => {
            if (hasContent) {
              this.$refs.mainwrap.style.filter = 'blur(3px)';
            } else {
              this.$refs.mainwrap.style.filter = 'blur(0)';
            }
          });
        }
    }

</script>

<style lang="scss">
#vue-app {
  min-height: 100%;
  height: 100%;
  position: relative;
}
</style>

<style scoped lang="scss">
@import "shop/core/constants";

.root {
  position: relative;
  min-height: 100%;

  main {
    width: 100%;
    height: 100%;
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: auto;

    .header {
      width: 100%;
      z-index: 100;
    }

    .footer-container {
      flex-grow: 0;
      flex-shrink: 0;
      flex-basis: auto;
    }

    &.fixed {
      .header {
        position: fixed;
      }

      .content-wrapper {
        margin-top: $header-height;
      }
    }
  }

  .content-wrapper {
    padding-top: $header-height;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;

    .main-message {
      position: absolute;
      width: 100%;
      z-index: 1;
    }

    .main-container {
      flex: 1;
    }
  }
}

.root :deep(*) {
  box-sizing: border-box;
}
</style>
