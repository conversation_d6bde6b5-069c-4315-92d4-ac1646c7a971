import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from 'common/testtools/inlineTestPageCreator';
import {abortRequest, respondRequest} from 'common/testtools/testRequests';
import {testMap} from 'common/testtools/scenariosstore';
import toolsData from 'common/testtools/scenariosstore/toolsData';

describe('tools page', () => {
    const REST_URL = '/shop/api/tools';
    const toolsRestHandler = respondRequest(REST_URL, {
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(toolsData)
    });

    beforeEach(async () => {
        await page.setRequestInterception(true);
    });

    it('looks as expected', async () => {
        page.on('request', toolsRestHandler);

        await createInlineUiTestPage(testMap.Tools, [{url: REST_URL, data: toolsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: 1200, height: 1809});
        await page.waitFor(3000);
        expect(await page.screenshot()).toMatchImageSnapshot('extra large size');

        await page.setViewport({width: 992, height: 1906});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot('large size');

        await page.setViewport({width: 720, height: 2514});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot('medium size');

        await page.setViewport({width: 576, height: 2495});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot('small size');
    });

    it('shows message for error response', async () => {
        page.on('request', respondRequest(REST_URL, {
            status: 404,
            contentType: 'application/json',
            body: JSON.stringify({userMessages:[{level: 'ERROR', code:'product.notFound'}]})
        }));

        await createInlineUiTestPage(testMap.Tools, [{url: REST_URL, data: toolsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('shows message for error response without error details', async () => {
        page.on('request', respondRequest(REST_URL, {
                status: 500,
                contentType: 'application/json',
                body: JSON.stringify({})
            })
        );

        await createInlineUiTestPage(testMap.Tools, [{url: REST_URL, data: toolsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('shows message for aborted request', async () => {
        page.on('request', abortRequest(REST_URL));

        await createInlineUiTestPage(testMap.Tools, [{url: REST_URL, data: toolsData}]);
        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });
});
