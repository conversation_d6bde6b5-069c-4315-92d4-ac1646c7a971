<template>
    <div>
        <v-container fluid>
            <v-row>
                <v-col cols="12" md="2">
                    <v-img v-if="hasLogo"
                           :src="logoUrl"
                           class="rounded-lg"
                           data-id="image-company-logo"
                           max-height="80"
                           max-width="80"></v-img>
                    <div v-else
                         data-id="image-company-logo-placeholder">
                        <logo-place-holder>
                        </logo-place-holder>
                    </div>
                </v-col>
                <v-col cols="12" md="10" class="mt-8 mt-md-0">
                    <p class="text-h3" data-id="text-company-name">{{ companyName }}</p>
                    <p class="text-body-1" data-id="text-tag-line">{{ headerData.tagLine }}</p>
                </v-col>
            </v-row>
            <v-row class="mt-10">
                <v-col v-if="showFoundedIn" cols="12" data-id="container-company-founded" lg="3">
                    <div>
                        <p class="text--disabled">
                            {{$t('shop.productDetails.companyProfileSection.companyInfo.founded')}}
                        </p>
                        <p class="font-weight-bold" data-id="text-company-founded-in">{{ headerData.foundedIn }}</p>
                    </div>
                </v-col>
                <v-col v-if="showCompanySize" cols="12" data-id="container-company-size" lg="3">
                    <div>
                        <p class="text--disabled">
                            {{$t('shop.productDetails.companyProfileSection.companyInfo.size')}}
                        </p>
                        <p class="font-weight-bold" data-id="text-company-size">{{ headerData.companySize.value }}</p>
                    </div>
                </v-col>
                <v-col cols="12" lg="3">
                    <div>
                        <p class="text--disabled">
                            {{$t('shop.productDetails.companyProfileSection.companyInfo.headquarters')}}
                        </p>
                        <p class="font-weight-bold">{{ headquarters }}</p>
                    </div>
                </v-col>
            </v-row>
            <v-row v-if="profileUrl">
                <v-col>
                    <div class="mb-4">
                        <v-btn :href="profileUrl"
                            class="my-2" data-id="button-profile-url"
                        >{{$t('shop.productDetails.companyProfileSection.companyInfo.visitCompanyProfile')}}
                        </v-btn>
                    </div>
                </v-col>
            </v-row>
        </v-container>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {HeaderData} from 'common/generated-types/types';
    import {isEmpty} from 'lodash';

    @Component({
        components: {}
    })
    export default class CompanyInfo extends Vue {
        @Prop() headerData!: HeaderData;
        @Prop() companyName!: string;
        @Prop() profileUrl!: string;

        get showFoundedIn(): boolean {
            return this.headerData!.showFounded && Number.isInteger(this.headerData!.foundedIn);
        }

        get showCompanySize(): boolean {
            return this.headerData!.showCompanySize && !isEmpty(this.headerData!.companySize);
        }

        get headquarters(): string {
            const city: string = this.headerData!.city;
            const country: string = this.headerData!.country;
            return [city, country].filter(Boolean).join(', ');
        }

        get hasLogo(): boolean {
            return !isEmpty(this.logoUrl);
        }

        get logoUrl(): string {
            return this.headerData!.logoUrl;
        }
    }
</script>
