<template>
  <div class="app-integration-container my-4 my-xl-8 pt-4">
    <v-row no-gutters class="app-integration-labels">
      <v-col cols="12" xl="4" v-for="appIntegration in appIntegrationList" :key="appIntegration.displayName">
        <p data-id="text-app-integration">
          <CDIcon class="mr-1" icon="$gateway" color="black" v-if=" appIntegration.iconType === 'GATEWAY'"></CDIcon>
          <CDIcon class="mr-1" icon="$certificate" color="black" v-else-if="appIntegration.iconType === 'STANDARD'"></CDIcon>
          <CDIcon class="mr-1" icon="$doublesquares" color="black" v-else></CDIcon>
          {{ appIntegration.displayName }}
        </p>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AppIntegrationType, PublicAppIntegrationData, StandardAppIntegrationType} from 'common/generated-types/types';
import {LocaleMessages} from 'vue-i18n';

interface AppIntegration {
  displayName: string | LocaleMessages;
  iconType: StandardAppIntegrationType | null;
}

@Component
export default class AppIntegrationLabels extends Vue {
  @Prop() appIntegrations!: PublicAppIntegrationData[];

  appIntegrationList: AppIntegration[] = [];

  mounted(): void {
    this.appIntegrationList = this.appIntegrations
        .filter((i: PublicAppIntegrationData) => i.type === AppIntegrationType.STANDARD)
        .map((i: PublicAppIntegrationData) => ({displayName: i.displayName, iconType: i.integrationType}));

    if (this.appIntegrations.some((i: PublicAppIntegrationData) => i.type === AppIntegrationType.CUSTOM)) {
      this.appIntegrationList.push({displayName: this.$t('shop.productDetails.integrationsSection.customIntegrationName'), iconType: null});
    }
  }

}
</script>

<style lang="scss" scoped>
  .app-integration-container {
    border-top: 1px solid var(--v-grey-lighten1);
  }
</style>
