<template>
    <div class="export-information-overview">
        <h1>
            {{$t('shop.exportInformation.title')}}
        </h1>
        <div class="item-list">
            <div class="item no-hover header">
                <div>{{$t('shop.exportInformation.appName')}}</div>
                <div>{{$t('shop.exportInformation.companyName')}}</div>
                <div>{{$t('shop.exportInformation.eccn')}}</div>
            </div>

            <a class="item black--text"
               v-for="app in pageData.products"
               v-bind:href="app.link">
                <div>{{app.appName}}</div>
                <div>{{app.companyName}}</div>
                <div>{{app.eccn}}</div>
            </a>
        </div>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import {ExportInformationOverviewData} from 'common/generated-types/types';

    @Component
    export default class ExportInformationOverview extends Vue {
        @Prop() pageData!: ExportInformationOverviewData;

        created(): void {
            this.setPageTitle();
        }

        private setPageTitle(): void {
            document.title = this.$t('shop.exportInformation.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
        }
    };
</script>

<style scoped lang="scss">
    @import "common/design";
    @import "common/item-list";

    h1 {
        margin: $spacing-xl 0 0;
    }

    .export-information-overview {
        max-width: 1200px;
        margin: auto;
    }

    .item-list {
        margin-top: $spacing-m;
    }

    .item {
        grid-template-columns: repeat(3, 1fr);
        padding-left: 70px;
        padding-right: 70px;
    }
</style>
