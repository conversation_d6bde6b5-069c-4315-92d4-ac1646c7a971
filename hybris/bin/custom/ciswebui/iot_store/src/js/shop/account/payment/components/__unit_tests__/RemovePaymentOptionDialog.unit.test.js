import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import RemovePaymentOptionDialog from 'shop/account/payment/components/RemovePaymentOptionDialog.vue'
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import {CDButton} from "cd-system";

jest.mock('common/services/i18nService');
jest.mock('common/util');


const mountRemovePaymentOptionDialog = (props) => wrapperComponentFactory(RemovePaymentOptionDialog, {
    props,
    mocks: {
        $t: (msg) => msg,
        $vuetify: {
            breakpoint: {
                smAndDown: false
            }
        }
    },
    stubs: [CDButton]
});

describe('RemovePaymentOptionDialog.vue', () => {
    let wrapper;

    afterEach(() => {
        wrapper.destroy();
    });
    it('renders correctly when the dialog is open', () => {
        wrapper = mountRemovePaymentOptionDialog({isOpen: true});

        expect(wrapper.find('v-dialog-stub').exists()).toBe(true);
        expect(wrapper.find('[data-id="container-remove-payment-option-dialog"]').exists()).toBe(true);
        expect(wrapper.find('[data-id="text-remove-payment-dialog-header"]').text()).toBe('shop.payment.removePaymentOption');
        expect(wrapper.find('[data-id="text-remove-pa"]').text()).toBe('shop.payment.removePaymentOptionInfo');
    });

    it('emits "confirm" event when the remove button is clicked', async () => {
        wrapper = mountRemovePaymentOptionDialog({isOpen: true});

        const removeButton = wrapper.find('[data-id="remove-payment-option-dialog-footer-remove"]');
        expect(removeButton.exists()).toBe(true);
        wrapper.vm.onConfirm(); // Directly call the method
        expect(wrapper.emitted('confirm')).toBeTruthy();
    });

    it('applies fullscreen mode on small devices', async () => {
        wrapper = mountRemovePaymentOptionDialog({isOpen: true});

        wrapper.vm.$vuetify.breakpoint.smAndDown = true;
        await wrapper.vm.$nextTick();

        expect(wrapper.find('v-dialog-stub').attributes('fullscreen')).toBe('true');
    });

    it('does not apply fullscreen mode on large devices', () => {
        wrapper = mountRemovePaymentOptionDialog({isOpen: true});

        expect(wrapper.find('v-dialog-stub').attributes('fullscreen')).toBeUndefined();
    });
});
