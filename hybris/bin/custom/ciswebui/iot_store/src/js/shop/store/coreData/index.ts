import {coreDataResource} from 'shop/resources';
import {defineStore} from 'pinia';
import {ref} from 'vue';
import type {ApiCoreData} from 'common/generated-types/types';

export const useCoreDataStore = defineStore('coreDate', () => {
    const coreData = ref({} as ApiCoreData);

    async function fetchCoreData(): Promise<void> {
        coreDataResource.getCoreData()
            .then(response => coreData.value = response?.data)
            .catch(err => console.error(err));
    }

    return {coreData, fetchCoreData};
});
