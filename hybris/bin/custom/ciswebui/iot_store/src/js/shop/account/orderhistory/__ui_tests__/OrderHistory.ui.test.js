import {acceptCookieBanner, resetMouseCoords, createInlineUiTestPage} from "common/testtools/inlineTestPageCreator";
import {testMap} from 'common/testtools/scenariosstore';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cloneDeep} from 'lodash';

describe("order history page", () => {
    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.OrderHistory);
    });

    it("with orders", async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();
        await testForScreenSize(535, 3000, "small size");
        await testForScreenSize(720, 3000, "medium size");
        await testForScreenSize(992, 1050, "large size");
        await testForScreenSize(1300, 1050, "extra large size");

        await page.hover(".v-data-table__expanded__row");
        expect(await page.screenshot()).toMatchImageSnapshot("when hovering over an order");

        await page.hover(".link-number a");
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("when hovering over a page number");
    });

    it("with orders next page", async () => {
        data.pageData.pagination.hasPrevious = true;
        data.pageData.pagination.currentPage = 1;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();
        await testForScreenSize(535, 3000, "small size");
        await testForScreenSize(720, 3000, "medium size");
        await testForScreenSize(992, 1050, "large size");
        await testForScreenSize(1300, 1050, "extra large size");

        await page.hover(".v-data-table__expanded__row");
        expect(await page.screenshot()).toMatchImageSnapshot("when hovering over an order next page");
    });

    it("with order in overdue and pending state", async () => {
        await page.setViewport({width: 1200, height: 1050});
        data.pageData.results[0].statusDisplay = 'overdue';
        data.pageData.results[0].statusDisplay = 'pending';
        await createInlineUiTestPage(data);

        await acceptCookieBanner();
        await resetMouseCoords();

        await page.waitFor(2000);
        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it("without orders", async () => {
        data.pageData.results = [];
        data.pageData.pagination.totalNumberOfResults = 0;

        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await Sizes.testLargeScreensize(1050);
    });

    const testForScreenSize = async function (width, height, snapShotIdentifier) {
        await page.setViewport({width: width, height: height});
        await page.waitFor(800);
        expect(await page.screenshot({
            clip: {
                x: 0,
                y: 0,
                width: width,
                height: height
            }
        })).toMatchImageSnapshot(snapShotIdentifier);
    };

});
