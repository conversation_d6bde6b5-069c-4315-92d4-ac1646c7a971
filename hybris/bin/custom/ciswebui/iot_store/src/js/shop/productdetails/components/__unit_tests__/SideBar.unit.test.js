import {shallowMount} from '@vue/test-utils';
import SideBar from '../SideBar.vue';
import {testMap} from 'common/testtools/scenariosstore';

import 'common/test-directive';

jest.mock('common/util');

const mountSideBar = (options) => {
    return shallowMount(SideBar, {
        mocks: {
            $t: key => key,
            $te: key => key,
            $n: key => key
        },
        stubs: ['CDChip'],
        propsData: options.pageData
    });
};


describe('SideBar', () => {
    let data;
    beforeEach(() => {
        data = Object.assign({}, testMap.SideBar);

        window.frontendData = {
            coreData: {}
        };
    });

    it('sort countries in alphabetical order', () => {
        const wrapper = mountSideBar(data);

        const expectedCountriesSorted = 'Austria, Belgium, Germany, Ireland, Luxembourg, Netherlands, Poland, United Kingdom';


        expect(wrapper.vm.countryList.length).toBeGreaterThan(0);
        expect(wrapper.vm.countryList).toBe(expectedCountriesSorted);
    });

    it('has appendix false', () => {
        const wrapper = mountSideBar(data);
        expect(wrapper.vm.hasAppendix).toBeFalsy();
        expect(wrapper.vm.appendixUrl).toEqual('');
    });

    it('has appendix true', () => {
        const appendixUrl = 'some appendix url';
        data.pageData.appContactInfo.eula.type = 'STANDARD';
        data.pageData.appContactInfo.eula.standardEulaAppendix = {
            'displayName': 'Test document',
            'url': appendixUrl,
            'size': 249136,
            'code': 'pc_00001004_documentation-files_6f464b36-f7e5-4aa6-8db3-37e9533aca28'
        };
        const wrapper = mountSideBar(data);
        expect(wrapper.vm.hasAppendix).toBeTruthy();
        expect(wrapper.vm.appendixUrl).toEqual(appendixUrl);
    });

    it('terms of use url custom', () => {
        data.pageData.appContactInfo.eula.type = 'CUSTOM';
        const wrapper = mountSideBar(data);
        expect(wrapper.vm.eulaUrl).toEqual(data.pageData.appContactInfo.eula.customUrl);
        expect(wrapper.vm.termsType).toEqual('CUSTOM');
    });

    it('terms of use url standard', () => {
        data.pageData.appContactInfo.eula.type = 'STANDARD';
        const wrapper = mountSideBar(data);
        expect(wrapper.vm.eulaUrl).toEqual(wrapper.vm.STANDARD_EULA_URL);
        expect(wrapper.vm.termsType).toEqual('STANDARD');
    });

    it('does not display country availability when app is private', () => {
        data.pageData.isPrivateApp = true;
        const wrapper = mountSideBar(data);
        expect(wrapper.vm.displayCountryAvailability).toBeFalsy();
    });

    it('displays country availability when app is not private', () => {
        data.pageData.isPrivateApp = false;
        const wrapper = mountSideBar(data);
        expect(wrapper.vm.displayCountryAvailability).toBeTruthy();
    });
});
