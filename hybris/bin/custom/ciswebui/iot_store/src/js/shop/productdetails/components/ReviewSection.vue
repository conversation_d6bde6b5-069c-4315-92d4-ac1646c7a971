<template>
    <div class="review-section">
        <v-container :fluid="true" class="pa-0">
            <v-container :fluid="$vuetify.breakpoint.lgAndDown">
                <v-row>
                    <v-col cols="12">
                        <div class="review-headr">
                            <div class="separator"></div>
                            <h2 class="label extra-label">
                                {{$t('shop.productDetails.reviewSection.title')}}
                            </h2>
                        </div>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" lg="8">
                        <div class="review-list">
                            <review-creation-form v-if="reviewForm"
                                                  v-bind:user-name="userName"
                                                  v-bind:post-url="postReviewUrl"
                                                  v-on:new-review="addReviewItem">
                            </review-creation-form>

                            <div v-if="reviewsData.length === 0">
                                <p>{{$t('shop.productDetails.reviewSection.notReviewed')}}</p>
                            </div>

                            <div v-else>
                                <list-transition>
                                    <review-item
                                        v-for="review in reviewsData"
                                        v-bind:key="review.id"
                                        v-bind:review="review">
                                    </review-item>
                                </list-transition>
                            </div>
                        </div>
                    </v-col>
                    <v-col cols="12" lg="3" offset-lg="1">
                        <CDBlock v-if="!isAlreadyReviewed" class="review-sidebar">
                            <div v-if="isFormEnabled">
                                <h2 class="mb-2">{{ $t('shop.productDetails.reviewSection.addReview') }}</h2>
                                <p>{{ $t('shop.productDetails.reviewSection.shareReviewTeaser') }}</p>
                            </div>
                            <div v-else-if="notBoughtApp">
                                <h2 class="mb-2">{{ $t('shop.productDetails.reviewSection.wantToAddReview') }}</h2>
                                <p>{{ $t('shop.productDetails.reviewSection.buyApp') }}</p>
                            </div>
                            <CDButton color="primary" :disabled="!isFormEnabled" @click="showReviewForm" class="form-button">
                                {{ $t('shop.productDetails.reviewSection.addReview') }}
                            </CDButton>
                        </CDBlock>
                    </v-col>
                </v-row>
            </v-container>
        </v-container>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import ReviewCreationForm from 'shop/productdetails/components/ReviewCreationForm.vue';
    import ReviewItem from 'shop/productdetails/components/ReviewItem.vue';
    import { ListTransition } from 'common/components';
    import {commercefacades, ReviewStatus} from 'common/generated-types/types';
    import ReviewData = commercefacades.ReviewData;

    @Component({
        components: {
            ListTransition,
            ReviewItem,
            ReviewCreationForm
        }
    })
    export default class ReviewSection extends Vue {
        @Prop() reviews!: ReviewData[];
        @Prop() postReviewUrl!: string;
        @Prop() reviewStatus!: ReviewStatus;
        @Prop() userName!: string;

        reviewsData = Object.assign([], this.reviews);
        reviewStatusData =  this.reviewStatus;

        reviewForm = false;

        get isFormEnabled(): boolean {
            return this.reviewStatusData === ReviewStatus.READY_FOR_REVIEW;
        }

        get isAlreadyReviewed(): boolean {
            return this.reviewStatusData === ReviewStatus.REVIEW_IS_ALREADY_DONE;
        }

        get notBoughtApp(): boolean {
            return this.reviewStatusData === ReviewStatus.NOT_BOUGHT_APP;
        }

        addReviewItem(reviewItem: ReviewData): void {
            this.reviewsData.unshift(reviewItem);
            this.reviewStatusData = ReviewStatus.REVIEW_IS_ALREADY_DONE;
        }

        showReviewForm(): void {
            this.reviewForm = true;
        }
    }
</script>
