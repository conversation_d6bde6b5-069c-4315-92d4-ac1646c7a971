import {shallowMount} from '@vue/test-utils';
import ToolEnabledPopup from 'shop/productdetails/components/ToolEnabledPopup';

jest.mock('common/util');

const productName = "Test Tool";
const camerasUrl = "https://devices.dev.securityandsafetydev.com";
const supportUrl = "https://support.securityandsafetythings.com/hc";
const propsData = {
    show : false,
    productName: productName,
};

const createWrapper = (props) => {
    window.frontendData = {
        coreData: {
            navigationItems: [
                {
                    "id"  : "globalSupport",
                    "itemCode"  : "globalSupport",
                    "url" : supportUrl
                }
            ],
            camerasUrl: camerasUrl
        }
    };
    return shallowMount(ToolEnabledPopup, {
        mocks: {
            $t: (word) => {
                return word;
            }
        },
        stubs: {
            'portal': true
        },
        propsData: props
    });
};



describe('Tool Enabled Popup', () => {
    it('When show prop is changed to true -> popup becomes visible.', async () => {
        const wrapper = createWrapper(propsData);
        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(false);
        wrapper.vm.show = true;
        await wrapper.vm.$nextTick();
        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(true);
    });

    it('When show prop is changed to true-> popup remains not visible.', async () => {
        const wrapper = createWrapper(propsData);
        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(false);
        wrapper.vm.show = false;
        await wrapper.vm.$nextTick();
        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(false);
    });

    it('When mounted -> supportUrl is populated.', async () => {
        const wrapper = createWrapper(propsData);
        expect(wrapper.vm.supportUrl).toBe(supportUrl);
    });

    it('When mounted -> dmpUrl is populated.', async () => {
        const wrapper = createWrapper(propsData);
        expect(wrapper.vm.dmpUrl).toBe(camerasUrl);
    });
});