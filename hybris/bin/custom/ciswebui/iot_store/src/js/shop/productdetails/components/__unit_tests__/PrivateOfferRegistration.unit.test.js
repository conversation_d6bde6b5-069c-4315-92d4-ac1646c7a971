import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {mount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import PrivateOfferRegistration from 'shop/productdetails/components/PrivateOfferRegistration';
import 'common/test-directive';
import {cloneDeep} from 'lodash';
import {i18n} from 'common/i18n';

Vue.use(VueI18n);

const offerRegistrationTemplate = {
    projectName: '',
    customerName: '',
    plannedStartDate: '',
    siteAddress: {},
};

const draftPropsData = {
    offerRegistration: offerRegistrationTemplate,
    showRegisterProject: true
};

const mountComponent = (props) => mount(PrivateOfferRegistration, {
    mocks: {
        $t: (word) => {
            return word;
        },
    },
    i18n,
    propsData: {
        value: props.offerRegistration,
        showRegisterProject: props.showRegisterProject
    }
});


describe('Private Offer Registration', () => {
    let propsData = {};
    beforeEach(() => {
        propsData = cloneDeep(draftPropsData);
    });

    it('is displayed when registerProject prop set to true', async () => {
        const wrapper = mountComponent(propsData);
        await wrapper.vm.$nextTick();

        expect(wrapper.find('[data-id="container-project-registration"]').exists()).toBeTruthy();
    });

    it('is not displayed when show prop is set to false', async () => {
        propsData.showRegisterProject = false;
        const wrapper = mountComponent(propsData);
        await wrapper.vm.$nextTick();

        expect(wrapper.find('[data-id="container-project-registration"]').exists()).toBeFalsy();
    });

});
