<template>
    <div class="info-wrapper">
        <div class="logo">
            <img v-bind:src="`${product.logoUrl}`" alt=""/>
        </div>
        <div class="info-section">
            <p class="text-h6">{{product.name}}</p>
            <p><span class="secondary-color">{{$t('shop.productDetails.productHeader.byCompany')}}</span>
                <span class="primary-color">{{product.company.friendlyName || product.company.name}}</span></p>
        </div>
    </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DetailProductData} from 'common/generated-types/types';

@Component({
    components: {}
})
export default class PrivateOfferLogo extends Vue {
    @Prop() product!: DetailProductData;


}
</script>

<style scoped lang="scss">
@import "common/design";
.logo {
    width: 58px;
    height: 58px;
    overflow: hidden;
    border-radius: 10px;
    background-repeat: no-repeat;
    float: left;

    img {
        width: 100%;
        height: 100%;
    }
}

.info-section {
    float: left;
    padding-left: 15px;
    padding-top: 6px;
}
</style>
