import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import { DocumentsSection } from 'shop/productdetails/components';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    data: {
        documentsPresent: true,
        pdfDataList: [
            {
                code: 'code123',
                displayName: 'i like hotdogs',
                size: 69,
                url: 'url123'
            },
            {
                code: 'code456',
                displayName: 'i like chicken soup',
                size: 1440,
                url: 'url456'
            },
            {
                code: 'code789',
                displayName: "this is a super long title and it's purpose is to check how the title of a pdf document breaks over multiple lines it will probably be ok but like not forever... this is a super long title and it's purpose is to check how the title of a pdf document breaks over multiple lines it will probably be ok but like not forever... this is a super long title and it's purpose is to check how the title of a pdf document breaks over multiple lines it will probably be ok but like not forever... ",
                size: 20000,
                url: 'urlMoreTest123'
            }
        ]
    },
    components: {
        TestPageRoot,
        DocumentsSection
    },
    template: `
      <test-page-root>
        <documents-section
            v-if="documentsPresent"
            v-bind:pdfDataList="pdfDataList">
        </documents-section>
      </test-page-root>
    `
});
