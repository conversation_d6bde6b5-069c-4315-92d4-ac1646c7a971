import { acceptCookieBanner, createInlineUiTestPage, resetMouseCoords } from 'common/testtools/inlineTestPageCreator'
import * as Sizes from 'common/testtools/testScreenSizes'
import { testMap } from 'common/testtools/scenariosstore'

describe('Help and Resources', () => {

  it('looks as expected', async () => {
    await createInlineUiTestPage(testMap.HelpAndResources)
    await acceptCookieBanner()
    await resetMouseCoords();

    await Sizes.testExtraSmallScreensize(2500)
    await Sizes.testSmallScreensize(2500)
    await Sizes.testMediumScreensize(2500)
    await Sizes.testLargeScreensize(2500)
    await Sizes.testExtraLargeScreensize(2500)
  })
})

