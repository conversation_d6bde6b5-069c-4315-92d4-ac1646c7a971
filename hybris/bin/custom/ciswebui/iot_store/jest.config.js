const themeArg = process.argv.filter((x) => x.startsWith('--theme='))[0];
const theme = themeArg ? themeArg.split('=')[1] : 'azena'; // default
const isAA = theme === 'aa';
const uiTestMatch = isAA ? ['**/*.ui.aa.test.js'] : ['**/*.ui.test.js', '!**/aa/**'];
const unitTestMatch = isAA ? ['**/*.unit.test.js', '**/*.unit.test.ts', '**/*.unit.aa.test.js', '**/*.unit.aa.test.ts'] : ['**/*.unit.test.js', '**/*.unit.test.ts', '!**/aa/**'];
const createTestEnv  = isAA ? 'puppeteer_environment_aa.js' : 'puppeteer_environment.js';
const setupTests = isAA ? 'setupAATests.js' : 'setupTests.js';
const uiSetup = isAA ? 'ui_setup_aa.js' : 'ui_setup.js';

const baseConfig = {
    'roots': ['./src/js'],
    'moduleDirectories': ['./src/js', 'node_modules', '<rootDir>'],
    'moduleNameMapper': {
        '\\.svg$': '<rootDir>/testing/svgTransform.js',
        'vuetify/lib(.*)': '<rootDir>/node_modules/vuetify/es5$1',
        'cd-system': '<rootDir>/node_modules/cd-system/index-cjs.js',
        '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/testing/__mocks__/fileMock.js',
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
        "^common/util$": "<rootDir>/testing/__mocks__/util.js"
    },
    'moduleFileExtensions': [
        'js',
        'json',
        'vue',
        'ts'
    ],
    'setupFilesAfterEnv': [
        `./testing/${setupTests}`
    ],
    'transform': {
        '^.+\\.svg$': '<rootDir>/testing/svgTransform.js',
        '^.+\\.js$': 'babel-jest',
        '.*\\.(vue)$': '@vue/vue2-jest',
        '^.+\\.ts?$': ['ts-jest', {
            'diagnostics': {
                'exclude': ['**/*.spec.ts', '**/*.test.ts']
            },
            'babelConfig': true
        }]
    }
};

module.exports = {
    'projects': [
        {
            ...baseConfig,
            'displayName': 'UNIT TEST',
            'testMatch': unitTestMatch,
            'testEnvironment': 'jsdom',
            'testEnvironmentOptions': {
                'customExportConditions': ['node', 'node-addons']
            }
        },
        {
            ...baseConfig,
            'displayName': 'UI TEST',
            'testMatch': uiTestMatch,
            'testEnvironment': `./testing/${createTestEnv}`,
        }
    ],
    'reporters': [
        'default',
        'jest-junit',
        '<rootDir>/testing/retryReporter.js'
    ],
    'globalSetup': `./testing/${uiSetup}`,
    'globalTeardown': './testing/ui_teardown.js',
    'verbose': true
};
