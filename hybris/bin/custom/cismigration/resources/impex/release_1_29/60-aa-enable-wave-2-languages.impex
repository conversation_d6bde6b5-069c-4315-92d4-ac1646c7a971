$contentCatalog = aaContentCatalog
$productCatalog = aav2ProductCatalog
$languages = fi, et, lt, lv, sv


INSERT_UPDATE Language; isocode[unique = true]; name[lang = en]; fallbackLanguages(isocode); active[default = true]
                      ; fi                    ; Finnish        ; en                        ;
                      ; et                    ; Estonian       ; en                        ;
                      ; lt                    ; Lithuanian     ; en                        ;
                      ; lv                    ; Latvian        ; en                        ;
                      ; sv                    ; Swedish        ; en                        ;

UPDATE Language; isocode[unique = true]; name[lang = fi]
               ; fi                    ; suomi
               ; sv                    ; ruotsi
               ; et                    ; viro
               ; lv                    ; latvia

UPDATE Language; isocode[unique = true]; name[lang = sv]
               ; fi                    ; finska
               ; sv                    ; svenska

UPDATE Language; isocode[unique = true]; name[lang = et]
               ; et                    ; eesti
               ; fi                    ; soome

UPDATE Language; isocode[unique = true]; name[lang = lv]
               ; lv                    ; latviesu
               ; fi                    ; somu

UPDATE Language; isocode[unique = true]; name[lang = lt]
               ; lt                    ; lietuviu

UPDATE Catalog; id[unique = true]; languages(isocode)[mode = merge]
              ; $productCatalog  ; $languages

UPDATE CatalogVersion; catalog(id)[unique = true]; version[unique = true]; languages(isocode)[mode = merge]
                     ; $productCatalog           ; Staged                ; $languages
                     ; $productCatalog           ; Online                ; $languages


UPDATE Catalog; id[unique = true]; languages(isocode)[mode = merge]
              ; $contentCatalog  ; $languages

UPDATE CatalogVersion; catalog(id)[unique = true]; version[unique = true]; languages(isocode)[mode = merge]
                     ; $contentCatalog           ; Staged                ; $languages
                     ; $contentCatalog           ; Online                ; $languages
