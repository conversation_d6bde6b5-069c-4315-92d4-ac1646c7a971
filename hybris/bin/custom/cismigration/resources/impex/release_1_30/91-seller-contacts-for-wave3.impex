#% impex.enableCodeExecution(true);
UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]

INSERT_UPDATE SellerContact; country(isocode)[unique = true]; name             ; email                ; phone
                           ; NO                             ; Salgs støtte     ; <EMAIL> ; +45 44 89 83 10
                           ; SE                             ; Försäljningsstöd ; <EMAIL> ; +45 44 89 83 10
                           ; DK                             ; Salgssupport     ; <EMAIL> ; +45 44 89 83 10
                           ; IS                             ; Salgssupport     ; <EMAIL> ; +45 44 89 83 10

#Dev
#% if: "dev".equals("$config-spring.profiles.active");
INSERT_UPDATE IotCompany; uid[unique = true]                   ; sellerContacts(country(isocode))
                        ; 713b8c34-f6be-4904-aefc-4bdd67453d87 ; NO
                        ; 40b7fc2d-cd0e-425b-8078-d3245d1c1f55 ; SE
                        ; b590385e-9141-4546-ae0f-5b2bfd8858a8 ; DK, IS
#% endif:

#Demo
#% if: "demo".equals("$config-spring.profiles.active");
                        ; 557c3410-acf0-4d73-8e92-a3a1d6abfad0 ; NO
                        ; 670bf066-8d87-474a-9ab3-7d14094d8455 ; SE
                        ; dee8bdda-3321-4853-bc09-32f85de66c2f ; DK, IS
#% endif:
