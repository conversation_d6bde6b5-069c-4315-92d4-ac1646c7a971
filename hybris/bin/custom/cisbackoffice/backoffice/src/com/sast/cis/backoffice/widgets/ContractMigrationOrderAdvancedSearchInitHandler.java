package com.sast.cis.backoffice.widgets;

import com.hybris.backoffice.navigation.NavigationNode;
import com.hybris.backoffice.widgets.advancedsearch.AdvancedSearchInitializer;
import com.hybris.backoffice.widgets.advancedsearch.impl.AdvancedSearchData;
import com.hybris.backoffice.widgets.advancedsearch.impl.SearchConditionData;
import com.hybris.cockpitng.core.config.impl.jaxb.hybris.advancedsearch.FieldType;
import com.hybris.cockpitng.search.data.ValueComparisonOperator;

import java.util.List;
import java.util.Optional;

import static java.util.Objects.nonNull;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

public class ContractMigrationOrderAdvancedSearchInitHandler implements AdvancedSearchInitializer {
    private static final String MIGRATION_ORDER = "migrationOrder";

    @Override
    public void addSearchDataConditions(final AdvancedSearchData searchData, final Optional<NavigationNode> navigationNode) {
        if (nonNull(searchData)) {
            final List<SearchConditionData> conditions = searchData.getConditions(MIGRATION_ORDER);
            if (isNotEmpty(conditions)) {
                conditions.clear();
            }
            final FieldType fieldType = new FieldType();
            fieldType.setDisabled(Boolean.FALSE);
            fieldType.setSelected(Boolean.TRUE);
            fieldType.setName(MIGRATION_ORDER);
            searchData.addCondition(fieldType, ValueComparisonOperator.EQUALS, Boolean.TRUE);
        }
    }

}
