package com.sast.cis.backoffice.jalo;

import com.sast.cis.backoffice.constants.CisbackofficeConstants;
import de.hybris.platform.jalo.JaloSession;
import de.hybris.platform.jalo.extension.ExtensionManager;
import org.apache.log4j.Logger;

public class CisbackofficeManager extends GeneratedCisbackofficeManager
{
	@SuppressWarnings("unused")
	private static final Logger log = Logger.getLogger( CisbackofficeManager.class.getName() );
	
	public static final CisbackofficeManager getInstance()
	{
		ExtensionManager em = JaloSession.getCurrentSession().getExtensionManager();
		return (CisbackofficeManager) em.getExtension(CisbackofficeConstants.EXTENSIONNAME);
	}
	
}
