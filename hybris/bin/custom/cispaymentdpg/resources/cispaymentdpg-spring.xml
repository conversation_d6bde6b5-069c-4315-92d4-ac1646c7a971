<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

		  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util https://www.springframework.org/schema/util/spring-util.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.sast.cis.payment.dpg"/>

    <bean id="sepaCollectionAccountGenerator" class="de.hybris.platform.servicelayer.keygenerator.impl.PersistentKeyGenerator">
        <property name="key" value="${dpg.collection.sepa.keygen.name}"/>
        <property name="digits" value="${dpg.collection.sepa.keygen.digits}"/>
        <property name="start" value="${dpg.collection.sepa.keygen.start}"/>
        <property name="type" value="${dpg.collection.sepa.keygen.type}"/>
        <property name="template" value="${dpg.collection.sepa.keygen.template}"/>
    </bean>

    <bean id="dpgAchTransferPaymentInstrumentConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.model.AchTransferPaymentInstrumentModel"/>
        <property name="populators">
            <list merge="true">
                <ref bean="achTransferPaymentInstrumentPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="dpgSepaTransferPaymentInstrumentConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.model.SepaTransferPaymentInstrumentModel"/>
        <property name="populators">
            <list merge="true">
                <ref bean="sepaTransferPaymentInstrumentPopulator"/>
            </list>
        </property>
    </bean>

    <util:map id="dpgCollectionAccountProviders" key-type="com.sast.cis.payment.dpg.enums.DpgCollectionAccountType"
              value-type="com.sast.cis.payment.dpg.collectionaccounts.CollectionAccountProvider">
        <entry value-ref="achPlatformAccountProvider">
            <key><value type="com.sast.cis.payment.dpg.enums.DpgCollectionAccountType">ACH_STATIC</value></key>
        </entry>

        <entry value-ref="sepaVirtualIbanProvider">
            <key><value type="com.sast.cis.payment.dpg.enums.DpgCollectionAccountType">SEPA_VIRTUAL</value></key>
        </entry>
    </util:map>

    <util:map id="dpgPaymentMethods" key-type="com.sast.cis.core.enums.PaymentMethodType"
              value-type="com.sast.cis.payment.dpg.methods.DpgPaymentMethod">
        <entry value-ref="dpgAchPaymentMethod">
            <key><value type="com.sast.cis.core.enums.PaymentMethodType">ACH_INTERNATIONAL</value></key>
        </entry>
        <entry value-ref="dpgSepaPaymentMethod">
            <key><value type="com.sast.cis.core.enums.PaymentMethodType">SEPA_CREDIT</value></key>
        </entry>
        <entry value-ref="dpgCreditCardPaymentMethod">
            <key><value type="com.sast.cis.core.enums.PaymentMethodType">CREDIT_CARD</value> </key>
        </entry>
    </util:map>

    <bean id="fetchSettlementJobPerformable" class="com.sast.cis.payment.dpg.job.FetchSettlementJobPerformable"
          parent="abstractJobPerformable"/>

    <bean id="fetchBalanceJobPerformable" class="com.sast.cis.payment.dpg.job.FetchBalanceJobPerformable"
          parent="abstractJobPerformable"/>

    <bean id="cancelAuthTransactionJobPerformable" class="com.sast.cis.payment.dpg.job.CancelAuthorizationTransactionJobPerformable"
          parent="abstractJobPerformable"/>

    <bean id="cancelPurchaseAndPaymentJobPerformable" class="com.sast.cis.payment.dpg.job.CancelPurchaseAndPaymentJobPerformable"
          parent="abstractJobPerformable"/>
</beans>
