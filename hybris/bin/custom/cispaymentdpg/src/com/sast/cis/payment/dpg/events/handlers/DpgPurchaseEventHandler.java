package com.sast.cis.payment.dpg.events.handlers;

import com.sast.cis.core.dao.PaymentTransactionDao;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.InvoiceModel;
import com.sast.cis.core.service.invoice.InvoiceService;
import com.sast.cis.payment.dpg.enums.DpgEntityStatus;
import com.sast.cis.payment.dpg.enums.DpgTransitionEntityType;
import com.sast.cis.payment.dpg.events.DpgEventHandler;
import com.sast.cis.payment.dpg.methods.cc.DpgCreditCardPaymentInfoService;
import com.sast.cis.payment.dpg.model.DpgCreditCardPaymentInfoModel;
import com.sast.cis.payment.dpg.model.DpgEventModel;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class DpgPurchaseEventHandler implements DpgEventHandler {
    private final PaymentTransactionDao paymentTransactionDao;
    private final InvoiceService invoiceService;
    private final DpgCreditCardPaymentInfoService dpgCreditCardPaymentInfoService;

    @Override
    public void handleEvent(@NonNull DpgEventModel storedEvent) {
        if (storedEvent.getEntityId() == null) {
            findMatchingAuthorizationTransaction(storedEvent.getDpgEntityId())
                .ifPresent(transactionModel -> handleMatchingAuthorizationTransactionEvent(transactionModel, storedEvent));
        } else {
            findMatchingInvoiceTransaction(storedEvent.getEntityId())
                .ifPresent(transactionModel -> handleMatchingInvoiceTransactionEvent(transactionModel, storedEvent));
        }
    }

    @Override
    public DpgTransitionEntityType getType() {
        return DpgTransitionEntityType.MARKETPLACEPURCHASE;
    }

    private void handleMatchingInvoiceTransactionEvent(PaymentTransactionModel paymentTransaction, DpgEventModel dpgEvent) {
        LOG.info("Handle event (eventId={}) for transaction (code={})", dpgEvent.getEventId(), paymentTransaction.getCode());
        if (DpgEntityStatus.COMPLETED.equals(dpgEvent.getEventStatus())) {
            LOG.info("Successful payment for invoice transaction {}, updating payment status.", paymentTransaction.getCode());
            final InvoiceModel invoice = invoiceService.findInvoiceByExternalIdOrThrow(paymentTransaction.getRequestToken());
            invoiceService.updateInvoiceStatusOnPaymentCompletion(invoice);
        } else {
            LOG.info("Nothing to do with purchase event {} for payment transaction {}", dpgEvent.getEventId(),
                paymentTransaction.getCode());
        }
    }

    private void handleMatchingAuthorizationTransactionEvent(PaymentTransactionModel paymentTransaction, DpgEventModel dpgEvent) {
        LOG.info("Handle event (eventId={}) for transaction (code={})", dpgEvent.getEventId(), paymentTransaction.getCode());
        if (DpgEntityStatus.COMPLETED.equals(dpgEvent.getEventStatus()) &&
            paymentTransaction.getInfo() instanceof DpgCreditCardPaymentInfoModel dpgCreditCardPaymentInfo) {
            LOG.info("Successful payment for authorization transaction {}, updating payment info status.", paymentTransaction.getCode());
            dpgCreditCardPaymentInfoService.updatePaymentInfoAfterCompletion(dpgCreditCardPaymentInfo);
        } else {
            LOG.info("Nothing to do with purchase event {} for payment transaction {}", dpgEvent.getEventId(),
                paymentTransaction.getCode());
        }
    }

    private Optional<PaymentTransactionModel> findMatchingInvoiceTransaction(String id) {
        return paymentTransactionDao.getTransactionByPaymentProviderAndRequestToken(PaymentProvider.DPG, id)
            .stream().filter(tx -> PaymentTransactionType.INVOICE.equals(tx.getType()))
            .findFirst();
    }

    private Optional<PaymentTransactionModel> findMatchingAuthorizationTransaction(String id) {
        if (id == null) {
            return Optional.empty();
        }
        return paymentTransactionDao.getTransactionByPaymentProviderAndRequestToken(PaymentProvider.DPG, id)
            .stream().filter(tx -> PaymentTransactionType.AUTHORIZATION.equals(tx.getType()))
            .findFirst();
    }
}
