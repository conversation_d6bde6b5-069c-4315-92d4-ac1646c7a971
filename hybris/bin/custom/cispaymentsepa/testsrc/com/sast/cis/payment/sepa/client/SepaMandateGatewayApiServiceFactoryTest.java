package com.sast.cis.payment.sepa.client;

import com.sast.cis.payment.adyen.client.authentication.filter.AuthenticationFilter;
import com.sast.cis.payment.sepa.client.api.SepaMandateApi;
import com.sast.cis.payment.sepa.client.configuration.SepaMandateClientJsonResolver;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.assertj.core.api.Assertions.assertThat;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class SepaMandateGatewayApiServiceFactoryTest {

    @Mock
    private AuthenticationFilter authenticationFilter;

    @Mock
    private SepaMandateClientJsonResolver sepaMandateClientJsonResolver;

    @InjectMocks
    private SepaMandateGatewayApiServiceFactory sepaMandateGatewayApiServiceFactory;

    private static final String TEST_SERVICE_URL = "http://localhost:8081";

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(sepaMandateGatewayApiServiceFactory, "sepaMandateServiceUrl", TEST_SERVICE_URL);
    }

    @Test
    public void testGetSepaMandateApi_ReturnsApiInstance() {
        // When
        SepaMandateApi result = sepaMandateGatewayApiServiceFactory.getSepaMandateApi();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isInstanceOf(SepaMandateApi.class);
    }

    @Test
    public void testServiceUrlConfiguration() {
        // Given
        String customUrl = "https://sepa-mandate-prod.example.com";
        ReflectionTestUtils.setField(sepaMandateGatewayApiServiceFactory, "sepaMandateServiceUrl", customUrl);

        // When/Then - Verify the service can be configured with different URLs
        assertThat(ReflectionTestUtils.getField(sepaMandateGatewayApiServiceFactory, "sepaMandateServiceUrl")).isEqualTo(customUrl);
    }

    @Test
    public void testFactoryUsesAuthenticationFilter() {
        // When
        SepaMandateApi result = sepaMandateGatewayApiServiceFactory.getSepaMandateApi();

        // Then
        // Verify that the factory creates an API instance (which means it successfully used the AuthenticationFilter)
        assertThat(result).isNotNull();
        // The AuthenticationFilter is injected and used in the JAX-RS client creation
        // Full integration testing would verify the actual authentication behavior
    }
}
