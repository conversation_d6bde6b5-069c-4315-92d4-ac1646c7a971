package com.sast.cis.payment.sepa.tools;

import com.sast.cis.payment.sepa.client.api.SepaMandateApiDto;
import com.sast.cis.payment.sepa.dto.SepaMandateDto;


public class SepaMandateConverter {

    public static SepaMandateDto toDto(SepaMandateApiDto apiDto) {
        if (apiDto == null) {
            return null;
        }

        return SepaMandateDto.builder()
                .mandateReference(apiDto.getMandateReference())
                .companyId(apiDto.getCompanyId())
                .accountHolderName(apiDto.getAccountHolderName())
                .iban(apiDto.getIban())
                .signatureDate(apiDto.getSignatureDate())
                .status(apiDto.getStatus())
                .useAsDefault(null) // Not available in API DTO
                .createdAt(null)    // Not available in API DTO
                .updatedAt(null)    // Not available in API DTO
                .build();
    }


    public static SepaMandateApiDto toApiDto(SepaMandateDto dto) {
        if (dto == null) {
            return null;
        }

        return SepaMandateApiDto.builder()
                .mandateReference(dto.getMandateReference())
                .companyId(dto.getCompanyId())
                .accountHolderName(dto.getAccountHolderName())
                .iban(dto.getIban())
                .signatureDate(dto.getSignatureDate())
                .status(dto.getStatus())
                .build();
    }


}