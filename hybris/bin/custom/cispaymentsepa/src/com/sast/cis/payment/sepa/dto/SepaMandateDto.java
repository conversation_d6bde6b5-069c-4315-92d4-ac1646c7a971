package com.sast.cis.payment.sepa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sast.cis.payment.sepa.client.api.MandateStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * Data Transfer Object for SEPA mandate operations.
 * This DTO represents the structure of SEPA mandate data exchanged with the sepa-mandate service.
 * Follows the same pattern as other payment DTOs in the system.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SepaMandateDto {

    /**
     * Unique mandate reference identifier
     */
    @JsonProperty("mandateReference")
    private String mandateReference;

    /**
     * Company ID associated with the mandate
     */
    @JsonProperty("companyId")
    private String companyId;

    /**
     * Account holder name
     */
    @JsonProperty("accountHolderName")
    private String accountHolderName;

    /**
     * IBAN (International Bank Account Number)
     */
    @JsonProperty("iban")
    private String iban;

    /**
     * Date when the mandate was signed
     */
    @JsonProperty("signatureDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate signatureDate;

    /**
     * Current status of the mandate (DRAFT, ACTIVE, DELETED)
     */
    @JsonProperty("status")
    private MandateStatus status;

    /**
     * Whether this mandate should be used as default
     */
    @JsonProperty("useAsDefault")
    private Boolean useAsDefault;

    /**
     * Timestamp when the mandate was created
     */
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private String createdAt;

    /**
     * Timestamp when the mandate was last updated
     */
    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private String updatedAt;


}
