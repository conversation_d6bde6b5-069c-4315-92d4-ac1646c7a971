package com.sast.cis.payment.sepa.service;

import com.sast.cis.payment.sepa.client.SepaMandateGatewayApiServiceFactory;
import com.sast.cis.payment.sepa.client.api.SepaMandateApi;
import com.sast.cis.payment.sepa.client.api.SepaMandateApiDto;
import com.sast.cis.payment.sepa.dto.SepaMandateDto;
import com.sast.cis.payment.sepa.tools.SepaMandateConverter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * Service for SEPA mandate operations.
 * This service acts as a bridge between the application and the sepa-mandate service,
 * using the SepaMandateGatewayApiServiceFactory to make authenticated REST calls.
 * Follows the same pattern as other payment services in the system.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SepaMandateService {

    private final SepaMandateGatewayApiServiceFactory sepaMandateGatewayApiServiceFactory;

    /**
     * Create a draft mandate for the given company ID.
     * If a mandate already exists for the companyId, return the existing mandate.
     */
    public SepaMandateDto createDraftMandate(String companyId) {
        log.info("Creating draft mandate for company: {}", companyId);

        try {
            SepaMandateApi api = sepaMandateGatewayApiServiceFactory.getSepaMandateApi();
            ResponseEntity<SepaMandateApiDto> response = api.createMandate(companyId);

            if (response.getStatusCodeValue() == 200 || response.getStatusCodeValue() == 201) {
                SepaMandateApiDto apiDto = response.getBody();
                SepaMandateDto mandate = SepaMandateConverter.toDto(apiDto);
                log.info("Successfully created draft mandate with reference: {} for company: {}",
                        mandate.getMandateReference(), companyId);
                return mandate;
            } else {
                log.error("Failed to create draft mandate for company: {}. Status: {}", companyId, response.getStatusCode());
                throw new RuntimeException("Failed to create draft mandate. Status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error creating draft mandate for company: {}", companyId, e);
            throw new RuntimeException("Failed to create draft mandate", e);
        }
    }

    /**
     * Finalize a draft mandate with the given reference and payload.
     * If the provided payload contains all required fields, then finalize the draft mandate.
     * Otherwise, update the draft mandate with additional input.
     */
    public SepaMandateDto finalizeDraftMandate(String reference, SepaMandateDto mandateDto) {
        log.info("Finalizing draft mandate with reference: {}", reference);

        try {
            SepaMandateApi api = sepaMandateGatewayApiServiceFactory.getSepaMandateApi();
            SepaMandateApiDto payload = SepaMandateConverter.toApiDto(mandateDto);
            ResponseEntity<SepaMandateApiDto> response = api.activateMandate(reference, payload);

            if (response.getStatusCodeValue() == 200) {
                SepaMandateApiDto apiDto = response.getBody();
                SepaMandateDto finalizedMandate = SepaMandateConverter.toDto(apiDto);
                log.info("Successfully finalized mandate with reference: {}", reference);
                return finalizedMandate;
            } else {
                log.error("Failed to finalize mandate with reference: {}. Status: {}", reference, response.getStatusCode());
                throw new RuntimeException("Failed to finalize mandate. Status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error finalizing mandate with reference: {}", reference, e);
            throw new RuntimeException("Failed to finalize mandate", e);
        }
    }

}
