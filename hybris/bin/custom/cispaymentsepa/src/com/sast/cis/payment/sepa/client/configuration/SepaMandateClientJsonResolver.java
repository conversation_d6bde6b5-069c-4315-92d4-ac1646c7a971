package com.sast.cis.payment.sepa.client.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.stereotype.Component;

import javax.ws.rs.ext.ContextResolver;
import javax.ws.rs.ext.Provider;

/**
 * JSON resolver for SEPA mandate client.
 * Configures Jackson ObjectMapper for proper JSON serialization/deserialization.
 * Follows the same pattern as Adyen's JSON resolver.
 */
@Provider
@Component
public class SepaMandateClientJsonResolver implements ContextResolver<ObjectMapper> {

    private final ObjectMapper objectMapper;

    public SepaMandateClientJsonResolver() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
    }

    @Override
    public ObjectMapper getContext(Class<?> type) {
        return objectMapper;
    }
}
