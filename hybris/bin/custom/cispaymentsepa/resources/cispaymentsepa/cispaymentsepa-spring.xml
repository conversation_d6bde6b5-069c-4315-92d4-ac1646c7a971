<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
            http://www.springframework.org/schema/beans/spring-beans.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.sast.cis.payment.sepa.service"/>

    <!-- Explicit bean definitions if component scanning doesn't work -->

    <!-- Authentication Filter Bean -->
    <bean id="authenticationFilter"
          class="com.sast.cis.payment.sepa.client.authentication.filter.AuthenticationFilter">
        <!-- Add constructor args or properties if needed -->
        <constructor-arg ref=""/>
    </bean>

    <bean id="accessTokenProvider"
          class="com.sast.cis.payment.sepa.client.authentication.AccessTokenProvider">
        <!-- Add constructor args or properties if needed -->
        <constructor-arg ref=""/>
    </bean>
    <!-- JSON Resolver Bean -->
    <bean id="sepaMandateClientJsonResolver"
          class="com.sast.cis.payment.sepa.client.configuration.SepaMandateClientJsonResolver">
        <!-- Add constructor args or properties if needed -->
    </bean>

    <!-- Factory Bean -->
    <bean id="sepaMandateGatewayApiServiceFactory"
          class="com.sast.cis.payment.sepa.client.SepaMandateGatewayApiServiceFactory">
        <constructor-arg ref="authenticationFilter"/>
        <constructor-arg ref="sepaMandateClientJsonResolver"/>
        <property name="sepaMandateServiceUrl" value="${sepa.mandate.service.url:http://localhost:8081}"/>
    </bean>

    <!-- Service Bean -->
    <bean id="sepaMandateService"
          class="com.sast.cis.payment.sepa.service.SepaMandateService">
        <constructor-arg ref="sepaMandateGatewayApiServiceFactory"/>
    </bean>



</beans>
