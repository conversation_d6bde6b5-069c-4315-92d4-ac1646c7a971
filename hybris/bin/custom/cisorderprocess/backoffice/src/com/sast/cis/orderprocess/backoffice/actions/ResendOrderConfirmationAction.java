package com.sast.cis.orderprocess.backoffice.actions;

import com.hybris.cockpitng.actions.ActionContext;
import com.hybris.cockpitng.actions.ActionResult;
import com.hybris.cockpitng.actions.CockpitAction;
import com.sast.cis.core.constants.AwsResource;
import com.sast.cis.core.dto.portal.OrderConfirmationDto;
import com.sast.cis.core.service.aws.MessageQueueService;
import com.sast.cis.core.service.ObjectMapperService;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.model.ModelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

public class ResendOrderConfirmationAction implements CockpitAction<OrderModel, String> {

    private static final Logger LOG = LoggerFactory.getLogger(ResendOrderConfirmationAction.class);

    @Resource
    private Converter<OrderModel, OrderConfirmationDto> orderModelToOrderConfirmationDtoConverter;

    @Resource
    private MessageQueueService messageQueueService;

    @Resource
    private ModelService modelService;

    @Resource
    private ObjectMapperService objectMapperService;

    @Override
    public ActionResult<String> perform(ActionContext<OrderModel> context) {
        OrderModel order = context.getData();
        OrderConfirmationDto orderConfirmation = orderModelToOrderConfirmationDtoConverter.convert(order);
        try {
            String message = objectMapperService.toJsonUnescaped(orderConfirmation);
            messageQueueService.publishMessage(message, AwsResource.SUCCESS_ORDER_TOPIC);
        } catch (Exception e) {
            LOG.warn("Error during JSON processing while resending order confirmation for order with code={}", order.getCode(), e);
            return new ActionResult<>(ActionResult.ERROR, "Error processing the confirmation message");
        }
        order.setStatus(OrderStatus.COMPLETED);
        modelService.save(order);
        return new ActionResult<>(ActionResult.SUCCESS, "Order confirmation message resent.");
    }

    @Override
    public boolean canPerform(ActionContext<OrderModel> context) {
        OrderModel order = context.getData();
        return order != null;
    }

    @Override
    public boolean needsConfirmation(ActionContext<OrderModel> ctx) {
        return true;
    }

    @Override
    public String getConfirmationMessage(ActionContext<OrderModel> ctx) {
        return "Resend confirmation for order?";
    }
}
