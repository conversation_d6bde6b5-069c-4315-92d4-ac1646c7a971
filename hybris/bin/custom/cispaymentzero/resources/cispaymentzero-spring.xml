<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
          http://www.springframework.org/schema/util https://www.springframework.org/schema/util/spring-util.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.sast.cis.payment.zero"/>

    <util:map id="zeroPaymentMethods" key-type="com.sast.cis.core.enums.PaymentMethodType"
              value-type="com.sast.cis.payment.zero.methods.ZeroPaymentMethod">
        <entry value-ref="zeroPaymentMethodImpl">
            <key><value type="com.sast.cis.core.enums.PaymentMethodType">ZERO</value></key>
        </entry>
        <entry value-ref="invoiceBySellerPaymentMethodImpl">
            <key><value type="com.sast.cis.core.enums.PaymentMethodType">INVOICE_BY_SELLER</value></key>
        </entry>
    </util:map>
</beans>
