package com.sast.cis.initialdata.setup;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.setup.InitialDataUpdatePermissionChecker;
import com.sast.cis.initialdata.constants.CisinitialdataConstants;
import de.hybris.platform.commerceservices.dataimport.impl.CoreDataImportService;
import de.hybris.platform.commerceservices.dataimport.impl.SampleDataImportService;
import de.hybris.platform.commerceservices.setup.AbstractSystemSetup;
import de.hybris.platform.commerceservices.setup.data.ImportData;
import de.hybris.platform.commerceservices.setup.events.CoreDataImportedEvent;
import de.hybris.platform.commerceservices.setup.events.SampleDataImportedEvent;
import de.hybris.platform.core.initialization.SystemSetup;
import de.hybris.platform.core.initialization.SystemSetup.Process;
import de.hybris.platform.core.initialization.SystemSetup.Type;
import de.hybris.platform.core.initialization.SystemSetupContext;
import de.hybris.platform.core.initialization.SystemSetupParameter;
import de.hybris.platform.core.initialization.SystemSetupParameterMethod;
import de.hybris.platform.cronjob.model.CronJobModel;
import de.hybris.platform.servicelayer.cronjob.CronJobService;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.sast.cis.core.service.ProductImportService.*;
import static de.hybris.platform.commerceservices.dataimport.impl.CoreDataImportService.IMPORT_CORE_DATA;
import static de.hybris.platform.commerceservices.dataimport.impl.SampleDataImportService.IMPORT_SAMPLE_DATA;

/**
 * This class provides hooks into the system's initialization and update processes.
 */
@SystemSetup(extension = CisinitialdataConstants.EXTENSIONNAME)
@Setter
@Getter
public class InitialDataSystemSetup extends AbstractSystemSetup {

    private static final Logger LOG = LoggerFactory.getLogger(InitialDataSystemSetup.class);

    private static final String ACTIVATE_SOLR_CRON_JOBS = "activateSolrCronJobs";
    private static final String INTEGRATOR = "integrator";
    private static final String IOTSTORE = "iotstore";
    private static final String FULL_INDEX_CRON_JOB = "full-index-cronJob";
    private static final String FULL_BACKOFFICE_INDEX_CRON_JOB = "full-backofficeIndex-cronJob";
    private static final String UPDATE_INDEX_CRON_JOB = "update-index-cronJob";
    private static final String UPDATE_BACKOFFICE_INDEX_CRON_JOB = "update-backofficeIndex-CronJob";

    private CoreDataImportService coreDataImportService;
    private SampleDataImportService cmsDataImportService;
    private SampleDataImportService productImportService;
    private CronJobService cronJobService;
    private ModelService modelService;
    private InitialDataUpdatePermissionChecker initialDataUpdatePermissionChecker;

    /**
     * Generates the Dropdown and Multi-select boxes for the project data import
     */
    @Override
    @SystemSetupParameterMethod
    public List<SystemSetupParameter> getInitializationOptions() {
        final List<SystemSetupParameter> params = new ArrayList<>();

        params.add(createBooleanSystemSetupParameter(IMPORT_CORE_DATA, "Import Core Data", true));
        params.add(createBooleanSystemSetupParameter(IMPORT_SAMPLE_DATA, "Import CMS Data", true));
        params.add(createBooleanSystemSetupParameter(IMPORT_DEMO_PRODUCTS, "Import Demo Products", true));
        params.add(createBooleanSystemSetupParameter(IMPORT_SAMPLE_PRODUCTS, "Import Sample Products", false));
        params.add(createBooleanSystemSetupParameter(ACTIVATE_SOLR_CRON_JOBS, "Activate and run Solr Cron Jobs", true));

        return params;
    }

    @SystemSetup(type = Type.ESSENTIAL, process = Process.ALL)
    public void createEssentialData(final SystemSetupContext context) {
        importImpexFile(context, "/cisinitialdata/import/common/essential-data.impex");
    }

    @SystemSetup(type = Type.PROJECT, process = Process.ALL)
    public void createProjectData(final SystemSetupContext context) {
        if (!initialDataUpdatePermissionChecker.isUpdateAllowed(context)) {
            LOG.warn("Initial data import is not allowed for process '{}'. Skipping import of '{}'.", context.getProcess(),
                    context.getExtensionName());
            return;
        }
        List<ImportData> iotStoreImportData = ImmutableList.of(new ImportData()
            .withProductCatalogName(CIS)
            .withContentCatalogNames(Arrays.asList(INTEGRATOR))
            .withStoreNames(Arrays.asList(IOTSTORE)));

        importAllData(context, iotStoreImportData);

        if (getBooleanSystemSetupParameter(context, ACTIVATE_SOLR_CRON_JOBS)) {
            activateAndRunCronJob(FULL_INDEX_CRON_JOB);
            activateAndRunCronJob(FULL_BACKOFFICE_INDEX_CRON_JOB);
            activateCronJob(UPDATE_INDEX_CRON_JOB);
            activateCronJob(UPDATE_BACKOFFICE_INDEX_CRON_JOB);
        }
    }

    private void importAllData(SystemSetupContext context, List<ImportData> importData) {
        getCoreDataImportService().execute(this, context, importData);
        getEventService().publishEvent(new CoreDataImportedEvent(context, importData));

        getSampleDataImportService().execute(this, context, importData);
        getEventService().publishEvent(new SampleDataImportedEvent(context, importData));

        getProductImportService().execute(this, context, importData);
        getEventService().publishEvent(new SampleDataImportedEvent(context, importData));
    }

    private void activateAndRunCronJob(String jobCode) {
        cronJobService.performCronJob(activateCronJob(jobCode), true);
    }

    private CronJobModel activateCronJob(String jobCode) {
        CronJobModel fullSolrJob = cronJobService.getCronJob(jobCode);
        fullSolrJob.setActive(true);
        modelService.save(fullSolrJob);
        return fullSolrJob;
    }

    public SampleDataImportService getSampleDataImportService() {
        return cmsDataImportService;
    }
}
