#% impex.setLocale( Locale.GERMAN );
#% setAccessibility(true)
#% impex.definitions.put("$currentDateTime", new java.text.SimpleDateFormat("dd.MM.yyyy HH:mm:ss").format(new Date()))

# numerical isocode for NO, used as prefix for product code
$noCc = 047

$productCatalog = aav2ProductCatalog
$classificationCatalog = aaClassificationCatalog
$catalogVersion = catalogversion(catalog(id[default=$productCatalog]), version[default='Staged'])[unique=true, default=$productCatalog:Staged]
$classCatalogVersion = catalogversion(catalog(id[default='aaClassificationCatalog']), version[default='1.0'])[unique=true, default='aaClassificationCatalog:1.0']
$supercategories = source(code, $classCatalogVersion)[unique=true]
$categories = target(code, $catalogVersion)[unique=true]

$clAttrModifiersVariants = system='$classificationCatalog', version='1.0', translator=de.hybris.platform.catalog.jalo.classification.impex.ClassificationAttributeTranslator;

# @formatter:off
$featureHR = @hardware-requirements,100[$clAttrModifiersVariants]; # hardware needed
$featureVT = @vehicle-types,100[$clAttrModifiersVariants]; # vehicle type
# @formatter:on

# Links ClassificationClasses to Categories
INSERT_UPDATE CategoryCategoryRelation; $categories; $supercategories
                                      ; main       ; 100

# when using relation, hybris does not change the modified time stamp of the main category.
# as a result the relation is not synchroized without force.
# this work around will help synchronize supercategories of the main category.
UPDATE Category; code[unique = true]; modifiedtime; $catalogVersion
               ; main               ; "$currentDateTime"

INSERT_UPDATE App; code[unique = true]       ; $featureHR; $catalogVersion;
                 ; AA2_$noCc1687_CSFSA500    ; hw_fsa_500
                 ; AA2_$noCc1687_CSFSA7XX    ; hw_fsa_720,hw_fsa_740,hw_fsa_740_sd,hw_fsa_740_au
                 ; AA2_$noCc1687_DCICRI      ; hw_dci_700,hw_dci_200
                 ; AA2_$noCc1687_DCICRIN     ; hw_dci_700,hw_dci_200
                 ; AA2_$noCc1987_ESIADV      ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350
                 ; AA2_$noCc1987_ESIREPCAT   ;
                 ; AA2_$noCc1987_ESIREPD     ;
                 ; AA2_$noCc1987_ESIREPE     ;
                 ; AA2_$noCc1987_ESIDIAG     ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350
                 ; AA2_$noCc1987_ESIMASTER   ; hw_kts_560,hw_kts_590,hw_kts_465,hw_kts_960,hw_kts_980,hw_kts_995,hw_kts_350
                 ; AA2_$noCc1987_TRKOHW1     ; hw_kts_truck,hw_kts_900_truck
                 ; AA2_$noCc1987_TRKOHW2     ; hw_kts_truck,hw_kts_900_truck
                 ; AA2_$noCc1987_TRKTRUCK    ; hw_kts_truck,hw_kts_900_truck
                 ; AA2_$noCc1987_TSTINFOAW   ;
                 ; AA2_$noCc1687_TSTINFODAT  ;
                 ; AA2_$noCc1987_THLPKW      ;
                 ; AA2_$noCc1687_CORE_ESIPKG ;
                 ; AA2_$noCc1987_KTS250SD    ; hw_kts_250

INSERT_UPDATE App; code[unique = true]       ; $featureVT; $catalogVersion;
                 ; AA2_$noCc1687_CSFSA500    ;
                 ; AA2_$noCc1687_CSFSA7XX    ;
                 ; AA2_$noCc1687_DCICRI      ; vt_pkw
                 ; AA2_$noCc1687_DCICRIN     ; vt_pkw
                 ; AA2_$noCc1987_ESIADV      ; vt_pkw
                 ; AA2_$noCc1987_ESIREPCAT   ; vt_pkw
                 ; AA2_$noCc1987_ESIREPD     ; vt_pkw
                 ; AA2_$noCc1987_ESIREPE     ; vt_pkw
                 ; AA2_$noCc1987_ESIDIAG     ; vt_pkw
                 ; AA2_$noCc1987_ESIMASTER   ; vt_pkw
                 ; AA2_$noCc1987_TRKOHW1     ; vt_agricultural_machines
                 ; AA2_$noCc1987_TRKOHW2     ; vt_construction_machine_engines
                 ; AA2_$noCc1987_TRKTRUCK    ; vt_truck
                 ; AA2_$noCc1987_TSTINFOAW   ; vt_pkw
                 ; AA2_$noCc1687_TSTINFODAT  ;
                 ; AA2_$noCc1987_THLPKW      ; vt_pkw
                 ; AA2_$noCc1687_CORE_ESIPKG ; vt_pkw
                 ; AA2_$noCc1987_KTS250SD    ; vt_pkw
