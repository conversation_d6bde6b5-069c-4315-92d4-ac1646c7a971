#
# Import the Solr configuration for the store
#

# Index Type
$solrIndexedType = aaAppType

# Language
$lang = de

UPDATE SolrSort; indexedType(identifier)[unique = true]; code[unique = true]; name[lang = $lang]
               ; $solrIndexedType                      ; name-asc           ; "Name (aufsteigend)"
               ; $solrIndexedType                      ; name-desc          ; "Name (absteigend)"
               ; $solrIndexedType                      ; price-asc          ; "Preis (aufsteigend)"
               ; $solrIndexedType                      ; price-desc         ; "Preis (absteigend)"
