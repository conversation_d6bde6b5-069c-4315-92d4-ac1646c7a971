package com.sast.cis.shop.frontend.search;

import com.sast.cis.core.data.CategoryPageData;
import com.sast.cis.core.i18n.FallbackCountryProvider;
import com.sast.cis.core.i18n.I18nSessionAttributesReader;
import com.sast.cis.core.productsearch.ShopProductService;
import com.sast.cis.core.productsearch.dto.ProductSearchQuery;
import de.hybris.platform.commercefacades.search.data.SearchFilterQueryData;
import de.hybris.platform.commerceservices.search.solrfacetsearch.data.FilterQueryOperator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.sast.cis.core.productsearch.dto.ProductFacetIndex.COUNTRIES;
import static com.sast.cis.core.productsearch.dto.ProductFacetIndex.LICENSE_TYPES;
import static com.sast.cis.shop.frontend.constants.CisshopfrontendConstants.CATEGORY_PAGE_SIZE_DEFAULT;
import static java.util.List.of;

@Component
@RequiredArgsConstructor
@Slf4j
public class ShopAppSearchFacade {

    private final PropertiesConfiguration shopFrontendProperties;
    private final ShopProductService shopProductService;
    private final I18nSessionAttributesReader i18nSessionAttributesReader;
    private final FallbackCountryProvider fallbackCountryProvider;

    /**
     * Searches for 'App' Products, based on the query and filters submitted in the 'ProductSearchQuery' search query.
     * 'App' products are products that have licenses of types 'Full', 'Subscription' and 'Evaluation'.
     * Additionally products that have no license are also considered 'App' products.
     * 'App' products are obtained by using a negative filter. All products that do not have a 'Tool' license are 'App' products.
     *
     * @return 'App' products.
     */
    public CategoryPageData getApps(final ProductSearchQuery searchInput) {
        if (searchInput.getPageSize() == 0) {
            searchInput.setPageSize(shopFrontendProperties.getInt(CATEGORY_PAGE_SIZE_DEFAULT));
        }
        final List<SearchFilterQueryData> searchFilterQueries = of(
            getLicenseTypeFilterQuery(),
            getSelectedCountryFilterQuery()
        );
        return shopProductService.getProducts(
            searchInput,
            searchFilterQueries
        );
    }

    private SearchFilterQueryData getLicenseTypeFilterQuery() {
        return new SearchFilterQueryData()
            .withKey(LICENSE_TYPES.getCode())
            .withValues(Set.of("NOT TOOL"))
            .withOperator(FilterQueryOperator.OR);
    }

    private SearchFilterQueryData getSelectedCountryFilterQuery() {
        final String selectedCountry = Optional.ofNullable(i18nSessionAttributesReader.getUserSelectedCountry())
            .orElse(fallbackCountryProvider.getCountry());
        return new SearchFilterQueryData()
            .withKey(COUNTRIES.getCode())
            .withValues(Set.of(selectedCountry))
            .withOperator(FilterQueryOperator.OR);
    }
}
