<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<extensioninfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="extensioninfo.xsd">
    <extension abstractclassprefix="Generated" classprefix="Cishealth" jaloLogicFree="true" managername="CishealthManager"
               managersuperclass="de.hybris.platform.jalo.extension.Extension" name="cishealth" usemaven="true">
                               
        <requires-extension name="webservicescommons"/>
        <requires-extension name="ciscore"/>

        <coremodule generated="true" manager="com.sast.cis.health.jalo.CishealthManager" packageroot="com.sast.cis.health"/>

        <webmodule jspcompile="false" webroot="/health"/>
    </extension>
</extensioninfo>
