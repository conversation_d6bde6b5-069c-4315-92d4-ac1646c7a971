package com.sast.cis.brim.converter.productexport;

import com.sast.cis.brim.converter.productexport.rate.RateFactory;
import com.sast.cis.core.model.AppLicenseModel;
import com.securityandsafetythings.billing.brim.model.product.v1.Charge;
import com.securityandsafetythings.billing.brim.model.product.v1.Rate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
public class ExpiredPriceDiscardingChargePlanFactory extends ChargePlanFactory {
    public ExpiredPriceDiscardingChargePlanFactory(RateFactory expiredPriceDiscardingRateFactory) {
        super(expiredPriceDiscardingRateFactory);
    }
}
