package com.sast.cis.brim.http.impl;

import com.sast.cis.brim.exception.BrimConfigurationException;
import com.sast.cis.brim.http.BrimClientFactory;
import com.sast.cis.brim.http.BrimWebTargetFactory;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.ws.rs.client.WebTarget;

@Service
public class BrimWebTargetFactoryImpl implements BrimWebTargetFactory {
    private static final String CONFIG_INVOICE_BASE_URL = "brim.http.invoiceFile.baseUrl";
    private static final String CONFIG_PRODUCT_UPDATE_BASE_URL = "brim.http.product.update.baseUrl";

    private final BrimClientFactory brimClientFactory;
    private final ConfigurationService configurationService;

    public BrimWebTargetFactoryImpl(BrimClientFactory brimClientFactory,
        ConfigurationService configurationService) {
        this.brimClientFactory = brimClientFactory;
        this.configurationService = configurationService;
    }

    @Override
    public WebTarget createInvoiceTarget() {
        return createTarget(CONFIG_INVOICE_BASE_URL);
    }

    @Override
    public WebTarget createProductUpdateTarget() {
        return createTarget(CONFIG_PRODUCT_UPDATE_BASE_URL);
    }

    private WebTarget createTarget(String baseUrl){
        return brimClientFactory.createClient()
            .target(getConfigurationValue(baseUrl));
    }

    private String getConfigurationValue(String key) {
        String configurationValue = configurationService.getConfiguration().getString(key);
        if (StringUtils.isBlank(configurationValue)) {
            throw new BrimConfigurationException(String.format("Provided configuration value for '%s' is blank.", key));
        }
        return configurationValue;
    }
}
