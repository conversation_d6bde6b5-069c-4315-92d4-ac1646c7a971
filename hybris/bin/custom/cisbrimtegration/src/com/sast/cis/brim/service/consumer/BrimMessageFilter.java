package com.sast.cis.brim.service.consumer;

import lombok.NonNull;

/**
 * Implementations of this interface determine whether a given message should be processed
 *
 * @param <T> applicable message type
 */
public interface BrimMessageFilter<T> {
    /**
     * Determines whether a given message should be silently ignored.
     * An ignored message must not be treated as error by the caller
     *
     * @param message message to check
     * @return whether a message should be silently ignored
     */
    boolean shouldIgnoreMessage(@NonNull T message);
}
