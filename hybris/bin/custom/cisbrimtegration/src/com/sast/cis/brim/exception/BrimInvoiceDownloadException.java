package com.sast.cis.brim.exception;

public class BrimInvoiceDownloadException extends BrimException {
    public BrimInvoiceDownloadException() {
    }

    public BrimInvoiceDownloadException(String message) {
        super(message);
    }

    public BrimInvoiceDownloadException(String message, Throwable cause) {
        super(message, cause);
    }

    public BrimInvoiceDownloadException(Throwable cause) {
        super(cause);
    }

    public BrimInvoiceDownloadException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
