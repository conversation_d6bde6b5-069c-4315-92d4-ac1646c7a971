package com.sast.cis.brim.dao;

import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.test.utils.SampleDataCreator;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.model.ModelService;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Optional;

import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.test.utils.TestDataConstants.AA2_PRODUCT_CATALOG;
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_DEVELOPER_UID;
import static com.sast.cis.test.utils.TestDataConstants.AA_PRODUCT_CATALOG;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class BrimAppLicenseDaoITest extends ServicelayerTransactionalTest {

    @Resource
    private ModelService modelService;

    @Resource
    private DeveloperService developerService;

    @Resource
    private BrimAppLicenseDao brimAppLicenseDao;

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();

    private AppLicenseModel oldCatalogAppLicense;
    private AppLicenseModel newCatalogAppLicense;

    @Before
    public void setUp() {
        var developer = developerService.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID);
        var app1 = sampleDataCreator.createApp("AA_1234", "aa.ITest", developer, AA_PRODUCT_CATALOG, STAGED, APPROVED);
        var app2 = sampleDataCreator.createApp("AA2_1234", "aa2.ITest", developer, AA2_PRODUCT_CATALOG, STAGED, APPROVED);
        oldCatalogAppLicense = sampleDataCreator.createFullAppLicense(app1);
        oldCatalogAppLicense.setBrimId(oldCatalogAppLicense.getCode());
        newCatalogAppLicense = sampleDataCreator.createFullAppLicense(app2);
        newCatalogAppLicense.setBrimId(newCatalogAppLicense.getCode());
        modelService.saveAll(oldCatalogAppLicense, newCatalogAppLicense);
    }

    @Test
    public void getAppLicenseCodeForBrimId_thenReturn() {

        Optional<String> oldLicenseCode = brimAppLicenseDao.getAppLicenseCodeForBrimId(oldCatalogAppLicense.getBrimId(),
            STAGED.getVersionName());
        assertThat(oldLicenseCode).isPresent();
        assertThat(oldLicenseCode).contains(oldCatalogAppLicense.getCode());

        Optional<String> newLicenseCode = brimAppLicenseDao.getAppLicenseCodeForBrimId(newCatalogAppLicense.getBrimId(),
            STAGED.getVersionName());
        assertThat(newLicenseCode).isPresent();
        assertThat(newLicenseCode).contains(newCatalogAppLicense.getCode());
    }
}
