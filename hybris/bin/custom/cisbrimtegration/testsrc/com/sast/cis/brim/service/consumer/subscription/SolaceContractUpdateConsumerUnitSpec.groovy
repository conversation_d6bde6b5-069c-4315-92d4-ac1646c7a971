package com.sast.cis.brim.service.consumer.subscription

import com.sast.cis.brim.enums.BrimEventType
import com.sast.cis.brim.service.consumer.BrimJmsMessageConsumer
import com.sast.cis.brim.service.consumer.SolaceContractUpdateEventConsumer
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test
import org.springframework.messaging.Message

@UnitTest
class SolaceContractUpdateConsumerUnitSpec extends JUnitPlatformSpecification {
    private BrimJmsMessageConsumer brimMessageConsumer = Mock()

    private SolaceContractUpdateEventConsumer solaceContractUpdateEventConsumer

    Message<String> mockMessage = Mock(Message)

    def setup() {
        solaceContractUpdateEventConsumer = new SolaceContractUpdateEventConsumer(brimMessageConsumer)
    }

    @Test
    def 'message is passed to processor'() {
        when:
        solaceContractUpdateEventConsumer.handle(mockMessage)

        then:
        1 * brimMessageConsumer.consume(mockMessage, BrimEventType.CONTRACT_UPDATED)
    }

    @Test
    def 'exception thrown by message processor is not caught'() {
        when:
        solaceContractUpdateEventConsumer.handle(mockMessage)

        then:
        1 * brimMessageConsumer.consume(mockMessage, BrimEventType.CONTRACT_UPDATED) >> { throw new RuntimeException() }
        thrown(RuntimeException)
    }
}
