<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright (c) 2020 SAP SE or an SAP affiliate company. All rights reserved.
-->
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<context:annotation-config/>
	<context:component-scan base-package="com.sast.cis.dmp.integration"/>

	<alias name="cisTrialExtensionRequestReverseConverter" alias="trialExtensionRequestReverseConverter"/>
	<bean id="cisTrialExtensionRequestReverseConverter" parent="abstractPopulatingConverter">
		<property name="targetClass" value="com.sast.cis.trialextension.model.TrialExtensionRequestModel"/>
		<property name="populators">
			<list>
				<ref bean="trialExtensionRequestReversePopulator"/>
			</list>
		</property>
	</bean>
</beans>
